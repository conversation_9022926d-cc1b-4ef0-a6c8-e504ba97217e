/* TradingChart.css */
.trading-chart-container {
  width: 100%;
  background-color: #000000;
  color: #d1d4dc;
  border-radius: 0;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  position: relative;
}

/* Toolbar left section with search and timeframes */
.toolbar-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

/* Professional symbol search container */
.chart-search-container {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(0, 231, 182, 0.2);
  border-radius: 8px;
  padding: 8px 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(0, 231, 182, 0.1);
}

.current-symbol-display {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 60px;
}

.symbol-text {
  font-size: 16px;
  font-weight: 700;
  color: #00e7b6;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: 0.5px;
  text-shadow: 0 0 8px rgba(0, 231, 182, 0.3);
}

.symbol-type {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: -2px;
}

.search-input-wrapper {
  position: relative;
  flex: 1;
  max-width: 280px;
}

.search-input {
  width: 100%;
  padding: 8px 35px 8px 45px;
  background: linear-gradient(to right, rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.4));
  border: 1px solid rgba(0, 231, 182, 0.3);
  border-radius: 20px;
  color: #ffffff;
  font-size: 13px;
  font-weight: 500;
  letter-spacing: 0.3px;
  box-shadow: inset 0 2px 5px rgba(0, 0, 0, 0.3),
              0 0 15px rgba(0, 231, 182, 0.15);
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: rgba(0, 231, 182, 0.7);
  box-shadow: 0 0 25px rgba(0, 231, 182, 0.25),
              inset 0 2px 5px rgba(0, 0, 0, 0.3);
  background: linear-gradient(to right, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.5));
}

.search-input::placeholder {
  color: rgba(0, 231, 182, 0.8);
  opacity: 0.9;
  font-weight: 500;
}

.symbol-search-input {
  width: 100%;
  height: 32px;
  background: transparent;
  border: none;
  color: rgba(255, 255, 255, 0.9);
  font-size: 13px;
  padding: 0 30px 0 8px;
  outline: none;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 500;
}

.symbol-search-input:focus {
  color: #ffffff;
}

.symbol-search-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
  font-weight: 400;
}

.search-loading-icon {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(0, 231, 182, 0.9);
  font-size: 14px;
  animation: spin 1s linear infinite;
  filter: drop-shadow(0 0 6px rgba(0, 231, 182, 0.6));
}

.aura-logo {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.loading-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: rgba(0, 231, 182, 0.9);
  font-size: 16px;
  animation: spin 1s linear infinite;
  filter: drop-shadow(0 0 8px rgba(0, 231, 182, 0.6));
  z-index: 3;
}

@keyframes spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}

.error-message {
  padding: 10px 16px;
  background-color: rgba(255, 82, 82, 0.08);
  color: #ff5252;
  font-size: 14px;
  text-align: center;
  border-bottom: 1px solid rgba(255, 82, 82, 0.15);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
  font-weight: 500;
}

/* Top toolbar */
.chart-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-bottom: 1px solid rgba(0, 231, 182, 0.08);
  height: 48px;
  background-color: rgba(0, 0, 0, 0.95);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(0, 231, 182, 0.03);
  position: relative;
  z-index: 10;
  backdrop-filter: blur(5px);
}

/* Timeframe selector styling */
.timeframe-selector {
  display: flex;
  gap: 8px;
}

.timeframe-selector button {
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 231, 182, 0.2);
  color: #787b86;
  padding: 4px 10px;
  border-radius: 15px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.03);
}

.timeframe-selector button:hover {
  background-color: rgba(0, 231, 182, 0.08);
  color: #d1d4dc;
  border-color: rgba(0, 231, 182, 0.3);
  box-shadow: 0 0 10px rgba(0, 231, 182, 0.15);
}

.timeframe-selector button.active {
  background-color: rgba(0, 231, 182, 0.15);
  color: #00e7b6;
  border-color: rgba(0, 231, 182, 0.4);
  box-shadow: 0 0 15px rgba(0, 231, 182, 0.2), inset 0 1px 0 rgba(0, 231, 182, 0.1);
  text-shadow: 0 0 10px rgba(0, 231, 182, 0.4);
}

/* Chart controls and filters */
.chart-controls {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-left: auto;
}

.chart-button {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 231, 182, 0.15);
  color: #787b86;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  font-weight: 500;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05), 0 1px 3px rgba(0, 0, 0, 0.2);
  letter-spacing: -0.02em;
  height: 28px;
  position: relative;
  overflow: hidden;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
}

.chart-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, rgba(0, 231, 182, 0), rgba(0, 231, 182, 0.3), rgba(0, 231, 182, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chart-button:hover::before {
  opacity: 1;
}

.chart-button svg {
  font-size: 12px;
  filter: drop-shadow(0 0 1px rgba(0, 231, 182, 0.3));
}

/* Filters dropdown styling */
.filters-dropdown {
  position: relative;
  display: inline-block;
}

.filters-button {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(0, 231, 182, 0.15);
  color: #00e7b6;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.2s ease;
  font-weight: 500;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.05), 0 1px 3px rgba(0, 0, 0, 0.2);
  letter-spacing: -0.02em;
  height: 28px;
  position: relative;
  overflow: hidden;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
}

.filters-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, rgba(0, 231, 182, 0), rgba(0, 231, 182, 0.3), rgba(0, 231, 182, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.filters-button:hover::before {
  opacity: 1;
}

.filters-button svg {
  font-size: 16px;
  filter: drop-shadow(0 0 1px rgba(0, 231, 182, 0.3));
}

.filters-content {
  display: none;
  position: absolute;
  right: 0;
  top: 100%;
  margin-top: 5px;
  background-color: rgba(0, 0, 0, 0.95);
  min-width: 200px;
  border-radius: 8px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(0, 231, 182, 0.1);
  z-index: 20;
  overflow: hidden;
  padding: 12px;
}

.filters-dropdown:hover .filters-content,
.filters-content:hover {
  display: block;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}

.filters-title {
  font-size: 12px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 12px;
  letter-spacing: -0.02em;
  text-transform: uppercase;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 231, 182, 0.1);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
}

.filters-description {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 15px;
  line-height: 1.4;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;
}

.filter-options-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.filter-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 10px;
  color: #d1d4dc;
  font-size: 12px;
  transition: all 0.2s ease;
  cursor: pointer;
  border-radius: 6px;
  background-color: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(0, 231, 182, 0.05);
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;
}

.filter-option:hover {
  background-color: rgba(0, 231, 182, 0.05);
  border-color: rgba(0, 231, 182, 0.1);
}

.filter-option.active {
  background-color: rgba(0, 231, 182, 0.08);
  border-color: rgba(0, 231, 182, 0.15);
  box-shadow: 0 0 10px rgba(0, 231, 182, 0.1);
}

.filter-option-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.filter-option-icon {
  color: rgba(0, 231, 182, 0.7);
  font-size: 14px;
  filter: drop-shadow(0 0 2px rgba(0, 231, 182, 0.3));
  transition: all 0.2s ease;
}

.filter-option.active .filter-option-icon {
  color: #00e7b6;
  filter: drop-shadow(0 0 3px rgba(0, 231, 182, 0.5));
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 36px;
  height: 20px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(0, 231, 182, 0.2);
  transition: .3s;
  border-radius: 20px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 14px;
  width: 14px;
  left: 2px;
  bottom: 2px;
  background-color: rgba(255, 255, 255, 0.8);
  transition: .3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: rgba(0, 231, 182, 0.2);
  border-color: rgba(0, 231, 182, 0.4);
}

input:checked + .toggle-slider:before {
  transform: translateX(16px);
  background-color: #00e7b6;
  box-shadow: 0 0 8px rgba(0, 231, 182, 0.5);
}

.chart-button:hover {
  background-color: rgba(0, 231, 182, 0.05);
  color: #d1d4dc;
  border-color: rgba(0, 231, 182, 0.25);
  box-shadow: 0 0 15px rgba(0, 231, 182, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.chart-button.active {
  background-color: rgba(0, 231, 182, 0.08);
  color: #00e7b6;
  border-color: rgba(0, 231, 182, 0.3);
  box-shadow: 0 0 20px rgba(0, 231, 182, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.chart-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 15%;
  right: 15%;
  height: 1px;
  background: linear-gradient(90deg, rgba(0, 231, 182, 0), rgba(0, 231, 182, 0.5), rgba(0, 231, 182, 0));
}

.filters-button:hover {
  background-color: rgba(0, 231, 182, 0.05);
  color: #00e7b6;
  border-color: rgba(0, 231, 182, 0.25);
  box-shadow: 0 0 15px rgba(0, 231, 182, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.filters-button.active {
  background-color: rgba(0, 231, 182, 0.08);
  color: #00e7b6;
  border-color: rgba(0, 231, 182, 0.3);
  box-shadow: 0 0 20px rgba(0, 231, 182, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.filters-button.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 15%;
  right: 15%;
  height: 1px;
  background: linear-gradient(90deg, rgba(0, 231, 182, 0), rgba(0, 231, 182, 0.5), rgba(0, 231, 182, 0));
}

.chart-controls-divider {
  width: 1px;
  height: 20px;
  background: linear-gradient(to bottom, rgba(0, 231, 182, 0.01), rgba(0, 231, 182, 0.15), rgba(0, 231, 182, 0.01));
  margin: 0 8px;
  opacity: 0.5;
}

.dropdown-arrow {
  font-size: 8px;
  margin-left: 3px;
  color: rgba(0, 231, 182, 0.7);
}

.chart-controls {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-left: auto;
  height: 100%;
  padding: 0 5px;
}

/* Chart content area */
.chart-content {
  display: flex;
  position: relative;
}

/* Chart content area with no sidebar */
.chart-content {
  display: flex;
  position: relative;
  width: 100%;
}

/* Delete mode styling */
.chart-container.delete-mode {
  cursor: crosshair !important;
}

.chart-container.delete-mode * {
  cursor: crosshair !important;
}

/* Drawing Tools Sidebar */
.drawing-tools-sidebar {
  width: 52px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.98) 0%, rgba(0, 0, 0, 0.95) 100%);
  border-right: 1px solid rgba(0, 231, 182, 0.15);
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 10;
  box-shadow: 2px 0 15px rgba(0, 0, 0, 0.4), inset -1px 0 0 rgba(0, 231, 182, 0.05);
  backdrop-filter: blur(10px);
}

.drawing-tools-header {
  padding: 8px 4px;
  border-bottom: 1px solid rgba(0, 231, 182, 0.1);
  background-color: rgba(0, 0, 0, 0.8);
  text-align: center;
}

.drawing-tools-header h3 {
  color: #00e7b6;
  font-size: 10px;
  font-weight: 600;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.close-button {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 16px;
  cursor: pointer;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  transition: all 0.2s ease;
}

.close-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.drawing-tools-content {
  flex: 1;
  padding: 8px 4px;
  overflow-y: auto;
}

.tool-category {
  margin-bottom: 16px;
}

.category-header {
  font-size: 8px;
  color: rgba(0, 231, 182, 0.7);
  font-weight: 600;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  padding: 0 4px;
  text-align: center;
}

.tool-grid {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.tool-button {
  width: 44px;
  height: 44px;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.6) 100%);
  border: 1px solid rgba(0, 231, 182, 0.12);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  margin: 0 auto;
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.06),
    0 2px 4px rgba(0, 0, 0, 0.4),
    0 0 0 0 rgba(0, 231, 182, 0);
  backdrop-filter: blur(8px);
}

.tool-button:hover {
  background: linear-gradient(135deg, rgba(0, 231, 182, 0.15) 0%, rgba(0, 231, 182, 0.08) 100%);
  border-color: rgba(0, 231, 182, 0.4);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.1),
    0 4px 12px rgba(0, 0, 0, 0.5),
    0 0 0 2px rgba(0, 231, 182, 0.15);
  transform: translateY(-1px) scale(1.02);
}

.tool-button.active {
  background: linear-gradient(135deg, rgba(0, 231, 182, 0.25) 0%, rgba(0, 231, 182, 0.15) 100%);
  border-color: rgba(0, 231, 182, 0.6);
  box-shadow:
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    0 6px 16px rgba(0, 0, 0, 0.6),
    0 0 0 3px rgba(0, 231, 182, 0.25),
    0 0 20px rgba(0, 231, 182, 0.3);
  transform: translateY(-2px) scale(1.05);
}

.tool-icon {
  color: rgba(0, 231, 182, 0.85);
  font-size: 18px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 0 2px rgba(0, 231, 182, 0.2));
}

.tool-icon-text {
  color: rgba(0, 231, 182, 0.85);
  font-size: 18px;
  font-weight: 600;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 0 2px rgba(0, 231, 182, 0.2));
  text-shadow: 0 0 4px rgba(0, 231, 182, 0.3);
}

.tool-icon-image {
  width: 20px;
  height: 20px;
  filter: brightness(0) saturate(100%) invert(85%) sepia(15%) saturate(1000%) hue-rotate(120deg) brightness(95%) contrast(95%) drop-shadow(0 0 2px rgba(0, 231, 182, 0.2));
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tool-button:hover .tool-icon,
.tool-button:hover .tool-icon-text {
  color: #00e7b6;
  filter: drop-shadow(0 0 6px rgba(0, 231, 182, 0.5));
  text-shadow: 0 0 8px rgba(0, 231, 182, 0.4);
  transform: scale(1.1);
}

.tool-button:hover .tool-icon-image {
  filter: brightness(0) saturate(100%) invert(85%) sepia(15%) saturate(1000%) hue-rotate(120deg) brightness(115%) contrast(115%) drop-shadow(0 0 6px rgba(0, 231, 182, 0.5));
  transform: scale(1.1);
}

.tool-button.active .tool-icon,
.tool-button.active .tool-icon-text {
  color: #00e7b6;
  filter: drop-shadow(0 0 10px rgba(0, 231, 182, 0.8));
  text-shadow: 0 0 12px rgba(0, 231, 182, 0.6);
  transform: scale(1.15);
}

.tool-button.active .tool-icon-image {
  filter: brightness(0) saturate(100%) invert(85%) sepia(15%) saturate(1000%) hue-rotate(120deg) brightness(125%) contrast(125%) drop-shadow(0 0 10px rgba(0, 231, 182, 0.8));
  transform: scale(1.15);
}

/* Tooltip for tool buttons */
.tool-button::after {
  content: attr(title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgba(0, 0, 0, 0.9);
  color: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  margin-left: 8px;
  border: 1px solid rgba(0, 231, 182, 0.2);
  z-index: 1000;
}

.tool-button:hover::after {
  opacity: 1;
}

/* Adjust chart area when sidebar is open */
.chart-content .chart-area {
  flex: 1;
}

/* Drawing mode cursor - improved */
.chart-container.drawing-mode {
  cursor: crosshair !important;
  position: relative;
}

.chart-container.drawing-mode * {
  cursor: crosshair !important;
}

/* Improved drawing mode overlay for better interaction */
.chart-container.drawing-mode::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  pointer-events: none;
  border: 2px solid rgba(0, 231, 182, 0.3);
  border-radius: 4px;
  animation: drawing-mode-pulse 2s infinite;
}

@keyframes drawing-mode-pulse {
  0%, 100% {
    border-color: rgba(0, 231, 182, 0.2);
    box-shadow: inset 0 0 20px rgba(0, 231, 182, 0.1);
  }
  50% {
    border-color: rgba(0, 231, 182, 0.4);
    box-shadow: inset 0 0 30px rgba(0, 231, 182, 0.2);
  }
}

.drawing-instruction {
  color: rgba(0, 231, 182, 0.8);
  font-style: italic;
  font-size: 11px;
}

/* Drawing counter badge */
.drawings-counter {
  background-color: #00e7b6;
  color: #000000;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  font-size: 9px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 8px rgba(0, 231, 182, 0.5);
  animation: pulse-badge 2s infinite;
  margin-top: 2px;
}

.drawing-count {
  position: absolute;
  top: -6px;
  right: -6px;
  background-color: #00e7b6;
  color: #000000;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 10px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 8px rgba(0, 231, 182, 0.5);
  animation: pulse-badge 2s infinite;
}

@keyframes pulse-badge {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 8px rgba(0, 231, 182, 0.5);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 12px rgba(0, 231, 182, 0.8);
  }
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .drawing-tools-sidebar {
    width: 50px;
  }

  .tool-button {
    width: 42px;
    height: 42px;
  }

  .tool-icon,
  .tool-icon-text {
    font-size: 14px;
  }

  .tool-icon-image {
    width: 14px;
    height: 14px;
  }

  .tool-button::after {
    display: none; /* Hide tooltips on mobile */
  }
}

/* Compact mode for smaller screens */
@media (max-width: 480px) {
  .drawing-tools-sidebar {
    width: 44px;
  }

  .tool-button {
    width: 36px;
    height: 36px;
  }

  .tool-icon,
  .tool-icon-text {
    font-size: 12px;
  }

  .tool-icon-image {
    width: 12px;
    height: 12px;
  }

  .drawing-tools-content {
    padding: 4px 2px;
  }

  .tool-category {
    margin-bottom: 8px;
  }
}

.chart-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #000000;
}

.chart-header {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: 10px 16px;
  border-bottom: 1px solid rgba(0, 231, 182, 0.1);
  font-size: 13px;
  background-color: #000000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(0, 231, 182, 0.05);
  position: relative;
  z-index: 5;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Drawing Status Indicator */
.drawing-status {
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: rgba(0, 231, 182, 0.1);
  border: 1px solid rgba(0, 231, 182, 0.3);
  border-radius: 6px;
  padding: 6px 12px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: 0 0 8px rgba(0, 231, 182, 0.3);
  }
  50% {
    box-shadow: 0 0 16px rgba(0, 231, 182, 0.5);
  }
}

.drawing-status-text {
  color: #00e7b6;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.3px;
}

.cancel-drawing {
  background: rgba(255, 82, 82, 0.2);
  border: 1px solid rgba(255, 82, 82, 0.4);
  color: #ff5252;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.cancel-drawing:hover {
  background: rgba(255, 82, 82, 0.3);
  border-color: rgba(255, 82, 82, 0.6);
  box-shadow: 0 0 8px rgba(255, 82, 82, 0.3);
}

/* Keyboard Shortcuts Panel */
.shortcuts-panel {
  position: absolute;
  left: 100%;
  top: 0;
  width: 200px;
  background-color: rgba(0, 0, 0, 0.95);
  border: 1px solid rgba(0, 231, 182, 0.2);
  border-radius: 8px;
  padding: 12px;
  margin-left: 8px;
  z-index: 1000;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
}

.shortcuts-header {
  color: #00e7b6;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 1px solid rgba(0, 231, 182, 0.2);
  padding-bottom: 6px;
}

.shortcuts-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.shortcut-key {
  background-color: rgba(0, 231, 182, 0.1);
  border: 1px solid rgba(0, 231, 182, 0.3);
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 10px;
  font-weight: 600;
  color: #00e7b6;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  min-width: 45px;
  text-align: center;
}

.shortcut-desc {
  color: rgba(255, 255, 255, 0.8);
  font-size: 11px;
  flex: 1;
  text-align: right;
}

/* Hide shortcuts panel on mobile */
@media (max-width: 768px) {
  .shortcuts-panel {
    display: none;
  }
}

.symbol-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.symbol {
  font-weight: 600;
  color: #ffffff;
  letter-spacing: -0.02em;
  font-size: 15px;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
}

.price-info {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-right: 30px;
  position: relative;
}

.price-info::after {
  content: '';
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  height: 20px;
  width: 1px;
  background: linear-gradient(to bottom, rgba(0, 231, 182, 0.01), rgba(0, 231, 182, 0.2), rgba(0, 231, 182, 0.01));
}

.price-label {
  color: #787b86;
  font-size: 14px;
  font-weight: 500;
  margin-right: 5px;
}

.price-value {
  color: #d1d4dc;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  letter-spacing: -0.02em;
  font-weight: 500;
  font-size: 14px;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.1);
}

.volume-info {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(0, 0, 0, 0.3);
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid rgba(0, 231, 182, 0.1);
  margin-left: 8px;
}

.volume-label {
  color: #787b86;
  font-size: 10px;
  font-weight: 500;
  margin-right: 2px;
}

.volume-value {
  color: #d1d4dc;
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 500;
  letter-spacing: -0.02em;
  font-size: 10px;
}

.volume-change {
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 9px;
  font-weight: 600;
  letter-spacing: 0px;
  margin-left: 2px;
}

.volume-change.positive {
  color: #00e7b6;
  background-color: rgba(0, 231, 182, 0.1);
  border: 1px solid rgba(0, 231, 182, 0.2);
  text-shadow: 0 0 5px rgba(0, 231, 182, 0.3);
}

.volume-change.negative {
  color: #ffffff;
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

.chart-container {
  flex: 1;
  height: 650px;
  background-color: #000000;
  position: relative;
}

/* Add glow effect to chart candles */
.chart-container::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  box-shadow: inset 0 0 40px rgba(0, 231, 182, 0.2);
  z-index: 2;
  mix-blend-mode: screen;
}

/* Hide TradingView watermark */
.chart-container .tv-lightweight-charts > div > div > div:last-child,
.chart-container div[id^="tradingview_"] > div > div > div:last-child,
.chart-container canvas + div,
.chart-container div[class*="watermark"],
.chart-container div[class*="attribution"] {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
  pointer-events: none !important;
}

/* Add additional glow for candles */
.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  background: radial-gradient(
    circle at center,
    rgba(0, 231, 182, 0.05) 0%,
    rgba(0, 0, 0, 0) 70%
  );
  z-index: 1;
}

/* Make candles glow */
.chart-container svg path {
  filter: drop-shadow(0 0 3px rgba(0, 231, 182, 0.5));
}

/* Target green candles for stronger glow */
.chart-container svg path[fill="#00e7b6"] {
  filter: drop-shadow(0 0 5px rgba(0, 231, 182, 0.7));
}

/* Target white candles for subtle glow */
.chart-container svg path[fill="#ffffff"] {
  filter: drop-shadow(0 0 4px rgba(255, 255, 255, 0.5));
}

/* Style chart axis text */
.chart-container text {
  font-family: 'Roboto Mono', monospace !important;
  font-size: 10px !important;
  font-weight: 500 !important;
  fill: rgba(0, 231, 182, 0.7) !important;
  text-shadow: 0 0 5px rgba(0, 231, 182, 0.3);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .future-header h1 {
    font-size: 28px;
  }

  .chart-toolbar {
    flex-wrap: wrap;
    height: auto;
    padding: 15px;
    gap: 15px;
  }

  .toolbar-left {
    width: 100%;
    order: 1;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .chart-search-bar {
    width: 60%;
  }

  .timeframe-selector {
    width: auto;
  }

  .chart-controls {
    order: 2;
    margin: 0;
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .future-header h1 {
    font-size: 24px;
  }

  .toolbar-left {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .chart-search-bar {
    width: 100%;
  }

  .timeframe-selector {
    width: 100%;
    justify-content: flex-start;
  }

  .chart-button span,
  .filters-button span {
    display: none;
  }

  .chart-header {
    flex-direction: column;
    gap: 15px;
    padding: 15px;
  }

  .symbol-info, .price-info, .volume-info {
    margin-right: 0;
    width: 100%;
  }

  .price-info {
    justify-content: space-between;
  }

  .price-info::after {
    display: none;
  }

  .side-toolbar {
    padding: 8px 4px;
  }

  .side-button {
    width: 32px;
    height: 32px;
    margin-bottom: 12px;
  }

  .chart-container {
    height: 550px;
  }
}

@media (max-width: 480px) {
  .future-header h1 {
    font-size: 20px;
  }

  .timeframe-selector {
    justify-content: space-between;
    width: 100%;
  }

  .chart-controls {
    justify-content: center;
  }

  .price-info {
    flex-wrap: wrap;
    gap: 10px;
  }

  .price-value {
    margin-right: 5px;
  }

  .chart-container {
    height: 500px;
  }
}

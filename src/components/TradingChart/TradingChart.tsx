import { useEffect, useState, useCallback, useRef } from 'react';
import ReactECharts from 'echarts-for-react';
import {
  FaChartBar, FaRegBell, <PERSON>a<PERSON><PERSON>, Fa<PERSON><PERSON><PERSON>, <PERSON>a<PERSON>hart<PERSON><PERSON>, Fa<PERSON><PERSON><PERSON>, FaRegLightbulb
} from 'react-icons/fa';
import './TradingChart.css';
import { fetchStockData, getDateRangeForLastDays, convertToChartData } from '../../services/auraPolygonService';
import {
  ChartBounds,
  calculateChartBounds,
  storeDrawingWithBothCoordinates,
  updateDrawingsForNewTimeframe
} from '../../utils/drawingCoordinates';

// Define the CandlestickData type
interface CandlestickData {
  time: string | number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume?: number;
}



// Generate more realistic trading data with hourly timestamps
const generateHourlyData = (days: number): CandlestickData[] => {
  const data: CandlestickData[] = [];
  let basePrice = 0.00953;
  const now = new Date();

  // Create a more realistic price pattern with trends and volatility clusters
  const trendDirections = [1, -1, 1, -1, 1]; // Alternating trends
  const trendLengths = [12, 18, 24, 16, 20]; // Hours per trend

  let currentTrendIndex = 0;
  let hoursInCurrentTrend = 0;
  let trendDirection = trendDirections[0];
  let volatilityMultiplier = 1;

  for (let i = days * 24; i >= 0; i--) {
    // Check if we need to switch trends
    if (hoursInCurrentTrend >= trendLengths[currentTrendIndex]) {
      currentTrendIndex = (currentTrendIndex + 1) % trendDirections.length;
      trendDirection = trendDirections[currentTrendIndex];
      hoursInCurrentTrend = 0;
      volatilityMultiplier = 0.8 + Math.random() * 0.8; // Random volatility between trends
    }

    const date = new Date(now.getTime() - i * 60 * 60 * 1000);
    const baseVolatility = 0.00015 * volatilityMultiplier;
    const trendStrength = 0.00005 * trendDirection;

    // Add some randomness to the trend
    const randomFactor = (Math.random() - 0.5) * 0.00008;

    const open = basePrice;
    const close = basePrice + trendStrength + randomFactor;

    // Higher volatility for high/low
    const highVolatility = baseVolatility * (1 + Math.random() * 0.5);
    const lowVolatility = baseVolatility * (1 + Math.random() * 0.5);

    const high = Math.max(open, close) + highVolatility;
    const low = Math.min(open, close) - lowVolatility;

    basePrice = close;
    hoursInCurrentTrend++;

    data.push({
      time: Math.floor(date.getTime() / 1000) as any,
      open,
      high,
      low,
      close
    });
  }

  return data;
};

// Generate candle data that matches the selected timeframe
const generateCandleDataForTimeframe = (timeframe: string): CandlestickData[] => {
  const data: CandlestickData[] = [];
  let basePrice = 0.00953;
  const now = new Date();

  // Determine candle interval and number of candles
  let intervalMs: number;
  let numCandles: number;

  switch (timeframe) {
    case '1m':
      intervalMs = 60 * 1000; // 1 minute
      numCandles = 1440; // 24 hours worth
      break;
    case '5m':
      intervalMs = 5 * 60 * 1000; // 5 minutes
      numCandles = 864; // 3 days worth
      break;
    case '15m':
      intervalMs = 15 * 60 * 1000; // 15 minutes
      numCandles = 672; // 1 week worth
      break;
    case '1h':
      intervalMs = 60 * 60 * 1000; // 1 hour
      numCandles = 720; // 1 month worth
      break;
    case '4h':
      intervalMs = 4 * 60 * 60 * 1000; // 4 hours
      numCandles = 540; // 3 months worth
      break;
    case '1d':
      intervalMs = 24 * 60 * 60 * 1000; // 1 day
      numCandles = 365; // 1 year worth
      break;
    case '1M':
      intervalMs = 30 * 24 * 60 * 60 * 1000; // ~1 month
      numCandles = 60; // 5 years worth
      break;
    case '1Y':
      intervalMs = 365 * 24 * 60 * 60 * 1000; // 1 year
      numCandles = 20; // 20 years worth
      break;
    default:
      intervalMs = 60 * 60 * 1000; // 1 hour default
      numCandles = 720;
  }

  // Generate candles going backwards from now
  for (let i = numCandles; i >= 0; i--) {
    const time = new Date(now.getTime() - i * intervalMs);

    // Create realistic price movements based on timeframe
    const volatilityMultiplier = timeframe === '1m' ? 0.005 :
                                timeframe === '5m' ? 0.008 :
                                timeframe === '15m' ? 0.012 :
                                timeframe === '1h' ? 0.02 :
                                timeframe === '4h' ? 0.035 :
                                timeframe === '1d' ? 0.05 :
                                timeframe === '1M' ? 0.15 : 0.3;

    const trend = Math.sin(i / (numCandles / 10)) * 0.001;
    const randomChange = (Math.random() - 0.5) * volatilityMultiplier;

    basePrice = Math.max(0.001, basePrice * (1 + trend + randomChange));

    const open = basePrice;
    const close = open * (1 + (Math.random() - 0.5) * volatilityMultiplier * 0.5);
    const high = Math.max(open, close) * (1 + Math.random() * volatilityMultiplier * 0.3);
    const low = Math.min(open, close) * (1 - Math.random() * volatilityMultiplier * 0.3);

    data.push({
      time: time.toISOString(),
      open: parseFloat(open.toFixed(6)),
      high: parseFloat(high.toFixed(6)),
      low: parseFloat(low.toFixed(6)),
      close: parseFloat(close.toFixed(6)),
      volume: Math.floor(Math.random() * 1000000) + 100000
    });
  }

  return data.reverse();
};

interface TradingChartProps {
  // Add props as needed
  symbol?: string;
}

const TradingChart: React.FC<TradingChartProps> = ({ symbol = 'TSLA' }) => {
  const [activeTimeframe, setActiveTimeframe] = useState<string>('4h');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [stockData, setStockData] = useState<CandlestickData[]>([]);
  const [currentSymbol, setCurrentSymbol] = useState<string>(symbol);


  // Filter states for UI
  const [breakoutEnabled, setBreakoutEnabled] = useState<boolean>(false);
  const [rangeEnabled, setRangeEnabled] = useState<boolean>(false);
  const [trendEnabled, setTrendEnabled] = useState<boolean>(false);

  // Drawing tools states
  const [showDrawingTools] = useState<boolean>(true); // Always show sidebar
  const [activeTool, setActiveTool] = useState<string | null>(null);
  const [drawings, setDrawings] = useState<any[]>([]);
  const [isDrawingMode, setIsDrawingMode] = useState<boolean>(false);
  const [showShortcuts, setShowShortcuts] = useState<boolean>(false);
  const [currentDrawing, setCurrentDrawing] = useState<any>(null);
  const [isDrawing, setIsDrawing] = useState<boolean>(false);
  const chartRef = useRef<any>(null);

  // Store chart bounds for drawing coordinate conversion
  const [chartBounds, setChartBounds] = useState<ChartBounds | null>(null);

  const [priceInfo, setPriceInfo] = useState({
    open: 0,
    high: 0,
    low: 0,
    close: 0,
    volume: 0
  });

  // TradingView-style drawing tools with exact Supabase icons
  const drawingTools = [
    {
      id: 'cursor',
      name: 'Cursor',
      icon: 'https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/sidebar/Group.png?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV8yNWIzMmY0OC0yOWY3LTRlNTgtOWMzNi1jYmZjNGJjM2NhMmYiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJzaWRlYmFyL0dyb3VwLnBuZyIsImlhdCI6MTc1MDE4NDA2NywiZXhwIjoxNzgxNzIwMDY3fQ.J29hT-j89Uexo_1hfbx2RgOaTeKmFkZW-0hrbBSIHak',
      iconType: 'image'
    },
    {
      id: 'line',
      name: 'Trend Line',
      icon: 'https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/sidebar/Tool%20(3).png?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV8yNWIzMmY0OC0yOWY3LTRlNTgtOWMzNi1jYmZjNGJjM2NhMmYiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJzaWRlYmFyL1Rvb2wgKDMpLnBuZyIsImlhdCI6MTc1MDE4NDA5NywiZXhwIjoxNzgxNzIwMDk3fQ.PM9v4Xgs10pElrxUt8TVvgO4gn24q1pzHrmPXlGwYf8',
      iconType: 'image'
    },
    {
      id: 'fibonacci',
      name: 'Fibonacci Retracement',
      icon: 'https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/sidebar/Tool%20(2).png?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV8yNWIzMmY0OC0yOWY3LTRlNTgtOWMzNi1jYmZjNGJjM2NhMmYiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJzaWRlYmFyL1Rvb2wgKDIpLnBuZyIsImlhdCI6MTc1MDE4NDA4OSwiZXhwIjoxNzgxNzIwMDg5fQ.edo7Kcr6TW1oPSW6d_wORJF1gyCgIjF1INiWeRIKhCw',
      iconType: 'image'
    },
    {
      id: 'measure',
      name: 'Measure Tool',
      icon: 'https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/sidebar/Tool.png?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV8yNWIzMmY0OC0yOWY3LTRlNTgtOWMzNi1jYmZjNGJjM2NhMmYiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJzaWRlYmFyL1Rvb2wucG5nIiwiaWF0IjoxNzUwMTg0MTMwLCJleHAiOjE3ODE3MjAxMzB9.um_xvfGo9HkhX6CsvDnaW6qr7Zbh_HyBd0O3jA9_Rs4',
      iconType: 'image'
    },
    {
      id: 'rr',
      name: 'Risk/Reward Ratio',
      icon: 'https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/sidebar/Tool%20(1).png?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV8yNWIzMmY0OC0yOWY3LTRlNTgtOWMzNi1jYmZjNGJjM2NhMmYiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJzaWRlYmFyL1Rvb2wgKDEpLnBuZyIsImlhdCI6MTc1MDE4NDA3OSwiZXhwIjoxNzgxNzIwMDc5fQ.BKGW5WPdWqYyRyPHh3sHSa93DEvbvhTP-tfa4BWdu2Q',
      iconType: 'image'
    },
    {
      id: 'text',
      name: 'Text Tool',
      icon: 'https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/sidebar/Tool%20(4).png?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV8yNWIzMmY0OC0yOWY3LTRlNTgtOWMzNi1jYmZjNGJjM2NhMmYiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJzaWRlYmFyL1Rvb2wgKDQpLnBuZyIsImlhdCI6MTc1MDE4NDEwOSwiZXhwIjoxNzgxNzIwMTA5fQ.YAaejdc7DK3BDdpQIpN80zkSMIKlvzuPIP9IJW2CiRQ',
      iconType: 'image'
    },
    {
      id: 'lock',
      name: 'Lock Tool',
      icon: 'https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/sign/sidebar/Tool%20(5).png?token=eyJraWQiOiJzdG9yYWdlLXVybC1zaWduaW5nLWtleV8yNWIzMmY0OC0yOWY3LTRlNTgtOWMzNi1jYmZjNGJjM2NhMmYiLCJhbGciOiJIUzI1NiJ9.eyJ1cmwiOiJzaWRlYmFyL1Rvb2wgKDUpLnBuZyIsImlhdCI6MTc1MDE4NDExOCwiZXhwIjoxNzgxNzIwMTE4fQ.u62DmOGWqBwipTS661cD2cB8YdixgNXCQSo3xyAOUE4',
      iconType: 'image'
    }
  ];

  const handleToolSelect = (toolId: string) => {
    setActiveTool(toolId);
    setIsDrawingMode(toolId !== 'cursor' && toolId !== 'delete' && toolId !== 'clear');
    setCurrentDrawing(null);
    setIsDrawing(false);

    // Handle special tools
    switch (toolId) {
      case 'cursor':
        // Exit drawing mode and return to normal cursor
        setActiveTool(null);
        setIsDrawingMode(false);
        setCurrentDrawing(null);
        setIsDrawing(false);
        break;
      case 'delete':
        // Enter delete mode - user clicks on drawings to delete them
        setIsDrawingMode(false);
        console.log('Delete mode activated - click on drawings to delete them');
        break;
      case 'clear':
        if (drawings.length > 0) {
          const confirmed = window.confirm(`Remove all ${drawings.length} drawing(s)?`);
          if (confirmed) {
            setDrawings([]);
            setActiveTool(null);
            setIsDrawingMode(false);
            setCurrentDrawing(null);
            setIsDrawing(false);
          }
        } else {
          setActiveTool(null);
        }
        break;
      case 'lock':
        // Toggle lock state for all drawings
        console.log('Locking all drawings');
        break;
      case 'hide':
        // Toggle visibility of all drawings
        console.log('Hiding all drawings');
        break;
      default:
        break;
    }
  };

  // Handle chart click for drawing
  const handleChartClick = (params: any, echarts?: any) => {
    console.log('handleChartClick called:', params);

    // Handle delete mode - check if clicking on a drawing or near one
    if (activeTool === 'delete') {
      console.log('Delete mode - checking for drawing click:', params);

      // First check if clicked directly on a drawing series (including hit areas)
      if (params.seriesName && params.seriesName.startsWith('Drawing_')) {
        const parts = params.seriesName.split('_');
        const drawingIndex = parseInt(parts[1]);

        if (!isNaN(drawingIndex) && drawingIndex >= 0 && drawingIndex < drawings.length) {
          const drawingType = drawings[drawingIndex].type;
          const confirmed = window.confirm(`Delete this ${drawingType} drawing?`);
          if (confirmed) {
            setDrawings(prev => prev.filter((_, index) => index !== drawingIndex));
            setActiveTool(null);
            console.log('Drawing deleted via series click');
          }
          return;
        }
      }

      // If no direct series click, try coordinate-based detection
      if (!chartRef.current) return;

      try {
        const chart = chartRef.current.getEchartsInstance();
        if (!chart || !params.event) return;

        // Convert pixel coordinates to chart coordinates
        const pointInPixel = [params.event.offsetX, params.event.offsetY];
        const pointInGrid = chart.convertFromPixel('grid', pointInPixel);

        if (!pointInGrid) return;

        const clickTime = new Date(pointInGrid[0]).toISOString();
        const clickPrice = pointInGrid[1];

        console.log('Click coordinates:', { time: clickTime, price: clickPrice });

        // Check proximity to each drawing
        for (let i = 0; i < drawings.length; i++) {
          const drawing = drawings[i];

          if (drawing.type === 'line' && drawing.points && drawing.points.length >= 2) {
            const [start, end] = drawing.points;

            // Simple proximity check - if click is near the line
            const startTime = new Date(start.time).getTime();
            const endTime = new Date(end.time).getTime();
            const clickTimeMs = new Date(clickTime).getTime();

            // Check if click is within time range of the line
            if (clickTimeMs >= Math.min(startTime, endTime) && clickTimeMs <= Math.max(startTime, endTime)) {
              // Calculate expected price at click time (linear interpolation)
              const timeRatio = (clickTimeMs - startTime) / (endTime - startTime);
              const expectedPrice = start.price + (end.price - start.price) * timeRatio;

              // Check if click is close to the line (within 5% of price range)
              const priceRange = Math.abs(end.price - start.price);
              const tolerance = Math.max(priceRange * 0.05, Math.abs(expectedPrice * 0.01)); // 5% of range or 1% of price

              if (Math.abs(clickPrice - expectedPrice) <= tolerance) {
                const confirmed = window.confirm(`Delete this ${drawing.type} drawing?`);
                if (confirmed) {
                  setDrawings(prev => prev.filter((_, index) => index !== i));
                  setActiveTool(null);
                  console.log('Drawing deleted via proximity detection');
                }
                return;
              }
            }
          }
        }

        console.log('No drawing found near click location');

      } catch (error) {
        console.error('Error in coordinate-based delete detection:', error);
      }

      return;
    }

    if (!isDrawingMode || !activeTool || !chartRef.current) {
      console.log('Not in drawing mode, no active tool, or no chart instance');
      return;
    }

    try {
      let point;

      if (params.event && params.event.offsetX && params.event.offsetY) {
        // Use ECharts coordinate conversion for accurate positioning
        const pointInPixel = [params.event.offsetX, params.event.offsetY];
        const pointInGrid = chartRef.current.convertFromPixel('grid', pointInPixel);

        if (pointInGrid && pointInGrid.length >= 2) {
          const [timeIndex, price] = pointInGrid;

          const timeValue = stockData[Math.floor(timeIndex)]?.time || stockData[0]?.time;
          const actualTime = typeof timeValue === 'number' ? new Date(timeValue * 1000).toISOString() : timeValue;

          point = {
            time: actualTime,
            price: price
          };
        }
      } else if (params.dataIndex !== undefined && params.value) {
        // Direct click on data point
        const timeValue = stockData[params.dataIndex]?.time || stockData[0]?.time;
        const actualTime = typeof timeValue === 'number' ? new Date(timeValue * 1000).toISOString() : timeValue;

        point = {
          time: actualTime,
          price: params.value[2] // Close price
        };
      }

      if (point) {
        console.log('Drawing point:', point, 'Tool:', activeTool);
        handleDrawingAction(point);
      } else {
        console.log('Could not determine click coordinates');
      }
    } catch (error) {
      console.error('Error in chart click:', error);
    }
  };

  // Handle mouse move for drawing preview - improved cursor tracking with tooltip protection
  const handleChartMouseMove = (params: any, echarts?: any) => {
    // Only process when actively drawing to prevent tooltip interference
    if (!isDrawingMode || !activeTool || !chartRef.current || !isDrawing || !currentDrawing) return;

    try {
      let point = null;

      // Use ECharts coordinate conversion for mouse move with better error handling
      if (params.event && params.event.offsetX !== undefined && params.event.offsetY !== undefined) {
        const pointInPixel = [params.event.offsetX, params.event.offsetY];

        // Safely attempt coordinate conversion
        try {
          const pointInGrid = chartRef.current.convertFromPixel('grid', pointInPixel);

          if (pointInGrid && Array.isArray(pointInGrid) && pointInGrid.length >= 2 &&
              typeof pointInGrid[0] === 'number' && typeof pointInGrid[1] === 'number') {
            const [timeIndex, price] = pointInGrid;

            // Ensure we have valid data bounds
            if (stockData && stockData.length > 0) {
              const safeIndex = Math.floor(Math.max(0, Math.min(timeIndex, stockData.length - 1)));
              const timeValue = stockData[safeIndex]?.time || stockData[0]?.time;
              const actualTime = typeof timeValue === 'number' ? new Date(timeValue * 1000).toISOString() : timeValue;

              point = {
                time: actualTime,
                price: price
              };
            }
          }
        } catch (conversionError) {
          // Silently handle coordinate conversion errors to prevent tooltip glitching
          return;
        }
      }

      // Update current drawing preview when we have a valid point
      if (point && currentDrawing.points && currentDrawing.points.length > 0) {
        switch (activeTool) {
          case 'line':
            setCurrentDrawing(prev => ({
              ...prev,
              points: [prev.points[0], point]
            }));
            break;

          case 'circle':
            setCurrentDrawing(prev => ({
              ...prev,
              points: [prev.points[0], point]
            }));
            break;

          case 'rectangle':
            setCurrentDrawing(prev => ({
              ...prev,
              points: [prev.points[0], point]
            }));
            break;

          case 'measure':
          case 'fibonacci':
          case 'rr':
            setCurrentDrawing(prev => ({
              ...prev,
              points: [prev.points[0], point]
            }));
            break;
        }
      }
    } catch (error) {
      // Silently handle errors to prevent tooltip interference
      console.debug('Drawing mouse move error:', error);
    }
  };

  // Function to fetch stock data
  const fetchData = useCallback(async (timeframe: string) => {
    setIsLoading(true);
    setError(null);

    try {
      let multiplier = 1;
      let timespanUnit = 'hour';
      let days = 7;

      // Set the appropriate timeframe parameters for Polygon API
      switch (timeframe) {
        case '1m':
          multiplier = 1;
          timespanUnit = 'minute';
          days = 1; // 1 day of 1-minute candles
          break;
        case '5m':
          multiplier = 5;
          timespanUnit = 'minute';
          days = 3; // 3 days of 5-minute candles
          break;
        case '15m':
          multiplier = 15;
          timespanUnit = 'minute';
          days = 7; // 1 week of 15-minute candles
          break;
        case '1h':
          multiplier = 1;
          timespanUnit = 'hour';
          days = 30; // 1 month of 1-hour candles
          break;
        case '4h':
          multiplier = 4;
          timespanUnit = 'hour';
          days = 90; // 3 months of 4-hour candles
          break;
        case '1d':
          multiplier = 1;
          timespanUnit = 'day';
          days = 365; // 1 year of daily candles
          break;
        case '1M':
          multiplier = 1;
          timespanUnit = 'month';
          days = 365 * 5; // 5 years of monthly candles
          break;
        case '1Y':
          multiplier = 1;
          timespanUnit = 'year';
          days = 365 * 20; // 20 years of yearly candles
          break;
        default:
          multiplier = 1;
          timespanUnit = 'hour';
          days = 30;
      }

      try {
        const dateRange = getDateRangeForLastDays(days);
        const stockData = await fetchStockData(
          currentSymbol,
          multiplier,
          timespanUnit,
          dateRange.from,
          dateRange.to
        );

        const chartData = convertToChartData(stockData);
        setStockData(chartData);

        // Update price info with the latest data
        if (chartData.length > 0) {
          const latestData = chartData[chartData.length - 1];
          setPriceInfo({
            open: latestData.open,
            high: latestData.high,
            low: latestData.low,
            close: latestData.close,
            volume: latestData.volume || 0
          });
        }
      } catch (apiError: any) {
        console.error('API Error:', apiError);
        throw apiError; // Re-throw to be caught by the outer catch
      }
    } catch (err) {
      console.error('Error fetching stock data:', err);
      setError('Failed to fetch stock data. Using simulated data instead.');

      // Generate fallback data that matches the selected timeframe
      let fallbackData: CandlestickData[];
      fallbackData = generateCandleDataForTimeframe(timeframe);

      setStockData(fallbackData);

      // Update price info with the latest fallback data
      if (fallbackData.length > 0) {
        const latestData = fallbackData[fallbackData.length - 1];
        setPriceInfo({
          open: latestData.open,
          high: latestData.high,
          low: latestData.low,
          close: latestData.close,
          volume: 1000000 // Default volume for fallback data
        });
      }
    } finally {
      setIsLoading(false);
    }
  }, [currentSymbol]);



  // Initialize chart and fetch data
  useEffect(() => {
    // Initial data load
    fetchData(activeTimeframe);
  }, [activeTimeframe, fetchData]);

  // Update chart bounds when stock data changes and adjust drawings for new timeframe
  useEffect(() => {
    if (stockData && stockData.length > 0) {
      const newBounds = calculateChartBounds(stockData);

      // If we have existing bounds and drawings, update drawings for new timeframe
      if (chartBounds && drawings.length > 0) {
        const updatedDrawings = updateDrawingsForNewTimeframe(drawings, newBounds);
        setDrawings(updatedDrawings);
      }

      setChartBounds(newBounds);
    }
  }, [stockData, activeTimeframe]);

  // Keyboard shortcuts for drawing tools
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      // Only handle shortcuts when not typing in input fields
      if (event.target instanceof HTMLInputElement) return;

      switch (event.key.toLowerCase()) {
        case 'escape':
          setActiveTool(null);
          setIsDrawingMode(false);
          break;
        case 'l':
          if (!event.ctrlKey && !event.metaKey) {
            handleToolSelect('line');
          }
          break;
        case 'c':
          if (!event.ctrlKey && !event.metaKey) {
            handleToolSelect('circle');
          }
          break;
        case 'r':
          if (!event.ctrlKey && !event.metaKey) {
            handleToolSelect('rectangle');
          }
          break;
        case 'm':
          if (!event.ctrlKey && !event.metaKey) {
            handleToolSelect('measure');
          }
          break;
        case 'f':
          if (!event.ctrlKey && !event.metaKey) {
            handleToolSelect('fibonacci');
          }
          break;
        case 'x':
          if (!event.ctrlKey && !event.metaKey) {
            handleToolSelect('rr');
          }
          break;
        case 'delete':
        case 'backspace':
          if (event.ctrlKey || event.metaKey) {
            handleToolSelect('remove');
          }
          break;
      }
    };

    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [showDrawingTools]);

  // Force chart re-render when drawing preview changes for better visibility
  useEffect(() => {
    if (currentDrawing && isDrawing) {
      // Debounce the chart updates to avoid excessive re-renders
      const timeoutId = setTimeout(() => {
        // Force a minimal chart update to ensure drawing preview is visible
        if (chartRef.current) {
          try {
            // Get current option and re-apply to trigger re-render
            const currentOption = chartRef.current.getOption();
            chartRef.current.setOption(currentOption, {
              notMerge: false,
              lazyUpdate: false,
              silent: true
            });
          } catch (error) {
            console.log('Chart update error (non-critical):', error);
          }
        }
      }, 16); // ~60fps update rate for smooth preview

      return () => clearTimeout(timeoutId);
    }
  }, [currentDrawing, isDrawing]);

  // Update chart options when drawing mode changes to handle tooltip state
  useEffect(() => {
    if (chartRef.current) {
      const newOption = getChartOptions();
      chartRef.current.setOption(newOption, {
        notMerge: false,
        lazyUpdate: false
      });
    }
  }, [isDrawingMode]);

  // Improved click handler for chart container with better coordinate conversion
  const handleContainerClick = (event: React.MouseEvent<HTMLDivElement>) => {
    console.log('Container clicked! Drawing mode:', isDrawingMode, 'Active tool:', activeTool);

    // Handle delete mode - don't process as drawing
    if (activeTool === 'delete') {
      console.log('Delete mode - container click ignored, waiting for drawing click');
      return;
    }

    if (!isDrawingMode || !activeTool || !chartRef.current) {
      console.log('Not in drawing mode, no active tool, or no chart instance');
      return;
    }

    // Prevent event bubbling
    event.stopPropagation();

    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    console.log('Click pixel coordinates:', { x, y });

    try {
      // Use ECharts coordinate conversion for accurate positioning
      const pointInPixel = [x, y];
      const pointInGrid = chartRef.current.convertFromPixel('grid', pointInPixel);

      if (pointInGrid && pointInGrid.length >= 2) {
        const [timeIndex, price] = pointInGrid;

        // Ensure timeIndex is within bounds
        const clampedTimeIndex = Math.max(0, Math.min(Math.floor(timeIndex), stockData.length - 1));
        const timeValue = stockData[clampedTimeIndex]?.time || stockData[0]?.time;
        const actualTime = typeof timeValue === 'number' ? new Date(timeValue * 1000).toISOString() : timeValue;

        const point = {
          time: actualTime,
          price: price
        };

        console.log('Converted point:', point);
        handleDrawingAction(point);
      } else {
        console.log('Could not convert coordinates, using fallback');
        // Improved fallback calculation
        const chartContainer = event.currentTarget.querySelector('.echarts-for-react');
        if (chartContainer) {
          const chartRect = chartContainer.getBoundingClientRect();
          const relativeX = (x - (rect.left - chartRect.left)) / chartRect.width;
          const relativeY = (y - (rect.top - chartRect.top)) / chartRect.height;

          // Account for chart margins (approximately 8% left, 5% right, 8% top, 17% bottom)
          const adjustedX = Math.max(0, Math.min(1, (relativeX - 0.08) / (1 - 0.08 - 0.05)));
          const adjustedY = Math.max(0, Math.min(1, (relativeY - 0.08) / (1 - 0.08 - 0.17)));

          const dataIndex = Math.floor(adjustedX * stockData.length);
          const prices = stockData.map(d => [d.low, d.high]).flat();
          const minPrice = Math.min(...prices);
          const maxPrice = Math.max(...prices);
          const price = maxPrice - (adjustedY * (maxPrice - minPrice));

          const clampedDataIndex = Math.max(0, Math.min(dataIndex, stockData.length - 1));
          const timeValue = stockData[clampedDataIndex]?.time || stockData[0]?.time;
          const actualTime = typeof timeValue === 'number' ? new Date(timeValue * 1000).toISOString() : timeValue;

          const point = {
            time: actualTime,
            price: Math.max(minPrice, Math.min(price, maxPrice))
          };

          console.log('Fallback point:', point);
          handleDrawingAction(point);
        }
      }
    } catch (error) {
      console.error('Error converting coordinates:', error);
    }
  };

  // Extracted drawing action logic
  const handleDrawingAction = (point: { time: string; price: number }) => {
    console.log('Drawing action:', activeTool, point);

    switch (activeTool) {
      case 'line':
        if (!isDrawing) {
          // Start drawing
          setCurrentDrawing({
            type: 'line',
            points: [point]
          });
          setIsDrawing(true);
          console.log('Started drawing line');
        } else {
          // Finish drawing
          const newDrawing = {
            type: 'line',
            points: [currentDrawing.points[0], point],
            id: Date.now()
          };

          // Store with both absolute and relative coordinates if bounds are available
          const drawingToStore = chartBounds
            ? storeDrawingWithBothCoordinates(newDrawing, chartBounds)
            : newDrawing;

          setDrawings(prev => [...prev, drawingToStore]);
          setCurrentDrawing(null);
          setIsDrawing(false);
          console.log('Finished drawing line:', drawingToStore);
        }
        break;

      case 'circle':
        if (!isDrawing) {
          // Start drawing circle
          setCurrentDrawing({
            type: 'circle',
            center: point,
            points: [point]
          });
          setIsDrawing(true);
          console.log('Started drawing circle');
        } else {
          // Finish drawing circle - second point determines radius
          const newDrawing = {
            type: 'circle',
            center: currentDrawing.center,
            radiusPoint: point,
            id: Date.now()
          };

          // Store with both absolute and relative coordinates if bounds are available
          const drawingToStore = chartBounds
            ? storeDrawingWithBothCoordinates(newDrawing, chartBounds)
            : newDrawing;

          setDrawings(prev => [...prev, drawingToStore]);
          setCurrentDrawing(null);
          setIsDrawing(false);
          console.log('Finished drawing circle:', drawingToStore);
        }
        break;

      case 'rectangle':
        if (!isDrawing) {
          // Start drawing rectangle
          setCurrentDrawing({
            type: 'rectangle',
            points: [point]
          });
          setIsDrawing(true);
          console.log('Started drawing rectangle');
        } else {
          // Finish drawing rectangle
          const newDrawing = {
            type: 'rectangle',
            points: [currentDrawing.points[0], point],
            id: Date.now()
          };

          // Store with both absolute and relative coordinates if bounds are available
          const drawingToStore = chartBounds
            ? storeDrawingWithBothCoordinates(newDrawing, chartBounds)
            : newDrawing;

          setDrawings(prev => [...prev, drawingToStore]);
          setCurrentDrawing(null);
          setIsDrawing(false);
          console.log('Finished drawing rectangle:', drawingToStore);
        }
        break;

      case 'measure':
        if (!isDrawing) {
          // Start measuring
          setCurrentDrawing({
            type: 'measure',
            points: [point]
          });
          setIsDrawing(true);
          console.log('Started measuring');
        } else {
          // Finish measuring
          const newDrawing = {
            type: 'measure',
            points: [currentDrawing.points[0], point],
            id: Date.now()
          };

          // Store with both absolute and relative coordinates if bounds are available
          const drawingToStore = chartBounds
            ? storeDrawingWithBothCoordinates(newDrawing, chartBounds)
            : newDrawing;

          setDrawings(prev => [...prev, drawingToStore]);
          setCurrentDrawing(null);
          setIsDrawing(false);
          console.log('Finished measuring:', drawingToStore);
        }
        break;

      case 'fibonacci':
        if (!isDrawing) {
          // Start fibonacci
          setCurrentDrawing({
            type: 'fibonacci',
            points: [point]
          });
          setIsDrawing(true);
          console.log('Started fibonacci');
        } else {
          // Finish fibonacci
          const newDrawing = {
            type: 'fibonacci',
            points: [currentDrawing.points[0], point],
            id: Date.now()
          };

          // Store with both absolute and relative coordinates if bounds are available
          const drawingToStore = chartBounds
            ? storeDrawingWithBothCoordinates(newDrawing, chartBounds)
            : newDrawing;

          setDrawings(prev => [...prev, drawingToStore]);
          setCurrentDrawing(null);
          setIsDrawing(false);
          console.log('Finished fibonacci:', drawingToStore);
        }
        break;

      case 'rr':
        // Drop in a default risk/reward setup with one click
        const entryPrice = point.price;
        const riskAmount = entryPrice * 0.02; // 2% risk
        const targetPrice = entryPrice + (riskAmount * 2); // 1:2 risk/reward
        const stopPrice = entryPrice - riskAmount;

        // Create entry point and target point for the RR setup
        const targetTime = new Date(new Date(point.time).getTime() + (24 * 60 * 60 * 1000)).toISOString(); // 1 day later

        const newDrawing = {
          type: 'rr',
          points: [
            point, // Entry point
            { time: targetTime, price: targetPrice } // Target point
          ],
          entryPrice,
          targetPrice,
          stopPrice,
          id: Date.now()
        };

        // Store with both absolute and relative coordinates if bounds are available
        const drawingToStore = chartBounds
          ? storeDrawingWithBothCoordinates(newDrawing, chartBounds)
          : newDrawing;

        setDrawings(prev => [...prev, drawingToStore]);
        setActiveTool(null);
        setIsDrawingMode(false);
        console.log('Dropped risk/reward setup:', drawingToStore);
        break;

      default:
        console.log('Tool not implemented yet:', activeTool);
        break;
    }
  };

  // Function to create drawing series from drawings array - improved for zoom persistence
  const createDrawingSeries = () => {
    const drawingSeries: any[] = [];

    console.log('Adding drawings to chart:', drawings.length);
    drawings.forEach((drawing, index) => {
      console.log(`Processing drawing ${index}:`, drawing.type, drawing.points?.length);
      switch (drawing.type) {
        case 'line':
          // Show exactly what the user drew - no extension
          // Ensure coordinates are properly formatted for persistence across zoom
          const lineData = drawing.points.map((point: any) => {
            // Always use ISO string format for time consistency
            let timeValue: string;
            if (typeof point.time === 'string') {
              timeValue = point.time;
            } else if (typeof point.time === 'number') {
              timeValue = new Date(point.time).toISOString();
            } else {
              timeValue = new Date(point.time).toISOString();
            }
            console.log(`Line point: time=${timeValue}, price=${point.price}`);
            return [timeValue, point.price];
          });

          drawingSeries.push({
            name: `Drawing_${index}`,
            type: 'line',
            coordinateSystem: 'cartesian2d',
            xAxisIndex: 0,
            yAxisIndex: 0,
            data: lineData,
            lineStyle: {
              color: '#00e7b6',
              width: activeTool === 'delete' ? 4 : 2, // Make thicker in delete mode
              type: 'solid'
            },
            symbol: activeTool === 'delete' ? 'circle' : 'none', // Show symbols in delete mode
            symbolSize: activeTool === 'delete' ? 8 : 0,
            animation: false,
            silent: false, // Allow clicking for delete functionality
            z: 1000,
            zlevel: 1,
            clip: false, // Prevent clipping
            large: false, // Disable large mode
            progressive: 0, // Disable progressive rendering
            emphasis: {
              disabled: activeTool !== 'delete', // Only enable emphasis in delete mode
              lineStyle: {
                width: activeTool === 'delete' ? 6 : 2,
                color: activeTool === 'delete' ? '#ff6b6b' : '#00e7b6'
              }
            },

          });

          // In delete mode, make the line more prominent for easier targeting
          if (activeTool === 'delete') {
            // Add a thicker invisible line for easier clicking
            drawingSeries.push({
              name: `Drawing_${index}_hitarea`,
              type: 'line',
              coordinateSystem: 'cartesian2d',
              xAxisIndex: 0,
              yAxisIndex: 0,
              data: lineData,
              lineStyle: {
                color: 'transparent',
                width: 15 // Wide invisible line for easier clicking
              },
              symbol: 'none',
              animation: false,
              silent: false,
              z: 999, // Just below the visible line
              zlevel: 1,
              clip: false
            });
          }
          break;

        case 'circle':
          // Create circle using center and radius point
          if (drawing.center && drawing.radiusPoint) {
            const centerTime = new Date(drawing.center.time).getTime();
            const radiusTime = new Date(drawing.radiusPoint.time).getTime();
            const timeDiff = Math.abs(radiusTime - centerTime);
            const priceDiff = Math.abs(drawing.radiusPoint.price - drawing.center.price);
            const radius = Math.sqrt(timeDiff * timeDiff + priceDiff * priceDiff);

            // Create circle points (simplified as octagon for performance)
            const circlePoints = [];
            for (let i = 0; i <= 16; i++) {
              const angle = (i / 16) * 2 * Math.PI;
              const timeOffset = Math.cos(angle) * timeDiff;
              const priceOffset = Math.sin(angle) * priceDiff;
              const pointTime = new Date(centerTime + timeOffset).toISOString();
              const pointPrice = drawing.center.price + priceOffset;
              circlePoints.push([pointTime, pointPrice]);
            }

            drawingSeries.push({
              name: `Drawing_${index}`,
              type: 'line',
              coordinateSystem: 'cartesian2d',
              data: circlePoints,
              lineStyle: {
                color: '#00e7b6',
                width: 2,
                type: 'solid'
              },
              symbol: 'none',
              animation: false,
              silent: false, // Allow clicking for delete functionality
              z: 1000,
              zlevel: 1,
              clip: false
            });
          }
          break;

        case 'rectangle':
          if (drawing.points && drawing.points.length >= 2) {
            const [start, end] = drawing.points;
            drawingSeries.push({
              name: `Drawing_${index}`,
              type: 'line',
              coordinateSystem: 'cartesian2d',
              data: [
                [start.time, start.price],
                [end.time, start.price],
                [end.time, end.price],
                [start.time, end.price],
                [start.time, start.price]
              ],
              lineStyle: {
                color: '#00e7b6',
                width: 2,
                type: 'solid'
              },
              symbol: 'none',
              animation: false,
              silent: false, // Allow clicking for delete functionality
              z: 1000,
              zlevel: 1,
              clip: false // Prevent clipping
            });
          }
          break;

        case 'measure':
          if (drawing.points && drawing.points.length >= 2) {
            const [start, end] = drawing.points;
            // Calculate distance and percentage
            const timeDiff = Math.abs(new Date(end.time).getTime() - new Date(start.time).getTime());
            const priceDiff = Math.abs(end.price - start.price);
            const priceChange = ((end.price - start.price) / start.price * 100).toFixed(2);

            drawingSeries.push({
              name: `Drawing_${index}`,
              type: 'line',
              coordinateSystem: 'cartesian2d',
              data: [[start.time, start.price], [end.time, end.price]],
              lineStyle: {
                color: '#ffa500',
                width: 2,
                type: 'dashed'
              },
              symbol: 'circle',
              symbolSize: 6,
              animation: false,
              silent: false, // Allow clicking for delete functionality
              z: 1000,
              zlevel: 1,
              clip: false,
              label: {
                show: true,
                position: 'middle',
                formatter: `${priceChange}%`,
                color: '#ffa500',
                fontSize: 12
              }
            });
          }
          break;

        case 'fibonacci':
          if (drawing.points && drawing.points.length >= 2) {
            const [start, end] = drawing.points;
            const priceDiff = end.price - start.price;
            const fibLevels = [0, 0.236, 0.382, 0.5, 0.618, 0.786, 1];

            fibLevels.forEach((level, levelIndex) => {
              const price = start.price + (priceDiff * level);
              drawingSeries.push({
                name: `Fib_${index}_${levelIndex}`,
                type: 'line',
                coordinateSystem: 'cartesian2d',
                data: [[start.time, price], [end.time, price]],
                lineStyle: {
                  color: level === 0.5 ? '#ff6b6b' : '#00e7b6',
                  width: level === 0.5 ? 2 : 1,
                  type: 'dashed',
                  opacity: 0.8
                },
                symbol: 'none',
                animation: false,
                silent: false, // Allow clicking for delete functionality
                z: 1000,
                zlevel: 1,
                clip: false,
                label: {
                  show: true,
                  position: 'insideEndTop',
                  formatter: `${(level * 100).toFixed(1)}%`,
                  color: '#00e7b6',
                  fontSize: 10
                }
              });
            });
          }
          break;

        case 'rr':
          if (drawing.points && drawing.points.length >= 2) {
            const [entry, target] = drawing.points;
            const entryPrice = entry.price;
            const targetPrice = target.price;
            const isLong = targetPrice > entryPrice;

            // Calculate stop loss (1:2 risk reward ratio)
            const riskAmount = Math.abs(targetPrice - entryPrice) / 2;
            const stopPrice = isLong ? entryPrice - riskAmount : entryPrice + riskAmount;

            // Entry line
            drawingSeries.push({
              name: `RR_Entry_${index}`,
              type: 'line',
              coordinateSystem: 'cartesian2d',
              data: [[entry.time, entryPrice], [target.time, entryPrice]],
              lineStyle: {
                color: '#ffffff',
                width: 2,
                type: 'solid'
              },
              symbol: 'none',
              animation: false,
              silent: false, // Allow clicking for delete functionality
              z: 1000,
              zlevel: 1,
              clip: false,
              label: {
                show: true,
                position: 'insideEndTop',
                formatter: 'Entry',
                color: '#ffffff',
                fontSize: 10
              }
            });

            // Target line
            drawingSeries.push({
              name: `RR_Target_${index}`,
              type: 'line',
              coordinateSystem: 'cartesian2d',
              data: [[entry.time, targetPrice], [target.time, targetPrice]],
              lineStyle: {
                color: '#00ff00',
                width: 2,
                type: 'dashed'
              },
              symbol: 'none',
              animation: false,
              silent: false, // Allow clicking for delete functionality
              z: 1000,
              zlevel: 1,
              clip: false,
              label: {
                show: true,
                position: 'insideEndTop',
                formatter: 'Target (2R)',
                color: '#00ff00',
                fontSize: 10
              }
            });

            // Stop loss line
            drawingSeries.push({
              name: `RR_Stop_${index}`,
              type: 'line',
              coordinateSystem: 'cartesian2d',
              data: [[entry.time, stopPrice], [target.time, stopPrice]],
              lineStyle: {
                color: '#ff0000',
                width: 2,
                type: 'dashed'
              },
              symbol: 'none',
              animation: false,
              silent: false, // Allow clicking for delete functionality
              z: 1000,
              zlevel: 1,
              clip: false,
              label: {
                show: true,
                position: 'insideEndTop',
                formatter: 'Stop (1R)',
                color: '#ff0000',
                fontSize: 10
              }
            });
          }
          break;
      }
    });

    // Add current drawing if in progress - always show preview
    if (currentDrawing && isDrawing) {
      switch (currentDrawing.type) {
        case 'line':
          if (currentDrawing.points && currentDrawing.points.length >= 1) {
            // Always show preview line, even with just one point
            let points = currentDrawing.points;

            // If we only have one point, create a very short line for visibility
            if (points.length === 1) {
              const singlePoint = points[0];
              // Create a tiny offset for the second point to make it visible
              points = [singlePoint, singlePoint];
            }

            drawingSeries.push({
              name: 'CurrentDrawing',
              type: 'line',
              coordinateSystem: 'cartesian2d',
              data: points.map((point: any) => [point.time, point.price]),
              lineStyle: {
                color: '#00e7b6',
                width: 3,
                type: 'dashed',
                opacity: 0.9
              },
              symbol: 'circle',
              symbolSize: 6,
              animation: false,
              silent: true,
              z: 1001,
              zlevel: 1,
              clip: false // Prevent clipping for current drawing
            });
          }
          break;

        case 'circle':
          if (currentDrawing.points && currentDrawing.points.length >= 2) {
            const [center, radiusPoint] = currentDrawing.points;
            const centerTime = new Date(center.time).getTime();
            const radiusTime = new Date(radiusPoint.time).getTime();
            const timeDiff = Math.abs(radiusTime - centerTime);
            const priceDiff = Math.abs(radiusPoint.price - center.price);

            // Create circle preview (simplified as octagon)
            const circlePoints = [];
            for (let i = 0; i <= 16; i++) {
              const angle = (i / 16) * 2 * Math.PI;
              const timeOffset = Math.cos(angle) * timeDiff;
              const priceOffset = Math.sin(angle) * priceDiff;
              const pointTime = new Date(centerTime + timeOffset).toISOString();
              const pointPrice = center.price + priceOffset;
              circlePoints.push([pointTime, pointPrice]);
            }

            drawingSeries.push({
              name: 'CurrentDrawing',
              type: 'line',
              coordinateSystem: 'cartesian2d',
              data: circlePoints,
              lineStyle: {
                color: '#00e7b6',
                width: 2,
                type: 'dashed',
                opacity: 0.8
              },
              symbol: 'none',
              animation: false,
              silent: true,
              z: 1001,
              zlevel: 1,
              clip: false
            });
          }
          break;

        case 'rectangle':
          if (currentDrawing.points && currentDrawing.points.length >= 2) {
            const [start, end] = currentDrawing.points;
            drawingSeries.push({
              name: 'CurrentDrawing',
              type: 'line',
              coordinateSystem: 'cartesian2d',
              data: [
                [start.time, start.price],
                [end.time, start.price],
                [end.time, end.price],
                [start.time, end.price],
                [start.time, start.price]
              ],
              lineStyle: {
                color: '#00e7b6',
                width: 2,
                type: 'dashed',
                opacity: 0.8
              },
              symbol: 'none',
              animation: false,
              silent: true,
              z: 1001,
              zlevel: 1,
              clip: false // Prevent clipping for rectangle preview
            });
          }
          break;

        case 'measure':
        case 'fibonacci':
        case 'rr':
          // Show simple line preview for these tools
          if (currentDrawing.points && currentDrawing.points.length >= 2) {
            const [start, end] = currentDrawing.points;
            drawingSeries.push({
              name: 'CurrentDrawing',
              type: 'line',
              coordinateSystem: 'cartesian2d',
              data: [[start.time, start.price], [end.time, end.price]],
              lineStyle: {
                color: currentDrawing.type === 'measure' ? '#ffa500' :
                       currentDrawing.type === 'rr' ? '#ffffff' : '#00e7b6',
                width: 2,
                type: 'dashed',
                opacity: 0.8
              },
              symbol: 'circle',
              symbolSize: 4,
              animation: false,
              silent: true,
              z: 1001,
              zlevel: 1,
              clip: false
            });
          }
          break;
      }

      // Add start point marker for better visibility (for all tools)
      if (currentDrawing.points && currentDrawing.points.length >= 1) {
        drawingSeries.push({
          name: 'StartPoint',
          type: 'scatter',
          coordinateSystem: 'cartesian2d',
          data: [[currentDrawing.points[0].time, currentDrawing.points[0].price]],
          symbol: 'circle',
          symbolSize: 8,
          itemStyle: {
            color: '#00e7b6',
            borderColor: '#ffffff',
            borderWidth: 2,
            shadowColor: '#00e7b6',
            shadowBlur: 10
          },
          animation: false,
          silent: true,
          z: 1002,
          zlevel: 1,
          clip: false // Prevent clipping for start point
        });
      }
    }

    return drawingSeries;
  };

  // Function to get ECharts options
  const getChartOptions = () => {
    // Format data for ECharts
    const formattedData = stockData.map(item => [
      // Convert timestamp to date string if needed
      typeof item.time === 'number' ? new Date(item.time * 1000).toISOString() : item.time,
      item.open,
      item.close,
      item.low,
      item.high,
      item.volume || 0
    ]);

    return {
      backgroundColor: '#000000',
      animation: false,
      legend: {
        show: false,
      },
      tooltip: {
        trigger: 'axis',
        // Disable tooltip when in drawing mode to prevent glitching
        show: !isDrawingMode,
        axisPointer: {
          type: 'cross',
          lineStyle: {
            color: 'rgba(0, 231, 182, 0.2)',
            width: 1
          }
        },
        backgroundColor: 'rgba(0, 0, 0, 0.95)',
        borderColor: 'rgba(0, 231, 182, 0.3)',
        borderWidth: 1,
        borderRadius: 12,
        padding: [12, 16],
        shadowBlur: 20,
        shadowColor: 'rgba(0, 0, 0, 0.8)',
        shadowOffsetX: 0,
        shadowOffsetY: 4,
        textStyle: {
          color: '#ffffff',
          fontFamily: "'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif",
          fontSize: 13,
          fontWeight: 500,
        },
        // Prevent tooltip position conflicts during drawing
        position: isDrawingMode ? 'none' : undefined,
        formatter: (params: any) => {
          if (!params || !params[0]) return '';

          const axisValue = params[0].axisValue; // This is the time from x-axis
          const candleData = params[0].data; // This is the OHLC data [open, close, low, high]

          // Find the data index to get volume from stockData
          const dataIndex = params[0].dataIndex;
          const volume = stockData[dataIndex]?.volume || 'N/A';

          // Parse the time properly
          const date = new Date(axisValue);

          // Format time based on active timeframe (same as x-axis)
          let timeStr = '';
          switch (activeTimeframe) {
            case '1m':
            case '5m':
            case '15m':
              timeStr = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
              break;
            case '1h':
            case '4h':
              timeStr = `${date.getHours().toString().padStart(2, '0')}:00 ${date.getMonth() + 1}/${date.getDate()}`;
              break;
            case '1d':
              timeStr = `${date.getMonth() + 1}/${date.getDate()}/${date.getFullYear()}`;
              break;
            case '1M':
              timeStr = `${date.getMonth() + 1}/${date.getFullYear()}`;
              break;
            case '1Y':
              timeStr = date.getFullYear().toString();
              break;
            default:
              timeStr = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
          }

          // Format volume with commas for readability
          const formattedVolume = typeof volume === 'number' ? volume.toLocaleString() : volume;

          if (!candleData || candleData.length < 4) return '';

          const [open, close, low, high] = candleData;
          const change = close - open;
          const changePercent = ((change / open) * 100).toFixed(2);
          const changeColor = change >= 0 ? '#00e7b6' : '#ff4757';
          const glowColor = change >= 0 ? 'rgba(0, 231, 182, 0.3)' : 'rgba(255, 71, 87, 0.3)';

          return `
            <div style="
              background: linear-gradient(135deg, rgba(0, 0, 0, 0.98) 0%, rgba(0, 10, 20, 0.95) 100%);
              border: 1px solid rgba(0, 231, 182, 0.4);
              border-radius: 16px;
              padding: 16px;
              font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
              box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.9),
                0 0 0 1px rgba(0, 231, 182, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
              backdrop-filter: blur(25px);
              min-width: 220px;
              position: relative;
              overflow: hidden;
            ">
              <!-- Header with symbol and time -->
              <div style="
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 12px;
                padding-bottom: 8px;
                border-bottom: 1px solid rgba(0, 231, 182, 0.15);
              ">
                <div style="
                  color: #00e7b6;
                  font-size: 14px;
                  font-weight: 700;
                  text-transform: uppercase;
                  letter-spacing: 1.2px;
                  text-shadow: 0 0 12px rgba(0, 231, 182, 0.6);
                ">${currentSymbol}</div>
                <div style="
                  color: rgba(255, 255, 255, 0.6);
                  font-size: 10px;
                  font-weight: 500;
                  letter-spacing: 0.5px;
                ">${timeStr}</div>
              </div>

              <!-- OHLC Grid -->
              <div style="
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 8px;
                margin-bottom: 12px;
              ">
                <div style="display: flex; justify-content: space-between;">
                  <span style="color: rgba(255, 255, 255, 0.6); font-size: 11px;">O</span>
                  <span style="color: #ffffff; font-weight: 600; font-size: 11px;">$${open.toFixed(4)}</span>
                </div>
                <div style="display: flex; justify-content: space-between;">
                  <span style="color: rgba(255, 255, 255, 0.6); font-size: 11px;">H</span>
                  <span style="color: #00e7b6; font-weight: 600; font-size: 11px; text-shadow: 0 0 6px rgba(0, 231, 182, 0.4);">$${high.toFixed(4)}</span>
                </div>
                <div style="display: flex; justify-content: space-between;">
                  <span style="color: rgba(255, 255, 255, 0.6); font-size: 11px;">L</span>
                  <span style="color: #ff4757; font-weight: 600; font-size: 11px; text-shadow: 0 0 6px rgba(255, 71, 87, 0.4);">$${low.toFixed(4)}</span>
                </div>
                <div style="display: flex; justify-content: space-between;">
                  <span style="color: rgba(255, 255, 255, 0.6); font-size: 11px;">C</span>
                  <span style="color: ${changeColor}; font-weight: 600; font-size: 11px; text-shadow: 0 0 6px ${glowColor};">$${close.toFixed(4)}</span>
                </div>
              </div>

              <!-- Change Section -->
              <div style="
                background: linear-gradient(135deg, ${glowColor} 0%, rgba(0, 0, 0, 0.3) 100%);
                border: 1px solid ${changeColor}40;
                border-radius: 8px;
                padding: 8px 12px;
                margin-bottom: 8px;
                display: flex;
                justify-content: space-between;
                align-items: center;
              ">
                <span style="color: rgba(255, 255, 255, 0.7); font-size: 11px; font-weight: 500;">Change</span>
                <div style="text-align: right;">
                  <div style="color: ${changeColor}; font-weight: 700; font-size: 13px; text-shadow: 0 0 8px ${glowColor};">
                    ${change >= 0 ? '+' : ''}$${change.toFixed(4)}
                  </div>
                  <div style="color: ${changeColor}; font-size: 10px; font-weight: 600; opacity: 0.8;">
                    ${change >= 0 ? '+' : ''}${changePercent}%
                  </div>
                </div>
              </div>

              <!-- Volume -->
              <div style="
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 4px 0;
                border-top: 1px solid rgba(0, 231, 182, 0.1);
              ">
                <span style="color: rgba(255, 255, 255, 0.5); font-size: 10px; font-weight: 500;">Volume</span>
                <span style="
                  color: rgba(255, 255, 255, 0.8);
                  font-size: 10px;
                  font-weight: 600;
                  font-family: 'SF Mono', monospace;
                ">
                  ${formattedVolume}
                </span>
              </div>
            </div>
          `;
        }
      },
      axisPointer: {
        link: { xAxisIndex: 'all' },
        label: {
          backgroundColor: '#777',
          formatter: (params: any) => {
            const date = new Date(params.value);

            // Use the same formatting logic as x-axis labels
            switch (activeTimeframe) {
              case '1m':
              case '5m':
              case '15m':
                return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
              case '1h':
              case '4h':
                return `${date.getHours().toString().padStart(2, '0')}:00\n${date.getMonth() + 1}/${date.getDate()}`;
              case '1d':
                return `${date.getMonth() + 1}/${date.getDate()}`;
              case '1M':
                return `${date.getMonth() + 1}/${date.getFullYear().toString().slice(-2)}`;
              case '1Y':
                return date.getFullYear().toString();
              default:
                return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
            }
          }
        }
      },
      grid: [
        {
          left: '3%',
          right: '5%',
          top: '8%',
          height: '70%',
          borderColor: 'rgba(0, 231, 182, 0.1)',
        },
        {
          left: '3%',
          right: '5%',
          top: '83%',
          height: '12%',
          borderColor: 'rgba(0, 231, 182, 0.1)',
        }
      ],
      xAxis: [
        {
          type: 'category',
          data: stockData.map(item =>
            typeof item.time === 'number' ? new Date(item.time * 1000).toISOString() : item.time
          ),
          scale: true,
          boundaryGap: false,
          axisLine: { lineStyle: { color: 'rgba(0, 231, 182, 0.1)' } },
          axisLabel: {
            color: 'rgba(0, 231, 182, 0.7)',
            fontFamily: "'Roboto Mono', monospace",
            fontSize: 10,
            formatter: (value: string) => {
              const date = new Date(value);

              // Format based on active timeframe for better readability
              switch (activeTimeframe) {
                case '1m':
                case '5m':
                case '15m':
                  // Show time for minute charts
                  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
                case '1h':
                case '4h':
                  // Show hour and date for hourly charts
                  return `${date.getHours().toString().padStart(2, '0')}:00\n${date.getMonth() + 1}/${date.getDate()}`;
                case '1d':
                  // Show month/day for daily charts
                  return `${date.getMonth() + 1}/${date.getDate()}`;
                case '1M':
                  // Show month/year for monthly charts
                  return `${date.getMonth() + 1}/${date.getFullYear().toString().slice(-2)}`;
                case '1Y':
                  // Show year for yearly charts
                  return date.getFullYear().toString();
                default:
                  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
              }
            }
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0, 231, 182, 0.07)',
              type: 'dashed'
            }
          },
          min: 'dataMin',
          max: 'dataMax'
        },
        {
          type: 'category',
          gridIndex: 1,
          data: stockData.map(item =>
            typeof item.time === 'number' ? new Date(item.time * 1000).toISOString() : item.time
          ),
          scale: true,
          boundaryGap: false,
          axisLine: { lineStyle: { color: 'rgba(0, 231, 182, 0.1)' } },
          axisLabel: { show: false },
          axisTick: { show: false },
          splitLine: { show: false },
          min: 'dataMin',
          max: 'dataMax'
        }
      ],
      yAxis: [
        {
          scale: true,
          splitNumber: 6,
          position: 'right',
          axisLine: { lineStyle: { color: 'rgba(0, 231, 182, 0.1)' } },
          axisLabel: {
            color: 'rgba(0, 231, 182, 0.7)',
            fontFamily: "'Roboto Mono', monospace",
            fontSize: 10,
            inside: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(0, 231, 182, 0.07)',
              type: 'dashed'
            }
          }
        },
        {
          scale: true,
          gridIndex: 1,
          position: 'right',
          splitNumber: 2,
          axisLabel: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
          splitLine: { show: false }
        }
      ],
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: [0, 1],
          start: 0,
          end: 100,
          minSpan: 5, // Minimum zoom level (5% of data)
          maxSpan: 100, // Maximum zoom level (100% of data)
          zoomOnMouseWheel: true,
          moveOnMouseMove: true,
          preventDefaultMouseMove: false,
          // Improved zoom handling to maintain drawings and prevent clipping
          onDataZoom: (params: any) => {
            console.log('DataZoom event:', params, 'Drawings count:', drawings.length);

            // Force chart to re-render drawings after zoom with multiple attempts
            const reRenderDrawings = () => {
              if (chartRef.current && drawings.length > 0) {
                console.log('Re-rendering drawings after zoom...');
                const newOption = getChartOptions();
                console.log('New option series count:', newOption.series.length);

                chartRef.current.setOption(newOption, {
                  notMerge: true, // Force complete re-render
                  lazyUpdate: false,
                  silent: false
                });

                // Force another update to ensure drawings stick
                setTimeout(() => {
                  if (chartRef.current) {
                    const finalOption = getChartOptions();
                    chartRef.current.setOption(finalOption, {
                      notMerge: false,
                      lazyUpdate: false
                    });
                  }
                }, 10);
              }
            };

            // Multiple attempts to ensure drawings persist
            setTimeout(reRenderDrawings, 10);
            setTimeout(reRenderDrawings, 100);

            // Auto-reset when zoomed too far out
            if (params.end - params.start > 95) {
              setTimeout(() => {
                if (chartRef.current) {
                  chartRef.current.dispatchAction({
                    type: 'dataZoom',
                    start: 70,
                    end: 100
                  });
                }
              }, 200);
            }
          }
        }
      ],
      series: [
        {
          name: 'Candle',
          type: 'candlestick',
          data: formattedData.map(item => item.slice(1, 5)),
          itemStyle: {
            color: '#00e7b6',
            color0: '#ffffff',
            borderColor: '#00e7b6',
            borderColor0: '#ffffff'
          },
          emphasis: {
            itemStyle: {
              color: '#00e7b6',
              color0: '#ffffff',
              borderColor: '#00e7b6',
              borderColor0: '#ffffff'
            }
          },
          markPoint: {
            data: drawings
              .filter(drawing => drawing.type === 'text')
              .map(drawing => ({
                coord: [drawing.point.time, drawing.point.price],
                value: drawing.text,
                itemStyle: {
                  color: '#00e7b6',
                  borderColor: '#ffffff',
                  borderWidth: 2
                },
                label: {
                  color: '#ffffff',
                  fontSize: 12,
                  fontWeight: 'bold',
                  backgroundColor: 'rgba(0, 231, 182, 0.8)',
                  borderRadius: 4,
                  padding: [4, 8]
                }
              }))
          }
        },
        {
          name: 'Volume',
          type: 'bar',
          xAxisIndex: 1,
          yAxisIndex: 1,
          data: formattedData.map((item, index) => {
            const color = stockData[index].close >= stockData[index].open ? '#00e7b6' : '#ffffff';
            return {
              value: item[5],
              itemStyle: {
                color: color,
                opacity: 0.5
              }
            };
          })
        },
        // Add drawing series
        ...createDrawingSeries()
      ]
    };
  };

  return (
    <div className="trading-chart-container">
      {/* Top toolbar */}
      <div className="chart-toolbar">
        <div className="toolbar-left">
          {/* Futuristic scanner search */}
          <div className="futuristic-scanner-search">
            <div className="scanner-input-wrapper">
              <div className="scanner-icon">⚡</div>
              <input
                type="text"
                className="scanner-input"
                placeholder="Enter symbol to scan..."
                value={currentSymbol}
                onChange={(e) => setCurrentSymbol(e.target.value.toUpperCase())}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    const input = e.currentTarget.value.trim().toUpperCase();
                    const symbolRegex = /^[A-Z]{1,5}$/;
                    if (symbolRegex.test(input)) {
                      setCurrentSymbol(input);
                    }
                  }
                }}
              />
              {isLoading && <FaSpinner className="scanner-loading" />}
            </div>
            <div className="scanner-status">
              <span className="status-indicator active"></span>
              <span className="status-text">LIVE SCAN</span>
            </div>
          </div>

          {/* Timeframe selector with comprehensive options */}
          <div className="timeframe-selector">
            <button
              className={activeTimeframe === '1m' ? 'active' : ''}
              onClick={() => {
                setActiveTimeframe('1m');
                fetchData('1m');
              }}
            >
              1m
            </button>
            <button
              className={activeTimeframe === '5m' ? 'active' : ''}
              onClick={() => {
                setActiveTimeframe('5m');
                fetchData('5m');
              }}
            >
              5m
            </button>
            <button
              className={activeTimeframe === '15m' ? 'active' : ''}
              onClick={() => {
                setActiveTimeframe('15m');
                fetchData('15m');
              }}
            >
              15m
            </button>
            <button
              className={activeTimeframe === '1h' ? 'active' : ''}
              onClick={() => {
                setActiveTimeframe('1h');
                fetchData('1h');
              }}
            >
              1h
            </button>
            <button
              className={activeTimeframe === '4h' ? 'active' : ''}
              onClick={() => {
                setActiveTimeframe('4h');
                fetchData('4h');
              }}
            >
              4h
            </button>
            <button
              className={activeTimeframe === '1d' ? 'active' : ''}
              onClick={() => {
                setActiveTimeframe('1d');
                fetchData('1d');
              }}
            >
              1D
            </button>
            <button
              className={activeTimeframe === '1M' ? 'active' : ''}
              onClick={() => {
                setActiveTimeframe('1M');
                fetchData('1M');
              }}
            >
              1M
            </button>
            <button
              className={activeTimeframe === '1Y' ? 'active' : ''}
              onClick={() => {
                setActiveTimeframe('1Y');
                fetchData('1Y');
              }}
            >
              1Y
            </button>
          </div>
        </div>

        {/* Chart controls (filters) moved to the right */}
        <div className="chart-controls">
          <div className="filters-dropdown">
            <button className="filters-button">
              <FaFilter />
              <span>Filters</span>
            </button>
            <div className="filters-content">
              <div className="filters-title">Chart Analysis Filters</div>
              <div className="filters-description">
                Toggle filters to highlight different market patterns. You can enable multiple filters simultaneously.
              </div>
              <div className="filter-options-container">
                <div className={`filter-option ${breakoutEnabled ? 'active' : ''}`}>
                  <div className="filter-option-label">
                    <FaRegLightbulb className="filter-option-icon" />
                    <span>Breakout</span>
                  </div>
                  <label className="toggle-switch">
                    <input
                      type="checkbox"
                      checked={breakoutEnabled}
                      onChange={() => setBreakoutEnabled(!breakoutEnabled)}
                    />
                    <span className="toggle-slider"></span>
                  </label>
                </div>

                <div className={`filter-option ${rangeEnabled ? 'active' : ''}`}>
                  <div className="filter-option-label">
                    <FaChartBar className="filter-option-icon" />
                    <span>Range</span>
                  </div>
                  <label className="toggle-switch">
                    <input
                      type="checkbox"
                      checked={rangeEnabled}
                      onChange={() => setRangeEnabled(!rangeEnabled)}
                    />
                    <span className="toggle-slider"></span>
                  </label>
                </div>

                <div className={`filter-option ${trendEnabled ? 'active' : ''}`}>
                  <div className="filter-option-label">
                    <FaChartLine className="filter-option-icon" />
                    <span>Trend</span>
                  </div>
                  <label className="toggle-switch">
                    <input
                      type="checkbox"
                      checked={trendEnabled}
                      onChange={() => setTrendEnabled(!trendEnabled)}
                    />
                    <span className="toggle-slider"></span>
                  </label>
                </div>
              </div>
            </div>
          </div>
          <div className="chart-controls-divider"></div>
          <button className="chart-button">
            <FaRegBell />
            <span>Alerts</span>
          </button>
          <div className="chart-controls-divider"></div>
          <button className="chart-button">
            <FaCog />
            <span>Settings</span>
          </button>
        </div>
      </div>

      {/* Chart container */}
      <div className="chart-content">
        {/* TradingView-style Tools Sidebar */}
        <div className="drawing-tools-sidebar">
          <div className="drawing-tools-content">
            {/* All tools in a single clean grid */}
            <div className="tool-grid">
              {drawingTools.map(tool => (
                <button
                  key={tool.id}
                  className={`tool-button ${activeTool === tool.id ? 'active' : ''}`}
                  onClick={() => handleToolSelect(tool.id)}
                  title={tool.name}
                >
                  <img src={tool.icon} alt={tool.name} className="tool-icon-image" />
                </button>
              ))}
            </div>

              {/* Help/Shortcuts Button */}
              <div className="tool-category">
                <div className="tool-grid">
                  <button
                    className={`tool-button ${showShortcuts ? 'active' : ''}`}
                    onClick={() => setShowShortcuts(!showShortcuts)}
                    title="Keyboard Shortcuts"
                  >
                    <span className="tool-icon-text">?</span>
                  </button>
                </div>
              </div>

              {/* Keyboard Shortcuts Panel */}
              {showShortcuts && (
                <div className="shortcuts-panel">
                  <div className="shortcuts-header">Shortcuts</div>
                  <div className="shortcuts-list">
                    <div className="shortcut-item">
                      <span className="shortcut-key">Esc</span>
                      <span className="shortcut-desc">Cancel</span>
                    </div>

                    <div className="shortcut-item">
                      <span className="shortcut-key">L</span>
                      <span className="shortcut-desc">Line</span>
                    </div>
                    <div className="shortcut-item">
                      <span className="shortcut-key">C</span>
                      <span className="shortcut-desc">Circle</span>
                    </div>
                    <div className="shortcut-item">
                      <span className="shortcut-key">R</span>
                      <span className="shortcut-desc">Rectangle</span>
                    </div>
                    <div className="shortcut-item">
                      <span className="shortcut-key">M</span>
                      <span className="shortcut-desc">Measure</span>
                    </div>
                    <div className="shortcut-item">
                      <span className="shortcut-key">F</span>
                      <span className="shortcut-desc">Fibonacci</span>
                    </div>
                    <div className="shortcut-item">
                      <span className="shortcut-key">X</span>
                      <span className="shortcut-desc">Risk/Reward</span>
                    </div>
                    <div className="shortcut-item">
                      <span className="shortcut-key">Ctrl+Del</span>
                      <span className="shortcut-desc">Clear All</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

        <div className="chart-area">
          <div className="chart-header">
            <div className="symbol-info">
              <span className="symbol">{currentSymbol}</span>
              <div className="volume-info">
                <span className="volume-label">Vol</span>
                <span className="volume-value">{(priceInfo.volume / 1000000).toFixed(2)}M</span>
                <span className={`volume-change ${priceInfo.close > priceInfo.open ? 'positive' : 'negative'}`}>
                  {((priceInfo.close - priceInfo.open) / priceInfo.open * 100).toFixed(2)}%
                </span>
              </div>
            </div>

            {/* Drawing Tool Status */}
            {(isDrawingMode || activeTool === 'delete') && activeTool && (
              <div className="drawing-status">
                <span className="drawing-status-text">
                  {activeTool === 'delete' ? 'Delete mode: Click on any drawing to delete it' :
                   isDrawing ? 'Drawing' : 'Click to start'}: {activeTool !== 'delete' ? drawingTools.find(tool => tool.id === activeTool)?.name : ''}
                  {isDrawing && (activeTool === 'line' || activeTool === 'rectangle') && (
                    <span className="drawing-instruction"> - Click again to finish</span>
                  )}
                </span>
                <button
                  className="cancel-drawing"
                  onClick={() => {
                    setActiveTool(null);
                    setIsDrawingMode(false);
                    setCurrentDrawing(null);
                    setIsDrawing(false);
                  }}
                >
                  Cancel
                </button>
              </div>
            )}
          </div>

          {error && <div className="error-message">{error}</div>}

          <div
            className={`chart-container ${isDrawingMode ? 'drawing-mode' : ''} ${activeTool === 'delete' ? 'delete-mode' : ''}`}
            onClick={handleContainerClick}
            onMouseMove={(event) => {
              // Always track mouse movement in drawing mode for continuous line preview
              if (isDrawingMode && activeTool && chartRef.current) {
                const rect = event.currentTarget.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;

                try {
                  // Try ECharts coordinate conversion first
                  const pointInPixel = [x, y];
                  const pointInGrid = chartRef.current.convertFromPixel('grid', pointInPixel);

                  let point = null;

                  if (pointInGrid && pointInGrid.length >= 2) {
                    const [timeIndex, price] = pointInGrid;
                    const clampedTimeIndex = Math.max(0, Math.min(Math.floor(timeIndex), stockData.length - 1));
                    const timeValue = stockData[clampedTimeIndex]?.time || stockData[0]?.time;
                    const actualTime = typeof timeValue === 'number' ? new Date(timeValue * 1000).toISOString() : timeValue;

                    point = {
                      time: actualTime,
                      price: price
                    };
                  } else {
                    // Fallback calculation when ECharts conversion fails
                    const chartContainer = event.currentTarget.querySelector('.echarts-for-react');
                    if (chartContainer && stockData.length > 0) {
                      const chartRect = chartContainer.getBoundingClientRect();
                      const containerRect = event.currentTarget.getBoundingClientRect();

                      // Calculate relative position within the chart
                      const relativeX = (x - (chartRect.left - containerRect.left)) / chartRect.width;
                      const relativeY = (y - (chartRect.top - containerRect.top)) / chartRect.height;

                      // Account for chart margins (approximately 3% left, 5% right, 8% top, 17% bottom)
                      const adjustedX = Math.max(0, Math.min(1, (relativeX - 0.03) / (1 - 0.03 - 0.05)));
                      const adjustedY = Math.max(0, Math.min(1, (relativeY - 0.08) / (1 - 0.08 - 0.17)));

                      const dataIndex = Math.floor(adjustedX * stockData.length);
                      const prices = stockData.map(d => [d.low, d.high]).flat();
                      const minPrice = Math.min(...prices);
                      const maxPrice = Math.max(...prices);
                      const price = maxPrice - (adjustedY * (maxPrice - minPrice));

                      const clampedDataIndex = Math.max(0, Math.min(dataIndex, stockData.length - 1));
                      const timeValue = stockData[clampedDataIndex]?.time || stockData[0]?.time;
                      const actualTime = typeof timeValue === 'number' ? new Date(timeValue * 1000).toISOString() : timeValue;

                      point = {
                        time: actualTime,
                        price: Math.max(minPrice, Math.min(price, maxPrice))
                      };
                    }
                  }

                  // Update current drawing preview if we have a valid point
                  if (point && isDrawing && currentDrawing) {
                    switch (activeTool) {
                      case 'line':
                        setCurrentDrawing(prev => ({
                          ...prev,
                          points: [prev.points[0], point]
                        }));
                        break;
                      case 'circle':
                        setCurrentDrawing(prev => ({
                          ...prev,
                          points: [prev.points[0], point]
                        }));
                        break;
                      case 'rectangle':
                        setCurrentDrawing(prev => ({
                          ...prev,
                          points: [prev.points[0], point]
                        }));
                        break;
                      case 'measure':
                      case 'fibonacci':
                      case 'rr':
                        setCurrentDrawing(prev => ({
                          ...prev,
                          points: [prev.points[0], point]
                        }));
                        break;
                    }
                  }
                } catch (error) {
                  // Silently handle coordinate conversion errors
                }
              }
            }}
          >
            <ReactECharts
              ref={(echarts) => {
                if (echarts) {
                  chartRef.current = echarts.getEchartsInstance();
                }
              }}
              option={getChartOptions()}
              style={{ height: '650px', width: '100%' }}
              opts={{ renderer: 'canvas' }}
              onEvents={{
                'click': (params: any, echarts: any) => {
                  console.log('=== CHART CLICK EVENT ===');
                  console.log('Chart clicked:', params, 'Drawing mode:', isDrawingMode, 'Active tool:', activeTool);
                  console.log('Click params details:', {
                    seriesName: params.seriesName,
                    seriesType: params.seriesType,
                    componentType: params.componentType,
                    dataIndex: params.dataIndex,
                    seriesIndex: params.seriesIndex
                  });
                  console.log('Full params object:', JSON.stringify(params, null, 2));

                  if ((isDrawingMode && activeTool) || activeTool === 'delete') {
                    handleChartClick(params, echarts);
                  }
                },
                'mouseover': (params: any) => {
                  if (activeTool === 'delete' && params.seriesName && params.seriesName.startsWith('Drawing_')) {
                    console.log('Hovering over drawing:', params.seriesName);
                  }
                },
                'mousemove': (params: any, echarts: any) => {
                  // Only track mouse movement when actively drawing to prevent tooltip conflicts
                  if (isDrawingMode && activeTool && isDrawing) {
                    handleChartMouseMove(params, echarts);
                  }
                },
                'globalout': () => {
                  // Clear current drawing preview when mouse leaves chart
                  if (isDrawing && currentDrawing && currentDrawing.points.length === 1) {
                    setCurrentDrawing(prev => ({
                      ...prev,
                      points: [prev.points[0]]
                    }));
                  }
                }
              }}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default TradingChart;

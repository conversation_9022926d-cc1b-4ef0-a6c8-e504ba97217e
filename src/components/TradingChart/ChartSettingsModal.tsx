import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>3, <PERSON><PERSON>, Eye, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Slider } from '@/components/ui/slider';

interface ChartSettings {
  // Candle Colors
  bullishCandleColor: string;
  bearishCandleColor: string;
  wickColor: string;
  
  // Chart Appearance
  backgroundColor: string;
  gridColor: string;
  textColor: string;
  
  // Chart Type & Style
  chartType: 'candle' | 'line' | 'area' | 'hollow_candle' | 'heikin_ashi';
  showVolume: boolean;
  showGrid: boolean;
  showCrosshair: boolean;
  
  // Technical Indicators
  showMA: boolean;
  maLength: number;
  showRSI: boolean;
  showMACD: boolean;
  showBollingerBands: boolean;
  
  // Price Scale
  priceScaleMode: 'normal' | 'logarithmic' | 'percentage';
  autoScale: boolean;
  
  // Time Scale
  timeFormat: '12h' | '24h';
  timezone: string;
  
  // Drawing Tools
  defaultLineColor: string;
  defaultLineWidth: number;
  snapToPrice: boolean;
  
  // Performance
  maxDataPoints: number;
  enableAnimations: boolean;
}

interface ChartSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  settings: ChartSettings;
  onSettingsChange: (settings: ChartSettings) => void;
}

const ChartSettingsModal: React.FC<ChartSettingsModalProps> = ({
  isOpen,
  onClose,
  settings,
  onSettingsChange
}) => {
  const [localSettings, setLocalSettings] = useState<ChartSettings>(settings);

  if (!isOpen) return null;

  const handleSettingChange = (key: keyof ChartSettings, value: any) => {
    const newSettings = { ...localSettings, [key]: value };
    setLocalSettings(newSettings);
  };

  const handleSave = () => {
    onSettingsChange(localSettings);
    onClose();
  };

  const handleReset = () => {
    const defaultSettings: ChartSettings = {
      bullishCandleColor: '#00e7b6',
      bearishCandleColor: '#ff4757',
      wickColor: '#ffffff',
      backgroundColor: '#0A0A0C',
      gridColor: 'rgba(255, 255, 255, 0.03)',
      textColor: 'rgba(255, 255, 255, 0.8)',
      chartType: 'candle',
      showVolume: true,
      showGrid: true,
      showCrosshair: true,
      showMA: false,
      maLength: 20,
      showRSI: false,
      showMACD: false,
      showBollingerBands: false,
      priceScaleMode: 'normal',
      autoScale: true,
      timeFormat: '24h',
      timezone: 'UTC',
      defaultLineColor: '#00e7b6',
      defaultLineWidth: 2,
      snapToPrice: true,
      maxDataPoints: 1000,
      enableAnimations: true
    };
    setLocalSettings(defaultSettings);
  };

  const colorPresets = [
    { name: 'Default', bull: '#00e7b6', bear: '#ff4757' },
    { name: 'Classic', bull: '#26a69a', bear: '#ef5350' },
    { name: 'TradingView', bull: '#089981', bear: '#f23645' },
    { name: 'Blue/Red', bull: '#2196f3', bear: '#f44336' },
    { name: 'Green/Orange', bull: '#4caf50', bear: '#ff9800' }
  ];

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="bg-[#141414] border border-white/[0.08] text-white max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <CardHeader className="flex flex-row items-center justify-between pb-4 border-b border-white/[0.08]">
          <CardTitle className="text-xl font-semibold flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Chart Settings
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0 text-white/60 hover:text-white hover:bg-white/10"
          >
            <X className="w-4 h-4" />
          </Button>
        </CardHeader>
        
        <CardContent className="p-0 overflow-y-auto max-h-[calc(90vh-120px)]">
          <Tabs defaultValue="appearance" className="w-full">
            <TabsList className="grid w-full grid-cols-4 bg-[#0A0A0A] border-b border-white/[0.08] rounded-none p-1">
              <TabsTrigger 
                value="appearance"
                className="data-[state=active]:bg-[#141414] data-[state=active]:text-white text-white/60 hover:text-white/80 transition-colors rounded-md"
              >
                <Palette className="w-4 h-4 mr-2" />
                Appearance
              </TabsTrigger>
              <TabsTrigger 
                value="indicators"
                className="data-[state=active]:bg-[#141414] data-[state=active]:text-white text-white/60 hover:text-white/80 transition-colors rounded-md"
              >
                <TrendingUp className="w-4 h-4 mr-2" />
                Indicators
              </TabsTrigger>
              <TabsTrigger 
                value="scale"
                className="data-[state=active]:bg-[#141414] data-[state=active]:text-white text-white/60 hover:text-white/80 transition-colors rounded-md"
              >
                <Grid className="w-4 h-4 mr-2" />
                Scale & Grid
              </TabsTrigger>
              <TabsTrigger 
                value="tools"
                className="data-[state=active]:bg-[#141414] data-[state=active]:text-white text-white/60 hover:text-white/80 transition-colors rounded-md"
              >
                <Eye className="w-4 h-4 mr-2" />
                Tools & Performance
              </TabsTrigger>
            </TabsList>

            <TabsContent value="appearance" className="p-6 space-y-6">
              {/* Chart Type */}
              <div className="space-y-3">
                <h3 className="text-lg font-medium text-white">Chart Type</h3>
                <div className="grid grid-cols-3 gap-3">
                  {[
                    { value: 'candle', label: 'Candlestick' },
                    { value: 'line', label: 'Line' },
                    { value: 'area', label: 'Area' },
                    { value: 'hollow_candle', label: 'Hollow Candle' },
                    { value: 'heikin_ashi', label: 'Heikin Ashi' }
                  ].map((type) => (
                    <Button
                      key={type.value}
                      variant={localSettings.chartType === type.value ? "default" : "outline"}
                      className={`${
                        localSettings.chartType === type.value 
                          ? 'bg-white text-black' 
                          : 'border-white/20 text-white/70 hover:bg-white/5'
                      }`}
                      onClick={() => handleSettingChange('chartType', type.value)}
                    >
                      {type.label}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Color Presets */}
              <div className="space-y-3">
                <h3 className="text-lg font-medium text-white">Color Presets</h3>
                <div className="grid grid-cols-5 gap-3">
                  {colorPresets.map((preset) => (
                    <Button
                      key={preset.name}
                      variant="outline"
                      className="border-white/20 text-white/70 hover:bg-white/5 flex flex-col items-center p-3 h-auto"
                      onClick={() => {
                        handleSettingChange('bullishCandleColor', preset.bull);
                        handleSettingChange('bearishCandleColor', preset.bear);
                      }}
                    >
                      <div className="flex gap-1 mb-2">
                        <div 
                          className="w-4 h-4 rounded" 
                          style={{ backgroundColor: preset.bull }}
                        />
                        <div 
                          className="w-4 h-4 rounded" 
                          style={{ backgroundColor: preset.bear }}
                        />
                      </div>
                      <span className="text-xs">{preset.name}</span>
                    </Button>
                  ))}
                </div>
              </div>

              {/* Custom Colors */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-white">Custom Colors</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-white/80">Bullish Candles</label>
                    <div className="flex items-center gap-3">
                      <input
                        type="color"
                        value={localSettings.bullishCandleColor}
                        onChange={(e) => handleSettingChange('bullishCandleColor', e.target.value)}
                        className="w-12 h-8 rounded border border-white/20 bg-transparent cursor-pointer"
                      />
                      <span className="text-sm text-white/60 font-mono">
                        {localSettings.bullishCandleColor}
                      </span>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-white/80">Bearish Candles</label>
                    <div className="flex items-center gap-3">
                      <input
                        type="color"
                        value={localSettings.bearishCandleColor}
                        onChange={(e) => handleSettingChange('bearishCandleColor', e.target.value)}
                        className="w-12 h-8 rounded border border-white/20 bg-transparent cursor-pointer"
                      />
                      <span className="text-sm text-white/60 font-mono">
                        {localSettings.bearishCandleColor}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="indicators" className="p-6 space-y-6">
              {/* Technical Indicators */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-white">Technical Indicators</h3>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-white/80">Moving Average</label>
                      <p className="text-xs text-white/60">Show simple moving average line</p>
                    </div>
                    <Switch
                      checked={localSettings.showMA}
                      onCheckedChange={(checked) => handleSettingChange('showMA', checked)}
                    />
                  </div>

                  {localSettings.showMA && (
                    <div className="ml-4 space-y-2">
                      <label className="text-sm font-medium text-white/80">MA Period: {localSettings.maLength}</label>
                      <Slider
                        value={[localSettings.maLength]}
                        onValueChange={(value) => handleSettingChange('maLength', value[0])}
                        max={200}
                        min={5}
                        step={1}
                        className="w-full"
                      />
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-white/80">RSI</label>
                      <p className="text-xs text-white/60">Relative Strength Index</p>
                    </div>
                    <Switch
                      checked={localSettings.showRSI}
                      onCheckedChange={(checked) => handleSettingChange('showRSI', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-white/80">MACD</label>
                      <p className="text-xs text-white/60">Moving Average Convergence Divergence</p>
                    </div>
                    <Switch
                      checked={localSettings.showMACD}
                      onCheckedChange={(checked) => handleSettingChange('showMACD', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-white/80">Bollinger Bands</label>
                      <p className="text-xs text-white/60">Price volatility bands</p>
                    </div>
                    <Switch
                      checked={localSettings.showBollingerBands}
                      onCheckedChange={(checked) => handleSettingChange('showBollingerBands', checked)}
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="scale" className="p-6 space-y-6">
              {/* Price Scale */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-white">Price Scale</h3>

                <div className="space-y-3">
                  <label className="text-sm font-medium text-white/80">Scale Mode</label>
                  <div className="grid grid-cols-3 gap-3">
                    {[
                      { value: 'normal', label: 'Normal' },
                      { value: 'logarithmic', label: 'Logarithmic' },
                      { value: 'percentage', label: 'Percentage' }
                    ].map((mode) => (
                      <Button
                        key={mode.value}
                        variant={localSettings.priceScaleMode === mode.value ? "default" : "outline"}
                        className={`${
                          localSettings.priceScaleMode === mode.value
                            ? 'bg-white text-black'
                            : 'border-white/20 text-white/70 hover:bg-white/5'
                        }`}
                        onClick={() => handleSettingChange('priceScaleMode', mode.value)}
                      >
                        {mode.label}
                      </Button>
                    ))}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <label className="text-sm font-medium text-white/80">Auto Scale</label>
                    <p className="text-xs text-white/60">Automatically adjust price range</p>
                  </div>
                  <Switch
                    checked={localSettings.autoScale}
                    onCheckedChange={(checked) => handleSettingChange('autoScale', checked)}
                  />
                </div>
              </div>

              {/* Grid & Display */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-white">Grid & Display</h3>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-white/80">Show Grid</label>
                      <p className="text-xs text-white/60">Display chart grid lines</p>
                    </div>
                    <Switch
                      checked={localSettings.showGrid}
                      onCheckedChange={(checked) => handleSettingChange('showGrid', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-white/80">Show Crosshair</label>
                      <p className="text-xs text-white/60">Display crosshair cursor</p>
                    </div>
                    <Switch
                      checked={localSettings.showCrosshair}
                      onCheckedChange={(checked) => handleSettingChange('showCrosshair', checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-white/80">Show Volume</label>
                      <p className="text-xs text-white/60">Display volume bars</p>
                    </div>
                    <Switch
                      checked={localSettings.showVolume}
                      onCheckedChange={(checked) => handleSettingChange('showVolume', checked)}
                    />
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="tools" className="p-6 space-y-6">
              {/* Drawing Tools */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-white">Drawing Tools</h3>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-white/80">Default Line Color</label>
                    <div className="flex items-center gap-3">
                      <input
                        type="color"
                        value={localSettings.defaultLineColor}
                        onChange={(e) => handleSettingChange('defaultLineColor', e.target.value)}
                        className="w-12 h-8 rounded border border-white/20 bg-transparent cursor-pointer"
                      />
                      <span className="text-sm text-white/60 font-mono">
                        {localSettings.defaultLineColor}
                      </span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium text-white/80">Line Width: {localSettings.defaultLineWidth}px</label>
                    <Slider
                      value={[localSettings.defaultLineWidth]}
                      onValueChange={(value) => handleSettingChange('defaultLineWidth', value[0])}
                      max={10}
                      min={1}
                      step={1}
                      className="w-full"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-white/80">Snap to Price</label>
                      <p className="text-xs text-white/60">Snap drawing tools to price levels</p>
                    </div>
                    <Switch
                      checked={localSettings.snapToPrice}
                      onCheckedChange={(checked) => handleSettingChange('snapToPrice', checked)}
                    />
                  </div>
                </div>
              </div>

              {/* Performance */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-white">Performance</h3>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-white/80">Max Data Points: {localSettings.maxDataPoints}</label>
                    <Slider
                      value={[localSettings.maxDataPoints]}
                      onValueChange={(value) => handleSettingChange('maxDataPoints', value[0])}
                      max={5000}
                      min={100}
                      step={100}
                      className="w-full"
                    />
                    <p className="text-xs text-white/60">Higher values may impact performance</p>
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <label className="text-sm font-medium text-white/80">Enable Animations</label>
                      <p className="text-xs text-white/60">Smooth chart transitions</p>
                    </div>
                    <Switch
                      checked={localSettings.enableAnimations}
                      onCheckedChange={(checked) => handleSettingChange('enableAnimations', checked)}
                    />
                  </div>
                </div>
              </div>

              {/* Time Format */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium text-white">Time & Date</h3>

                <div className="space-y-3">
                  <label className="text-sm font-medium text-white/80">Time Format</label>
                  <div className="grid grid-cols-2 gap-3">
                    {[
                      { value: '12h', label: '12 Hour' },
                      { value: '24h', label: '24 Hour' }
                    ].map((format) => (
                      <Button
                        key={format.value}
                        variant={localSettings.timeFormat === format.value ? "default" : "outline"}
                        className={`${
                          localSettings.timeFormat === format.value
                            ? 'bg-white text-black'
                            : 'border-white/20 text-white/70 hover:bg-white/5'
                        }`}
                        onClick={() => handleSettingChange('timeFormat', format.value)}
                      >
                        {format.label}
                      </Button>
                    ))}
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>

        {/* Footer Actions */}
        <div className="flex items-center justify-between p-6 border-t border-white/[0.08]">
          <Button
            variant="outline"
            onClick={handleReset}
            className="border-white/20 text-white/70 hover:bg-white/5"
          >
            Reset to Defaults
          </Button>
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              className="border-white/20 text-white/70 hover:bg-white/5"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              className="bg-white text-black hover:bg-white/90"
            >
              Apply Settings
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ChartSettingsModal;

import React, { useState, useEffect } from 'react';
import { X, Star, Play, ShoppingCart, BarChart3, MessageSquare, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import StarRating from '@/components/ui/star-rating';
import ReviewSummary from '@/components/reviews/ReviewSummary';
import ReviewCard from '@/components/reviews/ReviewCard';
import WriteReviewModal from '@/components/reviews/WriteReviewModal';
import PublicBacktestCard from '@/components/reviews/PublicBacktestCard';
import { MarketplaceAgent } from '@/services/marketplaceService';
import { 
  getAgentReviews, 
  getAgentReviewSummary, 
  getAgentPublicBacktests,
  AgentReview,
  ReviewSummary as ReviewSummaryType,
  PublicBacktest
} from '@/services/reviewService';
import { useToast } from '@/components/ui/use-toast';

interface AgentDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  agent: MarketplaceAgent;
  onPurchase?: (agent: MarketplaceAgent) => void;
  onBacktest?: (agent: MarketplaceAgent) => void;
  purchasing?: boolean;
}

const AgentDetailsModal: React.FC<AgentDetailsModalProps> = ({
  isOpen,
  onClose,
  agent,
  onPurchase,
  onBacktest,
  purchasing = false
}) => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('overview');
  const [reviews, setReviews] = useState<AgentReview[]>([]);
  const [reviewSummary, setReviewSummary] = useState<ReviewSummaryType | null>(null);
  const [publicBacktests, setPublicBacktests] = useState<PublicBacktest[]>([]);
  const [loading, setLoading] = useState(false);
  const [showWriteReview, setShowWriteReview] = useState(false);

  useEffect(() => {
    if (isOpen && agent) {
      loadAgentData();
    }
  }, [isOpen, agent]);

  const loadAgentData = async () => {
    if (!agent) return;

    try {
      setLoading(true);
      
      // Load all data in parallel
      const [reviewsResult, summaryResult, backtestsResult] = await Promise.allSettled([
        getAgentReviews(agent.id, 10, 0),
        getAgentReviewSummary(agent.id),
        getAgentPublicBacktests(agent.id, 5)
      ]);

      if (reviewsResult.status === 'fulfilled') {
        setReviews(reviewsResult.value.reviews);
      }

      if (summaryResult.status === 'fulfilled') {
        setReviewSummary(summaryResult.value);
      }

      if (backtestsResult.status === 'fulfilled') {
        setPublicBacktests(backtestsResult.value);
      }
    } catch (error) {
      console.error('Error loading agent data:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load agent details"
      });
    } finally {
      setLoading(false);
    }
  };

  const handlePurchase = () => {
    onPurchase?.(agent);
  };

  const handleBacktest = () => {
    onBacktest?.(agent);
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(price);
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="bg-[#1A1A1A] border-white/[0.08] text-white max-w-4xl max-h-[90vh] overflow-hidden p-0">
          {/* Header */}
          <div className="p-6 border-b border-white/[0.08]">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h2 className="text-2xl font-bold text-white">{agent.name}</h2>
                  {agent.price && agent.price > 0 ? (
                    <Badge className="bg-green-500/20 text-green-400 border border-green-500/30">
                      {formatPrice(agent.price)}
                    </Badge>
                  ) : (
                    <Badge className="bg-blue-500/20 text-blue-400 border border-blue-500/30">
                      Free
                    </Badge>
                  )}
                </div>
                
                {reviewSummary && (
                  <div className="flex items-center gap-2 mb-2">
                    <StarRating rating={reviewSummary.average_rating} size="sm" showValue />
                    <span className="text-white/60 text-sm">
                      ({reviewSummary.total_reviews} {reviewSummary.total_reviews === 1 ? 'review' : 'reviews'})
                    </span>
                  </div>
                )}

                <p className="text-white/80 text-sm">{agent.description}</p>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="text-white/60 hover:text-white hover:bg-white/[0.04]"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>

            {/* Action Buttons */}
            <div className="flex gap-3 mt-4">
              <Button
                onClick={handleBacktest}
                variant="outline"
                className="border-white/[0.12] text-white/70 hover:text-white hover:bg-white/[0.04]"
              >
                <BarChart3 className="w-4 h-4 mr-2" />
                Try Backtest
              </Button>

              {agent.price && agent.price > 0 ? (
                <Button
                  onClick={handlePurchase}
                  disabled={purchasing}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  {purchasing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Purchasing...
                    </>
                  ) : (
                    <>
                      <ShoppingCart className="w-4 h-4 mr-2" />
                      Buy for {formatPrice(agent.price)}
                    </>
                  )}
                </Button>
              ) : (
                <Button
                  onClick={handlePurchase}
                  disabled={purchasing}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  {purchasing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Getting...
                    </>
                  ) : (
                    <>
                      <Play className="w-4 h-4 mr-2" />
                      Get Free Agent
                    </>
                  )}
                </Button>
              )}

              <Button
                onClick={() => setShowWriteReview(true)}
                variant="outline"
                className="border-white/[0.12] text-white/70 hover:text-white hover:bg-white/[0.04]"
              >
                <MessageSquare className="w-4 h-4 mr-2" />
                Write Review
              </Button>
            </div>
          </div>

          {/* Content Tabs */}
          <div className="flex-1 overflow-hidden">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
              <TabsList className="bg-white/[0.02] border-b border-white/[0.08] rounded-none p-0 h-auto justify-start">
                <TabsTrigger
                  value="overview"
                  className="data-[state=active]:bg-white/[0.08] data-[state=active]:text-white text-white/60 hover:text-white/80 rounded-none border-b-2 border-transparent data-[state=active]:border-white/20 px-6 py-3 transition-colors"
                >
                  Overview
                </TabsTrigger>
                <TabsTrigger
                  value="reviews"
                  className="data-[state=active]:bg-white/[0.08] data-[state=active]:text-white text-white/60 hover:text-white/80 rounded-none border-b-2 border-transparent data-[state=active]:border-white/20 px-6 py-3 transition-colors"
                >
                  Reviews ({reviewSummary?.total_reviews || 0})
                </TabsTrigger>
                <TabsTrigger
                  value="backtests"
                  className="data-[state=active]:bg-white/[0.08] data-[state=active]:text-white text-white/60 hover:text-white/80 rounded-none border-b-2 border-transparent data-[state=active]:border-white/20 px-6 py-3 transition-colors"
                >
                  Public Backtests ({publicBacktests.length})
                </TabsTrigger>
              </TabsList>

              <div className="flex-1 overflow-y-auto">
                <TabsContent value="overview" className="p-6 space-y-4 m-0">
                  {reviewSummary && <ReviewSummary summary={reviewSummary} />}
                  
                  <div className="bg-white/[0.02] border border-white/[0.08] rounded-lg p-4">
                    <h3 className="text-white font-medium mb-2">Agent Details</h3>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-white/60">Category:</span>
                        <span className="text-white">{agent.category || 'Uncategorized'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-white/60">Downloads:</span>
                        <span className="text-white">{agent.download_count || 0}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-white/60">Created:</span>
                        <span className="text-white">
                          {new Date(agent.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="reviews" className="p-6 space-y-4 m-0">
                  {reviews.length > 0 ? (
                    reviews.map((review) => (
                      <ReviewCard
                        key={review.id}
                        review={review}
                        onHelpfulnessUpdate={loadAgentData}
                      />
                    ))
                  ) : (
                    <div className="text-center py-12">
                      <MessageSquare className="w-12 h-12 text-white/40 mx-auto mb-4" />
                      <h4 className="text-white/80 font-medium mb-2">No reviews yet</h4>
                      <p className="text-white/60 text-sm mb-6">Be the first to share your experience with this agent</p>
                      <Button
                        onClick={() => setShowWriteReview(true)}
                        variant="outline"
                        className="border-white/20 text-white/70 hover:bg-white/5 hover:text-white"
                      >
                        <MessageSquare className="w-4 h-4 mr-2" />
                        Write Review
                      </Button>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="backtests" className="p-6 space-y-4 m-0">
                  {publicBacktests.length > 0 ? (
                    publicBacktests.map((backtest) => (
                      <PublicBacktestCard key={backtest.id} backtest={backtest} />
                    ))
                  ) : (
                    <div className="text-center py-12">
                      <TrendingUp className="w-12 h-12 text-white/40 mx-auto mb-4" />
                      <h4 className="text-white/80 font-medium mb-2">No public backtests yet</h4>
                      <p className="text-white/60 text-sm">Community members haven't shared backtest results for this agent yet</p>
                    </div>
                  )}
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </DialogContent>
      </Dialog>

      {/* Write Review Modal */}
      <WriteReviewModal
        isOpen={showWriteReview}
        onClose={() => setShowWriteReview(false)}
        agentId={agent.id}
        agentName={agent.name}
        onSuccess={() => {
          setShowWriteReview(false);
          loadAgentData();
        }}
      />
    </>
  );
};

export default AgentDetailsModal;

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Shield, 
  Users, 
  AlertTriangle, 
  Activity, 
  Ban, 
  CheckCircle,
  XCircle,
  Trash2,
  Eye,
  Settings
} from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import {
  isCurrentUserAdmin,
  getAdminActionLogs,
  getUserBans,
  getBadWords,
  type AdminActionLog,
  type UserBan,
  type BadWord
} from '@/services/adminService';
import { formatDistanceToNow } from 'date-fns';

const AdminDashboard: React.FC = () => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [loading, setLoading] = useState(true);
  const [actionLogs, setActionLogs] = useState<AdminActionLog[]>([]);
  const [userBans, setUserBans] = useState<UserBan[]>([]);
  const [badWords, setBadWords] = useState<BadWord[]>([]);
  const { toast } = useToast();

  useEffect(() => {
    checkAdminStatus();
  }, []);

  const checkAdminStatus = async () => {
    try {
      const adminStatus = await isCurrentUserAdmin();
      setIsAdmin(adminStatus);
      
      if (adminStatus) {
        await loadAdminData();
      }
    } catch (error) {
      console.error('Error checking admin status:', error);
      toast({
        title: "Error",
        description: "Failed to verify admin status",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const loadAdminData = async () => {
    try {
      const [logs, bans, words] = await Promise.all([
        getAdminActionLogs(50),
        getUserBans(true),
        getBadWords()
      ]);

      setActionLogs(logs);
      setUserBans(bans);
      setBadWords(words);
    } catch (error) {
      console.error('Error loading admin data:', error);
      toast({
        title: "Error",
        description: "Failed to load admin data",
        variant: "destructive"
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!isAdmin) {
    return (
      <Card className="bg-gradient-to-br from-red-500/10 to-orange-500/10 border-red-500/20">
        <CardContent className="p-8 text-center">
          <AlertTriangle className="w-12 h-12 text-red-400 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-white mb-2">Access Denied</h3>
          <p className="text-white/70">
            You don't have administrator privileges to access this dashboard.
          </p>
        </CardContent>
      </Card>
    );
  }

  const getActionIcon = (actionType: string) => {
    switch (actionType) {
      case 'verify_agent':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'unverify_agent':
        return <XCircle className="w-4 h-4 text-yellow-400" />;
      case 'ban_user':
        return <Ban className="w-4 h-4 text-red-400" />;
      case 'unban_user':
        return <CheckCircle className="w-4 h-4 text-green-400" />;
      case 'delete_agent':
        return <Trash2 className="w-4 h-4 text-red-400" />;
      default:
        return <Activity className="w-4 h-4 text-blue-400" />;
    }
  };

  const getBanTypeColor = (banType: string) => {
    switch (banType) {
      case 'full':
        return 'bg-red-600';
      case 'temporary':
        return 'bg-yellow-600';
      case 'marketplace':
        return 'bg-orange-600';
      default:
        return 'bg-gray-600';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high':
        return 'bg-red-600';
      case 'medium':
        return 'bg-yellow-600';
      case 'low':
        return 'bg-green-600';
      default:
        return 'bg-gray-600';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <Shield className="w-8 h-8 text-blue-400" />
        <div>
          <h1 className="text-2xl font-bold text-white">Admin Dashboard</h1>
          <p className="text-white/60">Manage users, agents, and content moderation</p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-br from-blue-500/10 to-blue-600/10 border-blue-500/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Activity className="w-8 h-8 text-blue-400" />
              <div>
                <p className="text-sm text-white/60">Recent Actions</p>
                <p className="text-2xl font-bold text-white">{actionLogs.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-red-500/10 to-red-600/10 border-red-500/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Ban className="w-8 h-8 text-red-400" />
              <div>
                <p className="text-sm text-white/60">Active Bans</p>
                <p className="text-2xl font-bold text-white">{userBans.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-yellow-500/10 to-yellow-600/10 border-yellow-500/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <AlertTriangle className="w-8 h-8 text-yellow-400" />
              <div>
                <p className="text-sm text-white/60">Filtered Words</p>
                <p className="text-2xl font-bold text-white">{badWords.filter(w => w.is_active).length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-500/10 to-green-600/10 border-green-500/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Shield className="w-8 h-8 text-green-400" />
              <div>
                <p className="text-sm text-white/60">System Status</p>
                <p className="text-lg font-bold text-green-400">Active</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="actions" className="space-y-4">
        <TabsList className="bg-white/5 border-white/10">
          <TabsTrigger value="actions" className="data-[state=active]:bg-white/10">
            Recent Actions
          </TabsTrigger>
          <TabsTrigger value="bans" className="data-[state=active]:bg-white/10">
            User Bans
          </TabsTrigger>
          <TabsTrigger value="content" className="data-[state=active]:bg-white/10">
            Content Filter
          </TabsTrigger>
          <TabsTrigger value="agents" className="data-[state=active]:bg-white/10">
            Agent Management
          </TabsTrigger>
        </TabsList>

        <TabsContent value="actions" className="space-y-4">
          <Card className="bg-white/[0.02] border-white/[0.08]">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Activity className="w-5 h-5" />
                Recent Admin Actions
              </CardTitle>
              <CardDescription>
                Latest administrative actions performed in the system
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {actionLogs.length === 0 ? (
                  <p className="text-white/60 text-center py-8">No recent actions</p>
                ) : (
                  actionLogs.slice(0, 20).map((log) => (
                    <div
                      key={log.id}
                      className="flex items-center gap-3 p-3 bg-white/[0.02] rounded-lg border border-white/[0.05]"
                    >
                      {getActionIcon(log.action_type)}
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="text-white font-medium">
                            {log.action_type.replace('_', ' ').toUpperCase()}
                          </span>
                          <Badge variant="outline" className="text-xs">
                            {log.target_type}
                          </Badge>
                        </div>
                        <p className="text-white/60 text-sm">
                          by {log.admin_name} • {formatDistanceToNow(new Date(log.created_at), { addSuffix: true })}
                        </p>
                        {log.notes && (
                          <p className="text-white/50 text-xs mt-1">{log.notes}</p>
                        )}
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bans" className="space-y-4">
          <Card className="bg-white/[0.02] border-white/[0.08]">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Ban className="w-5 h-5" />
                Active User Bans
              </CardTitle>
              <CardDescription>
                Users currently banned from the platform
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {userBans.length === 0 ? (
                  <p className="text-white/60 text-center py-8">No active bans</p>
                ) : (
                  userBans.map((ban) => (
                    <div
                      key={ban.id}
                      className="flex items-center gap-3 p-3 bg-white/[0.02] rounded-lg border border-white/[0.05]"
                    >
                      <Ban className="w-4 h-4 text-red-400" />
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <span className="text-white font-medium">{ban.user_email}</span>
                          <Badge className={`text-xs ${getBanTypeColor(ban.ban_type)}`}>
                            {ban.ban_type}
                          </Badge>
                        </div>
                        <p className="text-white/60 text-sm">
                          Reason: {ban.reason}
                        </p>
                        <p className="text-white/50 text-xs">
                          by {ban.admin_name} • {formatDistanceToNow(new Date(ban.created_at), { addSuffix: true })}
                          {ban.expires_at && ` • Expires ${formatDistanceToNow(new Date(ban.expires_at), { addSuffix: true })}`}
                        </p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="content" className="space-y-4">
          <Card className="bg-white/[0.02] border-white/[0.08]">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <AlertTriangle className="w-5 h-5" />
                Content Filter Words
              </CardTitle>
              <CardDescription>
                Words and phrases filtered from agent names and descriptions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {badWords.filter(w => w.is_active).map((word) => (
                  <div
                    key={word.id}
                    className="flex items-center gap-2 p-2 bg-white/[0.02] rounded border border-white/[0.05]"
                  >
                    <span className="text-white text-sm flex-1">{word.word}</span>
                    <Badge className={`text-xs ${getSeverityColor(word.severity)}`}>
                      {word.severity}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="agents" className="space-y-4">
          <Card className="bg-white/[0.02] border-white/[0.08]">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Settings className="w-5 h-5" />
                Agent Management
              </CardTitle>
              <CardDescription>
                Verify agents and manage marketplace content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-white/60 text-center py-8">
                Agent management tools will be available in the marketplace interface
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AdminDashboard;

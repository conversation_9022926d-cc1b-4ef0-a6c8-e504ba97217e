import React, { useState, useRef, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, AlertCircle, Send, DollarSign } from 'lucide-react';
import { generateAgentWithAI, formatAIAgentForBuilder } from '@/services/aiAgentService';
import { AgentBlock } from '@/services/agentService';
import { cn } from '@/lib/utils';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import AgentPricingModal from '@/components/marketplace/AgentPricingModal';


interface AIAgentBuilderProps {
  onAgentGenerated: (agent: {
    blocks: AgentBlock[];
    entryBlockId: string;
    name: string;
    description: string;
  }) => void;
  userId: string;
  currentAgent?: any; // Add current agent prop for pricing
}



// Define message types
interface UserMessage {
  type: 'user';
  text: string;
}

interface AIMessage {
  type: 'ai';
  text: string;
  blocks?: AgentBlock[]; // Optional: include suggested blocks
  reasoning?: string;
  error?: string;
}

type ChatMessage = UserMessage | AIMessage;

const AIAgentBuilder: React.FC<AIAgentBuilderProps> = ({ onAgentGenerated, userId, currentAgent }) => {
  const [inputDescription, setInputDescription] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      type: 'ai',
      text: "Hi! I'll help you build simple trading agents that actually work. Just tell me what you want in plain English:\n\n• 'Buy when RSI is oversold'\n• 'Trade near support levels'\n• 'Use candlestick patterns'\n\nI'll create a clean, working agent for you! 🚀"
    }
  ]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [loadingText, setLoadingText] = useState('Creating your simple agent...');
  const [showWelcomeMessage, setShowWelcomeMessage] = useState(false);

  // Marketplace pricing modal state
  const [showPricingModal, setShowPricingModal] = useState(false);

  // State for typing animation
  const [displayedAiText, setDisplayedAiText] = useState('');
  const [displayedReasoningText, setDisplayedReasoningText] = useState('');
  const [fullAiText, setFullAiText] = useState('');
  const [fullReasoningText, setFullReasoningText] = useState('');

  // State for input suggestions
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [filteredSuggestions, setFilteredSuggestions] = useState<string[]>([]);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);

  // State for status feedback
  const [statusMessage, setStatusMessage] = useState('');

  // Simple, working strategy suggestions
  const suggestions = [
    "Buy when RSI is oversold (below 30)",
    "Trade with moving averages",
    "Use stochastic oscillator signals",
    "Trade bullish candlestick patterns",
    "Buy on price breakouts",
    "Trade with high volume spikes",
    "Use Williams %R indicator",
    "Trade CCI signals"
  ];

  // Trigger welcome message fade-in animation
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowWelcomeMessage(true);
    }, 300); // Small delay for smooth entrance
    return () => clearTimeout(timer);
  }, []);

  // Auto-scroll to the latest message
  useEffect(() => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [messages]);

  // Effect for AI response typing animation
  useEffect(() => {
    if (fullAiText) {
      setDisplayedAiText(''); // Reset displayed text
      setDisplayedReasoningText(''); // Reset reasoning text

      // Start with a small delay to ensure state is reset
      setTimeout(() => {
        let i = 0;
        const typingInterval = setInterval(() => {
          if (i < fullAiText.length) {
            setDisplayedAiText(fullAiText.substring(0, i + 1)); // Use substring instead of concatenation
            i++;
            // Auto-scroll during typing
            if (chatContainerRef.current) {
              chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
            }
          } else {
            clearInterval(typingInterval);
            // Start typing reasoning text after main text is done
            if (fullReasoningText) {
              let j = 0;
              const reasoningInterval = setInterval(() => {
                if (j < fullReasoningText.length) {
                  setDisplayedReasoningText(fullReasoningText.substring(0, j + 1)); // Use substring instead of concatenation
                  j++;
                  // Auto-scroll during typing
                  if (chatContainerRef.current) {
                    chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
                  }
                } else {
                  clearInterval(reasoningInterval);
                }
              }, 15); // Slightly faster typing for reasoning
            }
          }
        }, 20); // Typing speed (ms per character)

        return () => {
          clearInterval(typingInterval);
        };
      }, 50); // Small initial delay
    }
  }, [fullAiText, fullReasoningText]);

  // Effect to change loading text
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (isGenerating) {
      setLoadingText('Creating your simple agent...'); // Reset text when generation starts
      timer = setTimeout(() => {
        setLoadingText('Almost done, adding the finishing touches...'); // Ensure correct string with space
      }, 1800); // Change text after 1.8 seconds
    } else {
      setLoadingText('Creating your simple agent...'); // Reset text when generation finishes
    }
    return () => clearTimeout(timer); // Clean up timer
  }, [isGenerating]);

  // Handle input change with suggestions
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setInputDescription(value);

    // Filter suggestions based on input
    if (value.length > 2) {
      const filtered = suggestions.filter(suggestion =>
        suggestion.toLowerCase().includes(value.toLowerCase())
      );
      setFilteredSuggestions(filtered.slice(0, 5)); // Show max 5 suggestions
      setShowSuggestions(filtered.length > 0);
      setSelectedSuggestionIndex(-1);
    } else {
      setShowSuggestions(false);
      setFilteredSuggestions([]);
    }
  };

  // Handle suggestion selection - auto-run the suggestion
  const handleSuggestionClick = (suggestion: string) => {
    setInputDescription(suggestion);
    setShowSuggestions(false);
    setFilteredSuggestions([]);

    // Auto-run the suggestion
    setTimeout(() => {
      const userMessage: UserMessage = { type: 'user', text: suggestion };
      setMessages((prevMessages) => [...prevMessages, userMessage]);
      setInputDescription(''); // Clear input after sending

      setIsGenerating(true);
      setError(null);

      // Build conversation history for suggestion clicks too
      const conversationHistory = messages
        .filter((_, index) => index > 0) // Skip welcome message
        .map(msg => `${msg.type === 'user' ? 'User' : 'Assistant'}: ${msg.text}`)
        .concat([`User: ${suggestion}`]); // Add current suggestion

      generateAgentWithAI({
        description: suggestion,
        userId,
        currentAgent: currentAgent,
        conversationHistory: conversationHistory
      }).then(result => {
        if (result.success && result.agent) {
          const summaryText = `Great! I've created a simple ${result.agent.name} for you.`;
          const followUpText = `It's ready to use on your canvas - you can test it or ask me to adjust it!`;

          const aiMessage: AIMessage = {
            type: 'ai',
            text: `${summaryText} ${followUpText}`,
            blocks: result.agent.blocks,
            reasoning: result.reasoning,
          };
          setMessages((prevMessages) => {
            const newMessages = [...prevMessages, aiMessage];
            setFullAiText(aiMessage.text);
            setFullReasoningText(aiMessage.reasoning || '');
            return newMessages;
          });

          const formattedAgent = formatAIAgentForBuilder(result.agent);
          onAgentGenerated(formattedAgent);
        } else {
          const aiErrorMessage: AIMessage = {
            type: 'ai',
            text: result.error || 'Failed to generate agent.',
            error: result.error || 'Failed to generate agent.',
          };
          setMessages((prevMessages) => [...prevMessages, aiErrorMessage]);
          setError(result.error || 'Failed to generate agent');
        }
      }).catch(err => {
        console.error('Error generating agent:', err);
        const aiErrorMessage: AIMessage = {
          type: 'ai',
          text: 'An unexpected error occurred while generating the agent.',
          error: 'An unexpected error occurred while generating the agent',
        };
        setMessages((prevMessages) => [...prevMessages, aiErrorMessage]);
        setError('An unexpected error occurred while generating the agent');
      }).finally(() => {
        setIsGenerating(false);
      });
    }, 100); // Small delay to show the selection
  };

  // Handle keyboard navigation for suggestions
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (showSuggestions) {
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setSelectedSuggestionIndex(prev =>
          prev < filteredSuggestions.length - 1 ? prev + 1 : prev
        );
      } else if (e.key === 'ArrowUp') {
        e.preventDefault();
        setSelectedSuggestionIndex(prev => prev > 0 ? prev - 1 : -1);
      } else if (e.key === 'Enter' && selectedSuggestionIndex >= 0) {
        e.preventDefault();
        handleSuggestionClick(filteredSuggestions[selectedSuggestionIndex]);
      } else if (e.key === 'Escape') {
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
      }
    } else if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault(); // Prevent newline
      handleSendMessage();
    }
  };

  // Handle clear chat
  const handleClearChat = () => {
    const welcomeMessage: AIMessage = {
      type: 'ai',
      text: "Hi! I'll help you build simple trading agents that actually work. Just tell me what you want in plain English:\n\n• 'Buy when RSI is oversold'\n• 'Trade near support levels'\n• 'Use candlestick patterns'\n\nI'll create a clean, working agent for you! 🚀"
    };
    setMessages([welcomeMessage]);
    setLoadingText('Creating your simple agent...'); // Reset loading text
    setError(null); // Clear any errors
    setStatusMessage('Chat cleared');
    setTimeout(() => setStatusMessage(''), 2000);
  };

  const handleSendMessage = async () => {
    if (!inputDescription.trim() || isGenerating) return;

    const userMessage: UserMessage = { type: 'user', text: inputDescription.trim() };
    setMessages((prevMessages) => [...prevMessages, userMessage]);
    const strategyDescription = inputDescription.trim();
    setInputDescription(''); // Clear input after sending

    setIsGenerating(true);
    setError(null);

    try {
      // Build conversation history from messages (excluding the welcome message)
      const conversationHistory = messages
        .filter((_, index) => index > 0) // Skip welcome message
        .map(msg => `${msg.type === 'user' ? 'User' : 'Assistant'}: ${msg.text}`)
        .concat([`User: ${strategyDescription}`]); // Add current message

      const result = await generateAgentWithAI({
        description: strategyDescription,
        userId,
        currentAgent: currentAgent, // Pass current agent for editing context
        conversationHistory: conversationHistory // Pass conversation history
      });

      if (result.success && result.agent) {
        // Craft a user-friendly AI response
        const summaryText = `Perfect! I've created a simple ${result.agent.name} for you.`;
        const followUpText = `The agent is now on your canvas - you can test it right away or ask me to modify it if needed!`;

        const aiMessage: AIMessage = {
          type: 'ai',
          text: `${summaryText} ${followUpText}`,
          blocks: result.agent.blocks, // Pass blocks to the message
          reasoning: result.reasoning,
        };
        setMessages((prevMessages) => { // Update messages and set full text for typing
          const newMessages = [...prevMessages, aiMessage];
          setFullAiText(aiMessage.text); // Set the full text for the typing effect
          setFullReasoningText(aiMessage.reasoning || ''); // Set the full reasoning text
          return newMessages;
        });



        // Automatically load the agent into the builder when AI generates one
        const formattedAgent = formatAIAgentForBuilder(result.agent);
        onAgentGenerated(formattedAgent);

      } else {
        const aiErrorMessage: AIMessage = {
          type: 'ai',
          text: result.error || 'Failed to generate agent.',
          error: result.error || 'Failed to generate agent.',
        };
        setMessages((prevMessages) => [...prevMessages, aiErrorMessage]);
        setError(result.error || 'Failed to generate agent'); // Still set error for visibility
      }
    } catch (err) {
      console.error('Error generating agent:', err);
      const aiErrorMessage: AIMessage = {
        type: 'ai',
        text: 'An unexpected error occurred while generating the agent.',
        error: 'An unexpected error occurred while generating the agent',
      };
      setMessages((prevMessages) => [...prevMessages, aiErrorMessage]);
      setError('An unexpected error occurred while generating the agent');
    } finally {
      setIsGenerating(false);
    }
  };





  return (
    <div className="h-full flex flex-col bg-[#0A0A0A]">
      {/* Redesigned Clean Header */}
      <div className="relative bg-gradient-to-r from-[#0A0A0A] via-[#0D0D0D] to-[#0A0A0A] border-b border-[#1A1A1A]/40">
        <div className="flex items-center justify-between px-6 py-4">
          {/* Logo & Brand Section */}
          <div className="flex items-center gap-3">
            <div className="relative">
              <img
                src="http://thecodingkid.oyosite.com/logo_only.png"
                alt="Osis Logo"
                className="h-7 w-7 drop-shadow-sm"
              />
              <div className="absolute -inset-1 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
            <div className="flex flex-col">
              <span className="text-sm font-semibold text-white tracking-tight leading-none">Osis</span>
              <span className="text-[10px] text-white/40 font-medium tracking-wide">AI Agent Builder</span>
            </div>
          </div>

          {/* Action Section */}
          <div className="flex items-center justify-end gap-2">
            {/* Set Price Button - Show only for owned agents */}
            {currentAgent && currentAgent.user_id && (
              <button
                onClick={() => setShowPricingModal(true)}
                className="flex items-center gap-1 px-2 py-1 text-green-400 hover:text-green-300 text-[10px] font-medium transition-colors duration-150 border border-green-500/20 rounded hover:bg-green-500/10"
              >
                <DollarSign className="w-3 h-3" />
                Set Price
              </button>
            )}
            {/* Clear Chat Button - Always visible, ultra clean and compact */}
            <button
              onClick={handleClearChat}
              className="px-2 py-1 text-white/70 hover:text-white/90 text-[10px] font-medium transition-colors duration-150"
            >
              Clear
            </button>
          </div>
        </div>

        {/* Subtle bottom glow */}
        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/10 to-transparent"></div>
      </div>

      {/* Chat Container - Flexbox layout to keep input at bottom */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Chat Area - Flexible height that grows with available space */}
              <div
                ref={chatContainerRef}
                className="flex-1 overflow-y-auto p-4 space-y-4 custom-scrollbar"
                style={{
                  paddingBottom: '20px' // Extra padding at bottom for better UX
                }}
              >
                {/* Removed Example Prompts */}

                {messages.map((msg, index) => {
                  const isWelcomeMessage = index === 0 && msg.text.includes("Hi! I'm here to help");
                  return (
                    <div key={index} className={cn(
                      "flex w-full transition-all duration-700 ease-out",
                      msg.type === 'user' ? 'justify-end' : 'justify-start',
                      isWelcomeMessage && !showWelcomeMessage ? 'opacity-0 translate-y-2' : 'opacity-100 translate-y-0'
                    )}>
                      <div className={cn(
                        "p-2 rounded-lg max-w-[85%]", // Reduced padding and max-width
                         msg.type === 'user' ? 'bg-[#1A1A1A] text-white' : 'bg-[#141414] text-white/80'
                       )}>
                         {msg.type === 'user' ? (
                           // Render user message
                           <p className="text-xs">{msg.text}</p>
                         ) : ( /* Render AI message content */
                           <div className="space-y-1"> {/* Adjusted space-y for smaller bubble */}
                             <div className="text-[10px] text-blue-300 font-semibold mb-1">Osis Builder Agent</div> {/* AI Label */}
                             <p className="text-xs">{messages.length > 0 && messages[messages.length - 1] === msg && displayedAiText ? displayedAiText : msg.text}</p>
                           {msg.reasoning && msg.type === 'ai' && (
                             <p className="text-[10px] text-white/60 mt-1">
                               {messages.length > 0 && messages[messages.length - 1] === msg ? displayedReasoningText : msg.reasoning}
                             </p>
                           )}
                           {msg.blocks && msg.type === 'ai' && (
                             <Card className="bg-[#0A0A0A] border-[#1A1A1A]">
                               <CardHeader className="p-2"><CardTitle className="text-xs">Suggested Blocks</CardTitle></CardHeader>
                               <CardContent className="p-2">
                                 <div className="flex flex-wrap gap-1"> {/* Reduced gap */}
                                   {msg.blocks.map(block => (
                                     <Badge key={block.id} variant="secondary" className="bg-[#1A1A1A] text-white/80 border-[#303035] text-[10px] px-1 py-0"> {/* Reduced badge text size and padding */}
                                       {block.type}
                                     </Badge>
                                   ))}
                                 </div>
                               </CardContent>
                             </Card>
                           )}
                           {msg.error && msg.type === 'ai' && (
                             <Alert variant="destructive" className="mt-1 p-1.5 text-[10px]"> {/* Reduced alert padding/text size */}
                               <AlertCircle className="h-2.5 w-2.5" /> {/* Reduced icon size */}
                               <AlertTitle className="text-xs">Error</AlertTitle>
                               <AlertDescription className="text-[10px]">{msg.error}</AlertDescription>
                             </Alert>
                           )}
                         </div>
                       )}
                     </div>
                    </div>
                  );
                })}

                {isGenerating && (
                  <div className="flex justify-start">
                    <div className="flex items-center space-x-2 px-2 py-1.5">
                      <Loader2 className="h-3 w-3 animate-spin text-white/70" />
                      <span className="wave-text text-xs text-white/80">{loadingText.split('').map((char, index) => (
                        <span key={index} style={{ '--i': index } as React.CSSProperties}>{char === ' ' ? '\u00A0' : char}</span>
                      ))}</span>
                    </div>
                  </div>
                )}

                 {error && !isGenerating && (
                  <div className="flex justify-start">
                     <div className="p-3 rounded-xl max-w-[80%"> {/* Increased rounded corners */}
                        <Alert variant="destructive">
                          <AlertCircle className="h-4 w-4" />
                          <AlertTitle>Error</AlertTitle>
                          <AlertDescription>{error}</AlertDescription>
                        </Alert>
                     </div>
                  </div>
                 )}

              </div>

              {/* Status Message */}
              {statusMessage && (
                <div className="px-3 py-1.5 text-center text-xs transition-all duration-300 bg-blue-500/20 text-blue-400 border-t border-blue-500/30">
                  {statusMessage}
                </div>
              )}

              {/* Input Area - Fixed at bottom using flexbox */}
              <div className="flex-shrink-0 p-3 border-t border-[#1A1A1A]/30 bg-[#0A0A0A]">
                <div className="relative">
                  <Textarea
                    value={inputDescription}
                    onChange={handleInputChange}
                    onKeyDown={handleKeyDown}
                    placeholder="What should your agent do? (e.g., 'buy when RSI is oversold' or 'trade bullish patterns')"
                    className="w-full bg-[#141414] border-[#1A1A1A] text-white rounded-lg p-2 pr-8 text-xs resize-none min-h-[40px] max-h-[120px] custom-scrollbar focus-visible:ring-0 focus-visible:ring-offset-0"
                    disabled={isGenerating}
                  />

                  {/* Suggestions Dropdown - Refined compact styling */}
                  {showSuggestions && filteredSuggestions.length > 0 && (
                    <div className="absolute bottom-full left-0 right-0 mb-2 bg-[#1A1A1A]/95 backdrop-blur-sm border border-[#303035]/50 rounded-lg shadow-xl z-50 max-h-36 overflow-hidden">
                      <div className="px-3 py-1.5 border-b border-[#303035]/30">
                        <span className="text-xs text-white/50 font-medium">Suggestions</span>
                      </div>
                      <div className="max-h-32 overflow-y-auto custom-scrollbar">
                        {filteredSuggestions.map((suggestion, index) => (
                          <div
                            key={index}
                            onClick={() => handleSuggestionClick(suggestion)}
                            className={`px-3 py-2 text-sm cursor-pointer transition-colors duration-100 ${
                              index === selectedSuggestionIndex
                                ? 'bg-[#2A2A2A] text-white'
                                : 'text-white/70'
                            }`}
                          >
                            <div className="flex items-center gap-2.5">
                              <div className={`w-1.5 h-1.5 rounded-full transition-colors duration-200 ${
                                index === selectedSuggestionIndex
                                  ? 'bg-green-400 shadow-sm shadow-green-400/50'
                                  : 'bg-green-500/60'
                              }`}></div>
                              <span className="leading-snug">{suggestion}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                      <div className="px-3 py-1.5 border-t border-[#303035]/30 bg-[#161616]">
                        <span className="text-xs text-white/40">↑↓ navigate • Enter select • Esc close</span>
                      </div>
                    </div>
                  )}
                </div>

                <Button
                  onClick={handleSendMessage}
                  disabled={!inputDescription.trim() || isGenerating}
                  className="absolute bottom-[10px] right-3 text-white/70 hover:text-white hover:bg-[#1A1A1A]/60 rounded-md p-1 transition-colors disabled:opacity-50"
                  size="icon"
                  variant="ghost"
                >
                  <Send className="h-5 w-5" />
                </Button>
              </div>
      </div>

              {/* Custom Styles for Wave Text Animation */}
              <style>{`
                .wave-text span {
                  animation: gentleWave 3.5s ease-in-out infinite;
                  animation-delay: calc(var(--i) * 0.2s);
                  display: inline-block;
                }

                @keyframes gentleWave {
                  0%, 100% {
                    opacity: 0.4;
                  }
                  50% {
                    opacity: 0.8;
                  }
                }
              `}</style>

              {/* Custom Styles for icon button alignment */}
              <style>{`
                .items-end .absolute {
                  bottom: 0.75rem; /* Adjust bottom position based on padding */
                }
              `}</style>

      {/* Marketplace Pricing Modal */}
      {currentAgent && (
        <AgentPricingModal
          isOpen={showPricingModal}
          onClose={() => setShowPricingModal(false)}
          agentId={currentAgent.id}
          agentName={currentAgent.name}
          currentPrice={currentAgent.price}
          currentIsForSale={currentAgent.is_for_sale}
          onSuccess={() => {
            setShowPricingModal(false);
            // Optionally refresh agent data here
          }}
        />
      )}
    </div>
  );
};

export default AIAgentBuilder;

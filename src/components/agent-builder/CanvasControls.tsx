import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  ZoomIn, 
  ZoomOut, 
  Maximize, 
  RotateCcw, 
  Grid3X3, 
  Layers,
  Map,
  AlignLeft,
  Shuffle
} from 'lucide-react';
import { useReactFlow } from 'reactflow';
import { AgentBlock } from '@/services/agentService';

interface CanvasControlsProps {
  blocks: AgentBlock[];
  onAutoArrange: () => void;
  onZoomToFit: () => void;
  onResetView: () => void;
  onToggleGrid: () => void;
  onToggleMinimap: () => void;
  onToggleLayers: () => void;
  showGrid: boolean;
  showMinimap: boolean;
  showLayers: boolean;
}

const CanvasControls: React.FC<CanvasControlsProps> = ({
  blocks,
  onAutoArrange,
  onZoomToFit,
  onResetView,
  onToggleGrid,
  onToggleMinimap,
  onToggleLayers,
  showGrid,
  showMinimap,
  showLayers
}) => {
  const { zoomIn, zoomOut, fitView } = useReactFlow();

  const handleZoomIn = () => {
    zoomIn({ duration: 300 });
  };

  const handleZoomOut = () => {
    zoomOut({ duration: 300 });
  };

  const handleFitView = () => {
    fitView({ 
      duration: 500, 
      padding: 0.1,
      includeHiddenNodes: false 
    });
  };

  return (
    <TooltipProvider>
      <div className="absolute top-4 right-4 z-10 flex flex-col gap-2 bg-white/90 backdrop-blur-sm rounded-lg p-2 border border-gray-200 shadow-lg">
        {/* Zoom Controls */}
        <div className="flex gap-1">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={handleZoomIn}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Zoom In</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={handleZoomOut}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Zoom Out</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={handleFitView}
              >
                <Maximize className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Fit to View</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Layout Controls */}
        <div className="flex gap-1 border-t pt-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={onAutoArrange}
                disabled={blocks.length === 0}
              >
                <AlignLeft className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Auto Arrange Blocks</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={onResetView}
              >
                <RotateCcw className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Reset View</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* View Controls */}
        <div className="flex gap-1 border-t pt-2">
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant={showGrid ? "default" : "ghost"}
                size="icon"
                className="h-8 w-8"
                onClick={onToggleGrid}
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Toggle Grid</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant={showLayers ? "default" : "ghost"}
                size="icon"
                className="h-8 w-8"
                onClick={onToggleLayers}
              >
                <Layers className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Toggle Layer View</p>
            </TooltipContent>
          </Tooltip>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant={showMinimap ? "default" : "ghost"}
                size="icon"
                className="h-8 w-8"
                onClick={onToggleMinimap}
              >
                <Map className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent side="left">
              <p>Toggle Mini-map</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Block Count Indicator */}
        {blocks.length > 0 && (
          <div className="text-xs text-gray-500 text-center border-t pt-2">
            {blocks.length} block{blocks.length !== 1 ? 's' : ''}
          </div>
        )}
      </div>
    </TooltipProvider>
  );
};

export default CanvasControls;

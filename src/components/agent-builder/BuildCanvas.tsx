import React, { useCallback, useRef, useState } from 'react';
import React<PERSON>low, {
  Background,
  Controls,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  NodeTypes,
  EdgeTypes,
  NodeChange,
  EdgeChange,
  ConnectionLineType,
  Panel,
  MiniMap
} from 'reactflow';
import 'reactflow/dist/style.css';
import './agent-builder.css';
import { AgentBlock } from '@/services/agentService';
import { BlockType, ConnectionType } from '@/hooks/useAgentBuilder';
import IndicatorNode from './blocks/IndicatorBlock';
import PriceNode from './blocks/PriceBlock';
import FundamentalNode from './blocks/FundamentalBlock';
import ConditionNode from './blocks/ConditionBlock';
import ComparisonNode from './blocks/ComparisonBlock';
import TriggerNode from './blocks/TriggerBlock';
import OperatorNode from './blocks/OperatorBlock';
import WhenRunNode from './blocks/WhenRunBlock';
import BullishConfidenceBoostNode from './blocks/BullishConfidenceBoostBlock';
import BearishConfidenceBoostNode from './blocks/BearishConfidenceBoostBlock';
import ConfidenceBoostNode from './blocks/ConfidenceBoostBlock';
import CandlePatternNode from './blocks/CandlePatternBlock';
import StockSentimentNode from './blocks/StockSentimentBlock';
import ChartPatternNode from './blocks/ChartPatternBlock';
import BreakoutDetectionNode from './blocks/BreakoutDetectionBlock';
import GapAnalysisNode from './blocks/GapAnalysisBlock';
import ConsoleLogNode from './blocks/ConsoleLogBlock';
import MomentumIndicatorNode from './blocks/MomentumIndicatorBlock';
import MovingAverageNode from './blocks/MovingAverageBlock';
import SignalNode from './blocks/SignalBlock';
import GenericNode from './blocks/GenericBlock';
import TechnicalIndicatorBlock from './blocks/TechnicalIndicatorBlock';
import SignalGenerationBlock from './blocks/SignalGenerationBlock';
import LogicFlowBlock from './blocks/LogicFlowBlock';
import MarketAnalysisBlock from './blocks/MarketAnalysisBlock';

import { Button } from '@/components/ui/button';
import { ZoomIn, ZoomOut, Maximize, X } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

// Define custom node types
const nodeTypes: NodeTypes = {
  [BlockType.WHEN_RUN]: WhenRunNode,
  [BlockType.INDICATOR]: IndicatorNode,
  [BlockType.PRICE]: PriceNode,
  [BlockType.FUNDAMENTAL]: FundamentalNode,
  [BlockType.CONDITION]: ConditionNode,
  [BlockType.COMPARISON]: ComparisonNode,
  [BlockType.TRIGGER]: TriggerNode,
  [BlockType.OPERATOR]: OperatorNode,
  [BlockType.BULLISH_CONFIDENCE_BOOST]: BullishConfidenceBoostNode,
  [BlockType.BEARISH_CONFIDENCE_BOOST]: BearishConfidenceBoostNode,
  [BlockType.CONFIDENCE_BOOST]: ConfidenceBoostNode,
  [BlockType.CANDLE_PATTERN]: CandlePatternNode,
  [BlockType.STOCK_SENTIMENT]: StockSentimentNode,
  [BlockType.CHART_PATTERN]: ChartPatternNode,
  [BlockType.BREAKOUT_DETECTION]: BreakoutDetectionNode,
  [BlockType.GAP_ANALYSIS]: GapAnalysisNode,
  [BlockType.CONSOLE_LOG]: ConsoleLogNode,
  [BlockType.MOMENTUM_INDICATOR]: MomentumIndicatorNode,
  [BlockType.MOVING_AVERAGE]: MovingAverageNode,
  [BlockType.SIGNAL]: SignalNode,

  // Technical Indicators - Use dedicated components
  [BlockType.TREND_INDICATOR]: TechnicalIndicatorBlock,
  [BlockType.VOLUME_INDICATOR]: TechnicalIndicatorBlock,
  [BlockType.VOLATILITY_INDICATOR]: TechnicalIndicatorBlock,

  // Market Analysis Blocks
  [BlockType.SUPPORT_RESISTANCE]: MarketAnalysisBlock,
  [BlockType.TREND_LINE_ANALYSIS]: MarketAnalysisBlock,
  [BlockType.MARKET_STRUCTURE]: MarketAnalysisBlock,

  // Signal Generation Blocks
  [BlockType.PRICE_ACTION_SIGNAL]: SignalGenerationBlock,
  [BlockType.MULTI_TIMEFRAME_ANALYSIS]: SignalGenerationBlock,
  [BlockType.DIVERGENCE_DETECTION]: SignalGenerationBlock,
  [BlockType.VOLUME_CONFIRMATION]: SignalGenerationBlock,
  [BlockType.MARKET_REGIME]: SignalGenerationBlock,

  // Logic & Flow Control Blocks
  [BlockType.IF_THEN_ELSE]: LogicFlowBlock,
  [BlockType.AND_OPERATOR]: LogicFlowBlock,
  [BlockType.OR_OPERATOR]: LogicFlowBlock,
  [BlockType.NOT_OPERATOR]: LogicFlowBlock,
  [BlockType.SIGNAL_CONFIRMATION]: LogicFlowBlock,
  [BlockType.TIME_FILTER]: LogicFlowBlock,
  [BlockType.MARKET_CONDITION_FILTER]: LogicFlowBlock,

  // Legacy blocks - Use GenericNode for backward compatibility
  [BlockType.RISK_LIMIT]: GenericNode,
  [BlockType.MARKET_BREADTH]: GenericNode,
  [BlockType.MULTI_TIMEFRAME]: GenericNode,
  [BlockType.CONFLUENCE]: GenericNode,
  [BlockType.SCALE_STRATEGY]: GenericNode,
  [BlockType.PARTIAL_PROFIT]: GenericNode,
  [BlockType.AND]: GenericNode,
  [BlockType.OR]: GenericNode
};

interface BuildCanvasProps {
  blocks: AgentBlock[];
  connections: any[];
  entryBlockId: string;
  onBlockUpdate: (id: string, properties: Partial<AgentBlock>) => void;
  onBlockRemove: (id: string) => void;
  onConnectionAdd: (connection: any) => boolean;
  onConnectionRemove: (connection: any) => void;
  onSetEntryBlock: (id: string) => boolean;
  addBlock: (type: string, position: { x: number; y: number }) => string;
  errorDetails?: Record<string, string[]>;
  disconnectedBlocks?: AgentBlock[];
  onFullscreenToggle?: () => void;
  isFullscreen?: boolean;
}

const BuildCanvas: React.FC<BuildCanvasProps> = ({
  blocks,
  connections,
  entryBlockId,
  onBlockUpdate,
  onBlockRemove,
  onConnectionAdd,
  onConnectionRemove,
  onSetEntryBlock,
  addBlock,
  errorDetails = {},
  disconnectedBlocks = [],
  onFullscreenToggle,
  isFullscreen = false
}) => {
  const reactFlowWrapper = useRef<HTMLDivElement>(null);

  // Convert blocks to ReactFlow nodes
  const initialNodes: Node[] = blocks.map((block, index) => {
    const hasError = !!errorDetails[block.id];
    const isDisconnected = disconnectedBlocks.some(b => b.id === block.id);

    // Ensure position is valid with better default spacing
    const position = block.position || { x: 0, y: 0 };
    const validPosition = {
      x: typeof position.x === 'number' && !isNaN(position.x) ? position.x : 150 + (index * 200),
      y: typeof position.y === 'number' && !isNaN(position.y) ? position.y : 150 + (index * 150)
    };

    return {
      id: block.id,
      type: block.type,
      position: validPosition,
      data: {
        ...block,
        isEntryBlock: block.id === entryBlockId,
        onUpdate: (properties: Partial<AgentBlock>) => onBlockUpdate(block.id, properties),
        onRemove: () => onBlockRemove(block.id),
        onSetAsEntry: () => onSetEntryBlock(block.id),
        hasError,
        isDisconnected,
        errorMessages: errorDetails[block.id] || []
      },
      className: hasError ? 'error-node' : ''
    };
  });

  // Convert connections to ReactFlow edges
  const initialEdges: Edge[] = connections.map((conn, index) => ({
    id: `e-${index}`,
    source: conn.sourceId,
    target: conn.targetId,
    sourceHandle: conn.sourceHandle,
    targetHandle: conn.targetHandle,
    type: 'default',
    animated: true
  }));

  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);
  const [isDragging, setIsDragging] = useState<boolean>(false);

  // Store the ReactFlow instance
  const [reactFlowInstance, setReactFlowInstance] = React.useState<any>(null);



  // Update nodes when blocks change - more responsive updates
  React.useEffect(() => {
    // Allow updates during dragging for more responsive UI

    // Save current viewport before updating nodes
    let currentViewport = { x: 0, y: 0, zoom: 1 };
    if (reactFlowInstance) {
      currentViewport = reactFlowInstance.getViewport();
    }

    // Only update if blocks have changed
    const updatedNodes = blocks.map(block => {
      const hasError = !!errorDetails[block.id];
      const isDisconnected = disconnectedBlocks.some(b => b.id === block.id);

      // Find the existing node to preserve its position
      const existingNode = nodes.find(node => node.id === block.id);

      return {
        id: block.id,
        type: block.type,
        // Preserve existing node position if available
        position: existingNode?.position || block.position,
        data: {
          ...block,
          isEntryBlock: block.id === entryBlockId,
          onUpdate: (properties: Partial<AgentBlock>) => onBlockUpdate(block.id, properties),
          onRemove: () => onBlockRemove(block.id),
          onSetAsEntry: () => onSetEntryBlock(block.id),
          hasError,
          isDisconnected,
          errorMessages: errorDetails[block.id] || []
        },
        className: hasError ? 'error-node' : ''
      };
    });

    // Only update if there are blocks to show
    if (updatedNodes.length > 0) {
      setNodes(updatedNodes);

      // Restore viewport immediately
      if (reactFlowInstance) {
        reactFlowInstance.setViewport(currentViewport);
      }
    }
  }, [blocks, entryBlockId, onBlockUpdate, onBlockRemove, onSetEntryBlock, errorDetails, disconnectedBlocks, reactFlowInstance]);



  // Organize blocks in a hierarchical layout
  const organizeBlocks = useCallback(() => {
    console.log("Organizing blocks...");
    if (!reactFlowInstance || blocks.length === 0) {
      console.log("No ReactFlow instance or blocks");
      return;
    }

    // Find the entry block (usually the "When Run" block)
    const entryBlock = blocks.find(block => block.id === entryBlockId);
    if (!entryBlock) {
      console.log("No entry block found");
      return;
    }

    // Create a map of blocks by ID for quick lookup
    const blocksMap = blocks.reduce((map, block) => {
      map[block.id] = block;
      return map;
    }, {} as Record<string, AgentBlock>);

    // Build a graph of connections
    const graph: Record<string, string[]> = {};
    blocks.forEach(block => {
      graph[block.id] = [];
    });

    // Populate the graph with connections
    blocks.forEach(block => {
      if ('outputConnections' in block) {
        const outputConnections = (block as any).outputConnections || [];
        outputConnections.forEach((targetId: string) => {
          if (graph[block.id]) {
            graph[block.id].push(targetId);
          }
        });
      }
    });

    // Perform a breadth-first traversal to organize blocks by level
    const visited = new Set<string>();
    const levels: string[][] = []; // Array of levels, each containing block IDs
    const queue: { id: string; level: number }[] = [{ id: entryBlock.id, level: 0 }];

    visited.add(entryBlock.id);

    while (queue.length > 0) {
      const { id, level } = queue.shift()!;

      // Ensure the level exists in our levels array
      if (!levels[level]) {
        levels[level] = [];
      }

      // Add the block to its level
      levels[level].push(id);

      // Add all unvisited neighbors to the queue
      const neighbors = graph[id] || [];
      for (const neighborId of neighbors) {
        if (!visited.has(neighborId)) {
          visited.add(neighborId);
          queue.push({ id: neighborId, level: level + 1 });
        }
      }
    }

    // Add any blocks that weren't visited (disconnected blocks) to the last level
    const unvisitedBlocks = blocks.filter(block => !visited.has(block.id));
    if (unvisitedBlocks.length > 0) {
      const lastLevel = levels.length;
      levels[lastLevel] = unvisitedBlocks.map(block => block.id);
    }

    // Calculate new positions for each block - left to right flow with better spacing
    const LEVEL_HORIZONTAL_SPACING = 400; // Increased horizontal spacing between levels
    const BLOCK_VERTICAL_SPACING = 300; // Increased vertical spacing between blocks in the same level
    const START_X_OFFSET = 150; // Starting X offset to avoid edge of canvas
    const START_Y_OFFSET = 150; // Increased Y offset for more space at the top

    // Calculate new positions for each block
    const newPositions: Record<string, { x: number; y: number }> = {};

    levels.forEach((levelBlocks, levelIndex) => {
      const levelHeight = levelBlocks.length * BLOCK_VERTICAL_SPACING;
      const startY = -levelHeight / 2 + BLOCK_VERTICAL_SPACING / 2 + START_Y_OFFSET;

      levelBlocks.forEach((blockId, blockIndex) => {
        newPositions[blockId] = {
          x: START_X_OFFSET + levelIndex * LEVEL_HORIZONTAL_SPACING, // Levels go from left to right
          y: startY + blockIndex * BLOCK_VERTICAL_SPACING // Blocks in same level stacked vertically
        };
      });
    });

    // Update block positions
    const updatedBlocks = blocks.map(block => {
      if (newPositions[block.id]) {
        return {
          ...block,
          position: newPositions[block.id]
        };
      }
      return block;
    });

    // Update all blocks at once
    console.log("Updating block positions...");
    updatedBlocks.forEach(block => {
      onBlockUpdate(block.id, { position: block.position });
    });

    // Center the view on the organized blocks
    console.log("Fitting view...");
    setTimeout(() => {
      if (reactFlowInstance) {
        reactFlowInstance.fitView({ padding: 0.2 });
        console.log("Organization complete");
      }
    }, 100);
  }, [blocks, entryBlockId, onBlockUpdate, reactFlowInstance]);

  // Handle window resize without auto-rescaling
  React.useEffect(() => {
    // Only add the event listener if reactFlowInstance is initialized
    if (!reactFlowInstance) return;

    const handleResize = () => {
      // Instead of fitView, just update the flow to ensure it renders correctly
      const currentViewport = reactFlowInstance.getViewport();
      reactFlowInstance.setViewport(currentViewport);
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [reactFlowInstance]);

  // Track if this is the first load
  const isFirstLoad = React.useRef(true);

  // Organize blocks only when component first mounts
  React.useEffect(() => {
    if (!reactFlowInstance || blocks.length === 0) return;

    // Only organize blocks on first load
    if (isFirstLoad.current) {
      // Use a short delay to ensure ReactFlow is fully initialized
      const timer = setTimeout(() => {
        // Automatically organize blocks when loaded
        console.log("Auto-organizing blocks on first load");
        organizeBlocks();
        isFirstLoad.current = false;
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [reactFlowInstance, blocks.length, organizeBlocks]); // Include dependencies but use isFirstLoad to control execution

  // Update edges when connections change - improved synchronization
  React.useEffect(() => {
    // Create edges with consistent IDs to avoid duplication
    const updatedEdges = connections.map((conn, index) => ({
      id: `e-${conn.sourceId}-${conn.targetId}-${conn.sourceHandle}-${conn.targetHandle}`,
      source: conn.sourceId,
      target: conn.targetId,
      sourceHandle: conn.sourceHandle,
      targetHandle: conn.targetHandle,
      type: 'smoothstep',
      animated: true,
      style: {
        strokeWidth: 2,
        stroke: 'rgba(255, 255, 255, 0.25)',
        strokeLinecap: 'round'
      }
      // No markerEnd - clean lines without arrows
    }));

    // Update edges state
    setEdges(updatedEdges);
  }, [connections, setEdges]);

  // Handle node changes (position, etc.)
  const handleNodesChange = useCallback(
    (changes: NodeChange[]) => {
      // Apply changes to the nodes state immediately
      onNodesChange(changes);

      // Track dragging state and update block positions
      changes.forEach(change => {
        if (change.type === 'position') {
          // Update dragging state
          if (change.dragging !== undefined) {
            setIsDragging(change.dragging);
          }

          // Update block position immediately during and after dragging for responsive feedback
          if (change.position) {
            onBlockUpdate(change.id, { position: change.position });
          }
        }
      });
    },
    [onNodesChange, onBlockUpdate]
  );

  // Handle edge changes - simplified to fix connection issues
  const handleEdgesChange = useCallback(
    (changes: EdgeChange[]) => {
      // Save current viewport before making changes
      let currentViewport = { x: 0, y: 0, zoom: 1 };
      if (reactFlowInstance) {
        currentViewport = reactFlowInstance.getViewport();
      }

      // Apply changes to the edges state
      onEdgesChange(changes);

      // Handle edge removal
      changes.forEach(change => {
        if (change.type === 'remove') {
          // Find the edge that was removed
          const edge = edges.find(e => e.id === change.id);
          if (edge) {
            // Remove the connection from our state
            onConnectionRemove({
              sourceId: edge.source,
              targetId: edge.target,
              sourceHandle: edge.sourceHandle || 'output',
              targetHandle: edge.targetHandle || 'input'
            });
          }
        }
      });

      // Restore viewport immediately
      if (reactFlowInstance) {
        reactFlowInstance.setViewport(currentViewport);
      }
    },
    [edges, onEdgesChange, onConnectionRemove, reactFlowInstance]
  );

  // ALLOW EVERYTHING - Minimal validation with logical checks
  const isValidConnection = useCallback(
    (connection: Connection) => {
      try {
        console.log('🚀 ALLOW EVERYTHING validation:', connection);

        // Only basic checks
        if (!connection.source || !connection.target) {
          return false;
        }

        if (connection.source === connection.target) {
          return false;
        }

        // Check if connection already exists
        const connectionExists = connections.some(
          conn =>
            conn.sourceId === connection.source &&
            conn.targetId === connection.target &&
            conn.sourceHandle === (connection.sourceHandle || 'output') &&
            conn.targetHandle === (connection.targetHandle || 'input')
        );

        if (connectionExists) {
          return false;
        }

        // CRITICAL: Prevent multiple output connections from any single block
        const existingOutputConnections = connections.filter(
          conn => conn.sourceId === connection.source &&
                  conn.sourceHandle === (connection.sourceHandle || 'output')
        );
        if (existingOutputConnections.length > 0) {
          console.log('❌ REJECTED: Multiple output connections from single block not allowed');
          return false;
        }

        // Prevent illogical condition block connections
        const sourceBlock = blocks.find(b => b.id === connection.source);
        if (sourceBlock && sourceBlock.type === 'CONDITION') {
          // Check if both TRUE and FALSE outputs are trying to connect to the same target
          const existingConnections = connections.filter(conn =>
            conn.sourceId === connection.source &&
            conn.targetId === connection.target
          );

          // If there's already a connection from this condition block to the same target
          if (existingConnections.length > 0) {
            const existingHandle = existingConnections[0].sourceHandle;
            const newHandle = connection.sourceHandle;

            // Prevent connecting both TRUE and FALSE to the same target
            if ((existingHandle === 'true' && newHandle === 'false') ||
                (existingHandle === 'false' && newHandle === 'true')) {
              console.log('❌ REJECTED: Cannot connect both TRUE and FALSE outputs to the same target');
              return false;
            }
          }
        }

        console.log('✅ EVERYTHING ALLOWED - connection approved');
        return true;
      } catch (error) {
        console.error('Error in validation:', error);
        return false;
      }
    },
    [connections, blocks]
  );

  // Handle connection start - provide visual feedback and better logging
  const handleConnectStart = useCallback(
    (event: React.MouseEvent | React.TouchEvent, { nodeId, handleId, handleType }: { nodeId: string; handleId: string; handleType: string }) => {
      console.log('Connection started from:', { nodeId, handleId, handleType });

      // Find the source block to validate it exists
      const sourceBlock = blocks.find(b => b.id === nodeId);
      if (!sourceBlock) {
        console.warn('Connection started from unknown block:', nodeId);
        return;
      }

      console.log('Connection started from block type:', sourceBlock.type);

      // Add visual feedback for connection start
      setNodes(nodes =>
        nodes.map(node => ({
          ...node,
          data: {
            ...node.data,
            isConnecting: node.id === nodeId
          }
        }))
      );
    },
    [setNodes, blocks]
  );

  // Handle connection end - clean up visual feedback with better logging
  const handleConnectEnd = useCallback(
    (event: React.MouseEvent | React.TouchEvent) => {
      console.log('Connection ended - cleaning up visual feedback');
      // Remove visual feedback for connection
      setNodes(nodes =>
        nodes.map(node => ({
          ...node,
          data: {
            ...node.data,
            isConnecting: false
          }
        }))
      );
    },
    [setNodes]
  );

  // Factory settings connection handler - simple and reliable
  const handleConnect = useCallback(
    (connection: Connection) => {
      console.log('🏭 Factory handleConnect called with:', connection);

      // Create the connection object
      const connectionObj = {
        sourceId: connection.source,
        targetId: connection.target,
        sourceHandle: connection.sourceHandle || 'output',
        targetHandle: connection.targetHandle || 'input'
      };

      // Add the connection to our state
      const success = onConnectionAdd(connectionObj);

      console.log('🏭 Factory connection result:', success);

      // Clean up any connection state
      setNodes(nodes =>
        nodes.map(node => ({
          ...node,
          data: {
            ...node.data,
            isConnecting: false
          }
        }))
      );
    },
    [onConnectionAdd, setNodes]
  );

  // Handle dropping a new block onto the canvas
  const handleDrop = useCallback(
    (event: React.DragEvent<HTMLDivElement>) => {
      event.preventDefault();

      const reactFlowBounds = reactFlowWrapper.current?.getBoundingClientRect();
      const dragData = event.dataTransfer.getData('application/reactflow');

      if (!dragData || !reactFlowBounds) {
        return;
      }

      // Parse the drag data
      try {
        // Check if the data is JSON or just a string (for backward compatibility)
        let type, properties = {};

        try {
          const parsedData = JSON.parse(dragData);
          type = parsedData.type;
          properties = parsedData.properties || {};
        } catch {
          // If parsing fails, assume it's just the type string (old format)
          type = dragData;
        }

        if (!type) {
          return;
        }

        // Get the position where the block was dropped
        const position = reactFlowInstance.project({
          x: event.clientX - reactFlowBounds.left,
          y: event.clientY - reactFlowBounds.top
        });

        // Ensure the position is valid
        if (isNaN(position.x) || isNaN(position.y)) {
          position.x = 100;
          position.y = 100;
        }

        // Add the new block with properties
        addBlock(type, position, properties);
      } catch (error) {
        console.error('Error handling block drop:', error);
      }
    },
    [addBlock, reactFlowInstance]
  );

  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);


  // Fit view to see all nodes
  const handleFitView = useCallback(() => {
    if (reactFlowInstance) {
      reactFlowInstance.fitView({ padding: 0.2 });
    }
  }, [reactFlowInstance]);

  // Zoom in while preserving viewport center
  const handleZoomIn = useCallback(() => {
    if (reactFlowInstance) {
      requestAnimationFrame(() => {
        // Get current viewport
        const { x, y, zoom } = reactFlowInstance.getViewport();
        // Calculate new zoom level
        const newZoom = Math.min(zoom * 1.2, 1.5); // Increase zoom by 20%, max 1.5
        // Set viewport with new zoom but same position
        reactFlowInstance.setViewport({ x, y, zoom: newZoom }, { duration: 0 });
      });
    }
  }, [reactFlowInstance]);

  // Zoom out while preserving viewport center
  const handleZoomOut = useCallback(() => {
    if (reactFlowInstance) {
      requestAnimationFrame(() => {
        // Get current viewport
        const { x, y, zoom } = reactFlowInstance.getViewport();
        // Calculate new zoom level
        const newZoom = Math.max(zoom * 0.8, 0.2); // Decrease zoom by 20%, min 0.2
        // Set viewport with new zoom but same position
        reactFlowInstance.setViewport({ x, y, zoom: newZoom }, { duration: 0 });
      });
    }
  }, [reactFlowInstance]);

  // Initialize ReactFlow when component mounts
  React.useEffect(() => {
    // Force a re-render after a short delay to ensure ReactFlow is properly initialized
    const timer = setTimeout(() => {
      if (nodes.length > 0) {
        setNodes(nodes => [...nodes]);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="h-full w-full relative" ref={reactFlowWrapper} style={{ minHeight: '500px', minWidth: '100%' }}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={handleNodesChange}
        onEdgesChange={handleEdgesChange}
        onConnect={handleConnect}
        onConnectStart={handleConnectStart}
        onConnectEnd={handleConnectEnd}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onInit={setReactFlowInstance}
        nodeTypes={nodeTypes}
        isValidConnection={isValidConnection}
        connectionLineType={ConnectionLineType.SmoothStep}
        connectionLineStyle={{
          strokeWidth: 2,
          stroke: 'rgba(16, 185, 129, 0.6)',
          strokeLinecap: 'round',
          strokeDasharray: '5 5',
          filter: 'drop-shadow(0 1px 2px rgba(16, 185, 129, 0.3))'
        }}
        connectOnClick={false}
        deleteKeyCode={null}
        multiSelectionKeyCode={null}
        defaultViewport={{ x: 0, y: 0, zoom: 1 }}
        snapToGrid={true}
        snapGrid={[20, 20]}
        fitViewOptions={{ padding: 0.5 }}
        minZoom={0.2}
        maxZoom={1.5}
        deleteKeyCode={['Backspace', 'Delete']}
        multiSelectionKeyCode={['Control', 'Meta']}
        selectionKeyCode={['Shift']}
        connectionMode="loose"
        connectOnClick={false}
        proOptions={{ hideAttribution: true }}
      >
        <Background />
        {/* Canvas Controls - Hidden in fullscreen */}
        {!isFullscreen && (
          <Panel position="top-right" className="flex gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={handleZoomIn}>
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Zoom In</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={handleZoomOut}>
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Zoom Out</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={onFullscreenToggle || handleFitView}>
                    <Maximize className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>{onFullscreenToggle ? 'Enter Fullscreen' : 'Fit View'}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </Panel>
        )}

        {/* Fullscreen Exit Button */}
        {isFullscreen && (
          <Panel position="top-left" className="flex gap-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="outline" size="icon" onClick={onFullscreenToggle}>
                    <X className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Exit Fullscreen</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </Panel>
        )}



      </ReactFlow>
    </div>
  );
};

export default BuildCanvas;

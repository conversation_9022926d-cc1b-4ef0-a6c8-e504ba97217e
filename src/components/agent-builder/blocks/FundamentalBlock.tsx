import React from 'react';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { BarChart2, Trash2 } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';
import { FINANCIAL_METRICS, METRICS_BY_STATEMENT, STATEMENT_LABELS } from '@/constants/financialMetrics';

interface FundamentalBlockProps {
  data: {
    id: string;
    metric: string;
    statement?: 'balance_sheet' | 'income_statement' | 'cash_flow_statement' | 'comprehensive_income';
    period?: 'quarterly' | 'annual';
    parameters?: Record<string, any>;
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
  };
  selected: boolean;
}

const FundamentalBlock: React.FC<FundamentalBlockProps> = ({ data, selected }) => {

  // Handle statement change
  const handleStatementChange = (value: string) => {
    const statement = value as 'balance_sheet' | 'income_statement' | 'cash_flow_statement' | 'comprehensive_income';
    data.onUpdate({
      statement,
      metric: '' // Reset metric when statement changes
    });
  };

  // Handle metric change
  const handleMetricChange = (value: string) => {
    data.onUpdate({ metric: value });
  };

  // Handle period change
  const handlePeriodChange = (value: string) => {
    data.onUpdate({ period: value as 'quarterly' | 'annual' });
  };

  // Get available metrics for the selected statement
  const currentStatement = data.statement || 'income_statement';
  const availableMetrics = METRICS_BY_STATEMENT[currentStatement] || [];

  // Get the current metric (handle both direct and calculated metrics)
  const currentMetric = availableMetrics.find(m => m.id === data.metric) || availableMetrics[0];

  // Check if this is a calculated metric
  const isCalculatedMetric = currentStatement === 'calculated';

  return (
    <div className="relative">
      {/* Input handle - left for horizontal flow (consistent with other blocks) */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="transition-all duration-200"
        style={{
          background: '#6b7280',
          width: '12px',
          height: '12px',
          border: '2px solid white',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
        }}
      />

      {/* Output handle - right for horizontal flow (consistent with other blocks) */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="transition-all duration-200"
        style={{
          background: '#3b82f6',
          width: '12px',
          height: '12px',
          border: '2px solid white',
          boxShadow: '0 2px 4px rgba(0, 0, 0, 0.2)'
        }}
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center">
              <BarChart2 className="h-3 w-3 text-primary" />
            </div>
            <CardTitle className="text-sm font-medium">Fundamental</CardTitle>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={data.onRemove}
              title="Remove block"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2">
          <div className="space-y-2">
            <div>
              <label className="text-xs font-medium block mb-1">Financial Statement</label>
              <Select
                value={currentStatement}
                onValueChange={handleStatementChange}
              >
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="Select statement" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(STATEMENT_LABELS).map(([key, label]) => (
                    <SelectItem key={key} value={key} className="text-xs">
                      {label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label className="text-xs font-medium block mb-1">Metric</label>
              <Select
                value={data.metric}
                onValueChange={handleMetricChange}
              >
                <SelectTrigger className="h-8 text-xs">
                  <SelectValue placeholder="Select metric" />
                </SelectTrigger>
                <SelectContent>
                  {availableMetrics.map(metric => (
                    <SelectItem key={metric.id} value={metric.id} className="text-xs">
                      {metric.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {!isCalculatedMetric && (
              <div>
                <label className="text-xs font-medium block mb-1">Period</label>
                <Select
                  value={data.period || 'quarterly'}
                  onValueChange={handlePeriodChange}
                >
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue placeholder="Select period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="quarterly" className="text-xs">Quarterly</SelectItem>
                    <SelectItem value="annual" className="text-xs">Annual</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            )}

            {currentMetric && (
              <div className="mt-2 p-2 bg-muted/50 rounded-md">
                <p className="text-xs font-medium mb-1">{currentMetric.label}</p>
                <p className="text-xs text-muted-foreground mb-1">
                  {currentMetric.description}
                </p>
                {!isCalculatedMetric ? (
                  <>
                    <p className="text-xs text-muted-foreground">
                      <strong>Statement:</strong> {STATEMENT_LABELS[currentMetric.statement]}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      <strong>Unit:</strong> {currentMetric.unit}
                    </p>
                    <p className="text-xs mt-1 text-muted-foreground">
                      Data from Polygon API's financial statements.
                    </p>
                  </>
                ) : (
                  <>
                    <p className="text-xs text-muted-foreground">
                      <strong>Type:</strong> Calculated Ratio
                    </p>
                    <p className="text-xs text-muted-foreground">
                      <strong>Unit:</strong> {currentMetric.unit}
                    </p>
                    {(currentMetric as any).calculation && (
                      <p className="text-xs text-muted-foreground">
                        <strong>Formula:</strong> {(currentMetric as any).calculation}
                      </p>
                    )}
                    <p className="text-xs mt-1 text-muted-foreground">
                      Automatically calculated from financial statement data.
                    </p>
                  </>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default FundamentalBlock;

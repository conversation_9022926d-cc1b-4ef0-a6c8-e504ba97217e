import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON><PERSON>, Position } from 'reactflow';
import { TrendingUp, X, BarChart3, Layers, ArrowUpDown } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface MarketAnalysisBlockProps {
  data: {
    id: string;
    type: string;
    parameters: Record<string, any>;
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
  };
  selected: boolean;
}

const MarketAnalysisBlock: React.FC<MarketAnalysisBlockProps> = ({ data, selected }) => {
  
  const getBlockConfig = (type: string) => {
    switch (type) {
      case 'SUPPORT_RESISTANCE':
        return {
          title: 'Support/Resistance',
          icon: BarChart3,
          color: 'blue',
          methods: [
            { value: 'pivot_points', label: 'Pivot Points', params: { method: 'standard', timeframe: 'day' } },
            { value: 'swing_levels', label: 'Swing Levels', params: { lookbackPeriod: 20, minTouches: 2, tolerance: 0.5 } },
            { value: 'fibonacci_retracement', label: 'Fibonacci Retracement', params: { lookbackPeriod: 50, levels: [23.6, 38.2, 50, 61.8, 78.6] } },
            { value: 'volume_profile', label: 'Volume Profile', params: { period: 20, valueAreaPercentage: 70 } },
            { value: 'psychological_levels', label: 'Psychological Levels', params: { roundNumbers: true, stepSize: 5 } }
          ]
        };
      case 'TREND_LINE_ANALYSIS':
        return {
          title: 'Trend Line Analysis',
          icon: TrendingUp,
          color: 'green',
          methods: [
            { value: 'automatic_trendlines', label: 'Automatic Trend Lines', params: { minTouches: 2, lookbackPeriod: 50, tolerance: 1 } },
            { value: 'channel_detection', label: 'Channel Detection', params: { channelWidth: 2, minLength: 10, parallelTolerance: 0.5 } },
            { value: 'breakout_lines', label: 'Breakout Lines', params: { lookbackPeriod: 20, breakoutThreshold: 1, volumeConfirmation: true } },
            { value: 'regression_lines', label: 'Linear Regression', params: { period: 20, standardDeviations: 2 } }
          ]
        };
      case 'MARKET_STRUCTURE':
        return {
          title: 'Market Structure',
          icon: Layers,
          color: 'purple',
          methods: [
            { value: 'higher_highs_lows', label: 'Higher Highs/Lower Lows', params: { lookbackPeriod: 20, minSwingSize: 1 } },
            { value: 'market_phases', label: 'Market Phases', params: { trendPeriod: 50, rangePeriod: 20, volatilityThreshold: 1.5 } },
            { value: 'structure_breaks', label: 'Structure Breaks', params: { swingPeriod: 10, breakThreshold: 1, confirmationPeriod: 3 } },
            { value: 'liquidity_zones', label: 'Liquidity Zones', params: { volumeThreshold: 1.5, priceClusterTolerance: 0.5 } }
          ]
        };
      default:
        return {
          title: 'Market Analysis',
          icon: BarChart3,
          color: 'blue',
          methods: [
            { value: 'basic_analysis', label: 'Basic Analysis', params: { period: 20 } }
          ]
        };
    }
  };

  const config = getBlockConfig(data.type);
  const IconComponent = config.icon;

  // Safely access parameters with fallback
  const parameters = data.parameters || {};
  const currentMethod = config.methods.find(method => method.value === parameters.method) || config.methods[0];

  const handleMethodChange = (method: string) => {
    const selectedMethod = config.methods.find(m => m.value === method);
    if (selectedMethod) {
      data.onUpdate({
        parameters: {
          ...parameters,
          method,
          ...selectedMethod.params
        }
      });
    }
  };

  const handleParameterChange = (param: string, value: any) => {
    data.onUpdate({
      parameters: {
        ...parameters,
        [param]: parseFloat(value) || value
      }
    });
  };

  const renderParameterInputs = () => {
    if (!currentMethod) return null;

    return Object.entries(currentMethod.params).map(([param, defaultValue]) => {
      const currentValue = parameters[param] ?? defaultValue;
      
      // Special handling for different parameter types
      if (param === 'method' && parameters.method === 'pivot_points') {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block">Pivot Method</label>
            <Select value={currentValue} onValueChange={value => handleParameterChange(param, value)}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="standard">Standard</SelectItem>
                <SelectItem value="fibonacci">Fibonacci</SelectItem>
                <SelectItem value="woodie">Woodie</SelectItem>
                <SelectItem value="camarilla">Camarilla</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      }

      if (param === 'timeframe') {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block">Timeframe</label>
            <Select value={currentValue} onValueChange={value => handleParameterChange(param, value)}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1minute">1 Minute</SelectItem>
                <SelectItem value="5minute">5 Minutes</SelectItem>
                <SelectItem value="15minute">15 Minutes</SelectItem>
                <SelectItem value="hour">1 Hour</SelectItem>
                <SelectItem value="4hour">4 Hours</SelectItem>
                <SelectItem value="day">Daily</SelectItem>
                <SelectItem value="week">Weekly</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      }

      if (typeof defaultValue === 'boolean') {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block capitalize">
              {param.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </label>
            <Select value={currentValue.toString()} onValueChange={value => handleParameterChange(param, value === 'true')}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="true">Yes</SelectItem>
                <SelectItem value="false">No</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      }

      if (Array.isArray(defaultValue)) {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block capitalize">
              {param.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </label>
            <Input
              type="text"
              value={Array.isArray(currentValue) ? currentValue.join(', ') : currentValue}
              onChange={e => handleParameterChange(param, e.target.value.split(', ').map(v => parseFloat(v) || v))}
              className="h-8 text-xs"
              placeholder="Comma separated values"
            />
          </div>
        );
      }

      return (
        <div key={param} className="space-y-1">
          <label className="text-xs font-medium block capitalize">
            {param.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
          </label>
          <Input
            type="number"
            value={currentValue}
            onChange={e => handleParameterChange(param, e.target.value)}
            className="h-8 text-xs"
            min={0}
            step={param.includes('tolerance') || param.includes('threshold') || param.includes('percentage') ? 0.1 : 1}
          />
        </div>
      );
    });
  };

  const getColorClasses = (color: string) => {
    const colorMap: Record<string, { bg: string; text: string }> = {
      blue: { bg: 'bg-blue-500/10', text: 'text-blue-500' },
      green: { bg: 'bg-green-500/10', text: 'text-green-500' },
      purple: { bg: 'bg-purple-500/10', text: 'text-purple-500' }
    };
    return colorMap[color] || colorMap.blue;
  };

  const colorClasses = getColorClasses(config.color);

  return (
    <>
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-gray-400 border-2 border-white"
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''} ${data.hasError ? 'border-red-500' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`w-6 h-6 rounded-full ${colorClasses.bg} flex items-center justify-center`}>
              <IconComponent className={`h-3 w-3 ${colorClasses.text}`} />
            </div>
            <CardTitle className="text-sm font-medium">{config.title}</CardTitle>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={(e) => {
                e.stopPropagation();
                data.onRemove();
              }}
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2 space-y-3">
          {/* Method Selection */}
          <div className="space-y-1">
            <label className="text-xs font-medium block">Analysis Method</label>
            <Select value={parameters.method || currentMethod.value} onValueChange={handleMethodChange}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {config.methods.map(method => (
                  <SelectItem key={method.value} value={method.value}>
                    {method.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Parameter Inputs */}
          <div className="space-y-2">
            {renderParameterInputs()}
          </div>

          {/* Error Messages */}
          {data.hasError && data.errorMessages && (
            <div className="text-xs text-red-500 bg-red-50 p-2 rounded">
              {data.errorMessages.map((msg, idx) => (
                <div key={idx}>{msg}</div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Output handles with color coding */}
      <Handle
        type="source"
        position={Position.Right}
        id="bullish"
        style={{ top: '30%' }}
        className="w-3 h-3 bg-green-500 border-2 border-white"
      />
      <Handle
        type="source"
        position={Position.Right}
        id="neutral"
        style={{ top: '50%' }}
        className="w-3 h-3 bg-gray-400 border-2 border-white"
      />
      <Handle
        type="source"
        position={Position.Right}
        id="bearish"
        style={{ top: '70%' }}
        className="w-3 h-3 bg-red-500 border-2 border-white"
      />
    </>
  );
};

export default MarketAnalysisBlock;

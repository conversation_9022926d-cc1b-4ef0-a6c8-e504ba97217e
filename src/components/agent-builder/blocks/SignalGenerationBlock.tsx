import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Handle, Position } from 'reactflow';
import { Zap, Trash2, TrendingUp, BarChart3, Activity } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface SignalGenerationBlockProps {
  data: {
    id: string;
    type: string;
    parameters: Record<string, any>;
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
  };
  selected: boolean;
}

const SignalGenerationBlock: React.FC<SignalGenerationBlockProps> = ({ data, selected }) => {
  
  const getBlockConfig = (type: string) => {
    switch (type) {
      case 'PRICE_ACTION_SIGNAL':
        return {
          title: 'Price Action Signal',
          icon: TrendingUp,
          color: 'purple',
          signals: [
            { value: 'breakout', label: 'Breakout', params: { lookbackPeriod: 20, volumeConfirmation: true, minBreakoutSize: 1 } },
            { value: 'reversal', label: 'Reversal', params: { lookbackPeriod: 10, confirmationCandles: 2, minReversalSize: 2 } },
            { value: 'pullback', label: 'Pullback', params: { trendPeriod: 50, pullbackDepth: 38.2, confirmationPeriod: 5 } },
            { value: 'momentum_shift', label: 'Momentum Shift', params: { period: 14, threshold: 50, confirmationPeriod: 3 } }
          ]
        };
      case 'MULTI_TIMEFRAME_ANALYSIS':
        return {
          title: 'Multi-Timeframe Analysis',
          icon: BarChart3,
          color: 'indigo',
          signals: [
            { value: 'trend_alignment', label: 'Trend Alignment', params: { primaryTimeframe: 'day', secondaryTimeframe: 'hour', tertiaryTimeframe: '15minute' } },
            { value: 'higher_timeframe_bias', label: 'Higher TF Bias', params: { signalTimeframe: 'hour', biasTimeframe: 'day', confirmationRequired: true } },
            { value: 'multi_tf_confluence', label: 'Multi-TF Confluence', params: { timeframes: ['day', 'hour', '15minute'], minConfluence: 2 } }
          ]
        };
      case 'DIVERGENCE_DETECTION':
        return {
          title: 'Divergence Detection',
          icon: Activity,
          color: 'cyan',
          signals: [
            { value: 'rsi_divergence', label: 'RSI Divergence', params: { rsiPeriod: 14, lookbackPeriod: 20, minDivergenceStrength: 2 } },
            { value: 'macd_divergence', label: 'MACD Divergence', params: { fastPeriod: 12, slowPeriod: 26, signalPeriod: 9, lookbackPeriod: 20 } },
            { value: 'volume_divergence', label: 'Volume Divergence', params: { volumePeriod: 20, pricePeriod: 20, minDivergenceStrength: 1.5 } }
          ]
        };
      case 'VOLUME_CONFIRMATION':
        return {
          title: 'Volume Confirmation',
          icon: BarChart3,
          color: 'orange',
          signals: [
            { value: 'volume_spike', label: 'Volume Spike', params: { lookbackPeriod: 20, spikeMultiplier: 2, minSpikeDuration: 1 } },
            { value: 'volume_trend', label: 'Volume Trend', params: { trendPeriod: 10, minTrendStrength: 1.5 } },
            { value: 'accumulation_distribution', label: 'Accumulation/Distribution', params: { period: 20, threshold: 0.1 } }
          ]
        };
      case 'MARKET_REGIME':
        return {
          title: 'Market Regime',
          icon: Activity,
          color: 'teal',
          signals: [
            { value: 'trending_vs_ranging', label: 'Trending vs Ranging', params: { adxPeriod: 14, trendThreshold: 25, rangingThreshold: 20 } },
            { value: 'volatility_regime', label: 'Volatility Regime', params: { atrPeriod: 14, lookbackPeriod: 50, highVolThreshold: 1.5, lowVolThreshold: 0.7 } },
            { value: 'market_sentiment', label: 'Market Sentiment', params: { vixThreshold: 20, fearGreedThreshold: 25 } }
          ]
        };
      default:
        return {
          title: 'Signal Generation',
          icon: Zap,
          color: 'blue',
          signals: [
            { value: 'basic_signal', label: 'Basic Signal', params: { threshold: 50 } }
          ]
        };
    }
  };

  const config = getBlockConfig(data.type);
  const IconComponent = config.icon;

  // Ensure parameters exist and have default values
  const parameters = data.parameters || {};
  const currentSignal = config.signals.find(signal => signal.value === parameters.signalType) || config.signals[0];

  const handleSignalChange = (signalType: string) => {
    const selectedSignal = config.signals.find(s => s.value === signalType);
    if (selectedSignal) {
      data.onUpdate({
        parameters: {
          ...parameters,
          signalType,
          ...selectedSignal.params
        }
      });
    }
  };

  const handleParameterChange = (param: string, value: any) => {
    data.onUpdate({
      parameters: {
        ...parameters,
        [param]: parseFloat(value) || value
      }
    });
  };

  const renderParameterInputs = () => {
    if (!currentSignal) return null;

    return Object.entries(currentSignal.params).map(([param, defaultValue]) => {
      const currentValue = parameters[param] ?? defaultValue;
      
      // Special handling for timeframe parameters
      if (param.includes('timeframe') || param.includes('Timeframe')) {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block capitalize">
              {param.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </label>
            <Select value={currentValue} onValueChange={value => handleParameterChange(param, value)}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1minute">1 Minute</SelectItem>
                <SelectItem value="5minute">5 Minutes</SelectItem>
                <SelectItem value="15minute">15 Minutes</SelectItem>
                <SelectItem value="hour">1 Hour</SelectItem>
                <SelectItem value="4hour">4 Hours</SelectItem>
                <SelectItem value="day">Daily</SelectItem>
                <SelectItem value="week">Weekly</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      }

      // Special handling for boolean parameters
      if (typeof defaultValue === 'boolean') {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block capitalize">
              {param.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </label>
            <Select value={currentValue.toString()} onValueChange={value => handleParameterChange(param, value === 'true')}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="true">Yes</SelectItem>
                <SelectItem value="false">No</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      }

      // Special handling for array parameters
      if (Array.isArray(defaultValue)) {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block capitalize">
              {param.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </label>
            <Input
              type="text"
              value={Array.isArray(currentValue) ? currentValue.join(', ') : currentValue}
              onChange={e => handleParameterChange(param, e.target.value.split(', '))}
              className="h-8 text-xs"
              placeholder="Comma separated values"
            />
          </div>
        );
      }

      return (
        <div key={param} className="space-y-1">
          <label className="text-xs font-medium block capitalize">
            {param.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
          </label>
          <Input
            type="number"
            value={currentValue}
            onChange={e => handleParameterChange(param, e.target.value)}
            className="h-8 text-xs"
            min={0}
            step={param.includes('threshold') || param.includes('multiplier') || param.includes('depth') ? 0.1 : 1}
          />
        </div>
      );
    });
  };

  const getColorClasses = (color: string) => {
    const colorMap: Record<string, { bg: string; text: string }> = {
      purple: { bg: 'bg-purple-500/10', text: 'text-purple-500' },
      indigo: { bg: 'bg-indigo-500/10', text: 'text-indigo-500' },
      cyan: { bg: 'bg-cyan-500/10', text: 'text-cyan-500' },
      orange: { bg: 'bg-orange-500/10', text: 'text-orange-500' },
      teal: { bg: 'bg-teal-500/10', text: 'text-teal-500' },
      blue: { bg: 'bg-blue-500/10', text: 'text-blue-500' }
    };
    return colorMap[color] || colorMap.blue;
  };

  const colorClasses = getColorClasses(config.color);

  return (
    <>
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-gray-400 border-2 border-white"
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''} ${data.hasError ? 'border-red-500' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`w-6 h-6 rounded-full ${colorClasses.bg} flex items-center justify-center`}>
              <IconComponent className={`h-3 w-3 ${colorClasses.text}`} />
            </div>
            <CardTitle className="text-sm font-medium">{config.title}</CardTitle>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={(e) => {
                e.stopPropagation();
                data.onRemove();
              }}
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2 space-y-3">
          {/* Signal Type Selection */}
          <div className="space-y-1">
            <label className="text-xs font-medium block">Signal Type</label>
            <Select value={parameters.signalType || currentSignal.value} onValueChange={handleSignalChange}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {config.signals.map(signal => (
                  <SelectItem key={signal.value} value={signal.value}>
                    {signal.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Parameter Inputs */}
          <div className="space-y-2">
            {renderParameterInputs()}
          </div>

          {/* Error Messages */}
          {data.hasError && data.errorMessages && (
            <div className="text-xs text-red-500 bg-red-50 p-2 rounded">
              {data.errorMessages.map((msg, idx) => (
                <div key={idx}>{msg}</div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Output handles with color coding */}
      <Handle
        type="source"
        position={Position.Right}
        id="bullish"
        style={{ top: '30%' }}
        className="w-3 h-3 bg-green-500 border-2 border-white"
      />
      <Handle
        type="source"
        position={Position.Right}
        id="neutral"
        style={{ top: '50%' }}
        className="w-3 h-3 bg-gray-400 border-2 border-white"
      />
      <Handle
        type="source"
        position={Position.Right}
        id="bearish"
        style={{ top: '70%' }}
        className="w-3 h-3 bg-red-500 border-2 border-white"
      />
    </>
  );
};

export default SignalGenerationBlock;

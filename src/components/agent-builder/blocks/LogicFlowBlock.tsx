import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Handle, Position } from 'reactflow';
import { GitBranch, Trash2, Plus, Minus, Filter, CheckCircle } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';

interface LogicFlowBlockProps {
  data: {
    id: string;
    type: string;
    parameters: Record<string, any>;
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
  };
  selected: boolean;
}

const LogicFlowBlock: React.FC<LogicFlowBlockProps> = ({ data, selected }) => {
  
  const getBlockConfig = (type: string) => {
    switch (type) {
      case 'IF_THEN_ELSE':
        return {
          title: 'If-Then-Else',
          icon: GitBranch,
          color: 'blue',
          hasCondition: true,
          inputs: ['condition'],
          outputs: ['true', 'false'],
          params: { operator: 'greater_than', threshold: 50 }
        };
      case 'AND_OPERATOR':
        return {
          title: 'AND Operator',
          icon: Plus,
          color: 'green',
          hasCondition: false,
          inputs: ['input1', 'input2'],
          outputs: ['true', 'false'],
          params: { requireAll: true }
        };
      case 'OR_OPERATOR':
        return {
          title: 'OR Operator',
          icon: Plus,
          color: 'orange',
          hasCondition: false,
          inputs: ['input1', 'input2'],
          outputs: ['true', 'false'],
          params: { requireAny: true }
        };
      case 'NOT_OPERATOR':
        return {
          title: 'NOT Operator',
          icon: Minus,
          color: 'red',
          hasCondition: false,
          inputs: ['input'],
          outputs: ['output'],
          params: { invert: true }
        };
      case 'SIGNAL_CONFIRMATION':
        return {
          title: 'Signal Confirmation',
          icon: CheckCircle,
          color: 'purple',
          hasCondition: true,
          inputs: ['signal1', 'signal2'],
          outputs: ['confirmed', 'rejected'],
          params: { 
            confirmationType: 'both_agree',
            confidenceThreshold: 70,
            timeWindow: 5
          }
        };
      case 'TIME_FILTER':
        return {
          title: 'Time Filter',
          icon: Filter,
          color: 'cyan',
          hasCondition: true,
          inputs: ['input'],
          outputs: ['allow', 'block'],
          params: {
            filterType: 'time_range',
            startTime: '09:30',
            endTime: '16:00',
            timezone: 'US/Eastern'
          }
        };
      case 'MARKET_CONDITION_FILTER':
        return {
          title: 'Market Condition Filter',
          icon: Filter,
          color: 'indigo',
          hasCondition: true,
          inputs: ['input'],
          outputs: ['allow', 'block'],
          params: {
            conditionType: 'volatility',
            threshold: 20,
            operator: 'less_than'
          }
        };
      default:
        return {
          title: 'Logic Block',
          icon: GitBranch,
          color: 'blue',
          hasCondition: true,
          inputs: ['input'],
          outputs: ['output'],
          params: {}
        };
    }
  };

  const config = getBlockConfig(data.type);
  const IconComponent = config.icon;

  const handleParameterChange = (param: string, value: any) => {
    data.onUpdate({
      parameters: {
        ...data.parameters,
        [param]: parseFloat(value) || value
      }
    });
  };

  const renderParameterInputs = () => {
    return Object.entries(config.params).map(([param, defaultValue]) => {
      const currentValue = data.parameters[param] ?? defaultValue;
      
      // Special handling for different parameter types
      if (param === 'operator') {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block">Operator</label>
            <Select value={currentValue} onValueChange={value => handleParameterChange(param, value)}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="greater_than">Greater Than</SelectItem>
                <SelectItem value="less_than">Less Than</SelectItem>
                <SelectItem value="equal_to">Equal To</SelectItem>
                <SelectItem value="greater_equal">Greater or Equal</SelectItem>
                <SelectItem value="less_equal">Less or Equal</SelectItem>
                <SelectItem value="not_equal">Not Equal</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      }

      if (param === 'confirmationType') {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block">Confirmation Type</label>
            <Select value={currentValue} onValueChange={value => handleParameterChange(param, value)}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="both_agree">Both Signals Agree</SelectItem>
                <SelectItem value="majority_vote">Majority Vote</SelectItem>
                <SelectItem value="weighted_average">Weighted Average</SelectItem>
                <SelectItem value="strongest_signal">Strongest Signal</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      }

      if (param === 'filterType') {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block">Filter Type</label>
            <Select value={currentValue} onValueChange={value => handleParameterChange(param, value)}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="time_range">Time Range</SelectItem>
                <SelectItem value="day_of_week">Day of Week</SelectItem>
                <SelectItem value="exclude_times">Exclude Times</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      }

      if (param === 'conditionType') {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block">Condition Type</label>
            <Select value={currentValue} onValueChange={value => handleParameterChange(param, value)}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="volatility">Volatility (VIX)</SelectItem>
                <SelectItem value="volume">Volume</SelectItem>
                <SelectItem value="trend_strength">Trend Strength</SelectItem>
                <SelectItem value="market_breadth">Market Breadth</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      }

      if (param === 'timezone') {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block">Timezone</label>
            <Select value={currentValue} onValueChange={value => handleParameterChange(param, value)}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="US/Eastern">US/Eastern</SelectItem>
                <SelectItem value="US/Central">US/Central</SelectItem>
                <SelectItem value="US/Mountain">US/Mountain</SelectItem>
                <SelectItem value="US/Pacific">US/Pacific</SelectItem>
                <SelectItem value="UTC">UTC</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      }

      if (typeof defaultValue === 'boolean') {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block capitalize">
              {param.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </label>
            <Select value={currentValue.toString()} onValueChange={value => handleParameterChange(param, value === 'true')}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="true">Yes</SelectItem>
                <SelectItem value="false">No</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      }

      return (
        <div key={param} className="space-y-1">
          <label className="text-xs font-medium block capitalize">
            {param.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
          </label>
          <Input
            type={param.includes('Time') ? 'time' : 'number'}
            value={currentValue}
            onChange={e => handleParameterChange(param, e.target.value)}
            className="h-8 text-xs"
            min={0}
            step={param.includes('threshold') || param.includes('confidence') ? 0.1 : 1}
          />
        </div>
      );
    });
  };

  const getColorClasses = (color: string) => {
    const colorMap: Record<string, { bg: string; text: string }> = {
      blue: { bg: 'bg-blue-500/10', text: 'text-blue-500' },
      green: { bg: 'bg-green-500/10', text: 'text-green-500' },
      orange: { bg: 'bg-orange-500/10', text: 'text-orange-500' },
      red: { bg: 'bg-red-500/10', text: 'text-red-500' },
      purple: { bg: 'bg-purple-500/10', text: 'text-purple-500' },
      cyan: { bg: 'bg-cyan-500/10', text: 'text-cyan-500' },
      indigo: { bg: 'bg-indigo-500/10', text: 'text-indigo-500' }
    };
    return colorMap[color] || colorMap.blue;
  };

  const colorClasses = getColorClasses(config.color);

  return (
    <>
      {/* Input handles */}
      {config.inputs.map((input, index) => (
        <Handle
          key={input}
          type="target"
          position={Position.Left}
          id={input}
          style={{ top: `${30 + (index * 20)}%` }}
          className="w-3 h-3 bg-gray-400 border-2 border-white"
        />
      ))}

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''} ${data.hasError ? 'border-red-500' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`w-6 h-6 rounded-full ${colorClasses.bg} flex items-center justify-center`}>
              <IconComponent className={`h-3 w-3 ${colorClasses.text}`} />
            </div>
            <CardTitle className="text-sm font-medium">{config.title}</CardTitle>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={(e) => {
                e.stopPropagation();
                data.onRemove();
              }}
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2 space-y-3">
          {/* Parameter Inputs */}
          <div className="space-y-2">
            {renderParameterInputs()}
          </div>

          {/* Error Messages */}
          {data.hasError && data.errorMessages && (
            <div className="text-xs text-red-500 bg-red-50 p-2 rounded">
              {data.errorMessages.map((msg, idx) => (
                <div key={idx}>{msg}</div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Output handles */}
      {config.outputs.map((output, index) => {
        const isPositive = output === 'true' || output === 'allow' || output === 'confirmed';
        const isNegative = output === 'false' || output === 'block' || output === 'rejected';
        
        let handleColor = 'bg-gray-400';
        if (isPositive) handleColor = 'bg-green-500';
        if (isNegative) handleColor = 'bg-red-500';
        
        return (
          <Handle
            key={output}
            type="source"
            position={Position.Right}
            id={output}
            style={{ top: `${30 + (index * 20)}%` }}
            className={`w-3 h-3 ${handleColor} border-2 border-white`}
          />
        );
      })}
    </>
  );
};

export default LogicFlowBlock;

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Handle, Position } from 'reactflow';
import { Shield, Trash2, Percent, DollarSign, Target, TrendingUp, AlertTriangle } from 'lucide-react';
import { AgentBlock } from '@/services/agentService';


interface RiskManagementBlockProps {
  data: {
    id: string;
    type: string;
    parameters: Record<string, any>;
    isEntryBlock: boolean;
    onUpdate: (properties: Partial<AgentBlock>) => void;
    onRemove: () => void;
    onSetAsEntry: () => void;
    hasError?: boolean;
    isDisconnected?: boolean;
    errorMessages?: string[];
  };
  selected: boolean;
}

const RiskManagementBlock: React.FC<RiskManagementBlockProps> = ({ data, selected }) => {
  
  const getBlockConfig = (type: string) => {
    switch (type) {

      case 'STOP_LOSS':
        return {
          title: 'Stop Loss',
          icon: Shield,
          color: 'red',
          methods: [
            { value: 'fixed_percentage', label: 'Fixed Percentage', params: { percentage: 2 } },
            { value: 'fixed_amount', label: 'Fixed Amount', params: { amount: 50 } },
            { value: 'trailing', label: 'Trailing Stop', params: { trailAmount: 1, trailType: 'percentage' } },
            { value: 'atr_based', label: 'ATR-Based', params: { atrMultiplier: 2, atrPeriod: 14 } },
            { value: 'structure_based', label: 'Structure-Based', params: { lookbackPeriod: 20, bufferPercentage: 0.5 } }
          ]
        };
      case 'TAKE_PROFIT':
        return {
          title: 'Take Profit',
          icon: DollarSign,
          color: 'green',
          methods: [
            { value: 'fixed_percentage', label: 'Fixed Percentage', params: { percentage: 4 } },
            { value: 'fixed_amount', label: 'Fixed Amount', params: { amount: 100 } },
            { value: 'risk_reward_ratio', label: 'Risk-Reward Ratio', params: { ratio: 2 } },
            { value: 'fibonacci_levels', label: 'Fibonacci Levels', params: { level: 61.8, lookbackPeriod: 50 } },
            { value: 'resistance_based', label: 'Resistance-Based', params: { lookbackPeriod: 20, bufferPercentage: 0.5 } }
          ]
        };
      case 'PORTFOLIO_RISK':
        return {
          title: 'Portfolio Risk',
          icon: Shield,
          color: 'orange',
          methods: [
            { value: 'max_drawdown', label: 'Max Drawdown Limit', params: { maxDrawdown: 10 } },
            { value: 'correlation_limit', label: 'Correlation Limit', params: { maxCorrelation: 0.7, lookbackPeriod: 60 } },
            { value: 'sector_exposure', label: 'Sector Exposure Limit', params: { maxSectorExposure: 25 } },
            { value: 'var_limit', label: 'Value at Risk Limit', params: { varPercentage: 5, confidenceLevel: 95 } }
          ]
        };
      default:
        return {
          title: 'Risk Management',
          icon: Shield,
          color: 'blue',
          methods: [
            { value: 'fixed_percentage', label: 'Fixed Percentage', params: { percentage: 2 } }
          ]
        };
    }
  };

  const config = getBlockConfig(data.type);
  const IconComponent = config.icon;

  // Ensure parameters exist and have default values
  const parameters = data.parameters || {};
  const currentMethod = config.methods.find(method => method.value === parameters.method) || config.methods[0];

  const handleMethodChange = (method: string) => {
    const selectedMethod = config.methods.find(m => m.value === method);
    if (selectedMethod) {
      data.onUpdate({
        parameters: {
          ...parameters,
          method,
          ...selectedMethod.params
        }
      });
    }
  };

  const handleParameterChange = (param: string, value: any) => {
    data.onUpdate({
      parameters: {
        ...parameters,
        [param]: parseFloat(value) || value
      }
    });
  };

  const renderParameterInputs = () => {
    if (!currentMethod) return null;

    return Object.entries(currentMethod.params).map(([param, defaultValue]) => {
      const currentValue = parameters[param] ?? defaultValue;
      
      // Special handling for different parameter types
      if (param === 'trailType') {
        return (
          <div key={param} className="space-y-1">
            <label className="text-xs font-medium block">Trail Type</label>
            <Select value={currentValue} onValueChange={value => handleParameterChange(param, value)}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="percentage">Percentage</SelectItem>
                <SelectItem value="amount">Fixed Amount</SelectItem>
              </SelectContent>
            </Select>
          </div>
        );
      }

      return (
        <div key={param} className="space-y-1">
          <label className="text-xs font-medium block capitalize">
            {param.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
          </label>
          <Input
            type="number"
            value={currentValue}
            onChange={e => handleParameterChange(param, e.target.value)}
            className="h-8 text-xs"
            min={0}
            step={param.includes('percentage') || param.includes('ratio') || param.includes('multiplier') ? 0.1 : 1}
          />
        </div>
      );
    });
  };

  const getColorClasses = (color: string) => {
    switch (color) {
      case 'red':
        return { bg: 'bg-red-500/10', text: 'text-red-500' };
      case 'green':
        return { bg: 'bg-green-500/10', text: 'text-green-500' };
      case 'orange':
        return { bg: 'bg-orange-500/10', text: 'text-orange-500' };
      default:
        return { bg: 'bg-blue-500/10', text: 'text-blue-500' };
    }
  };

  const colorClasses = getColorClasses(config.color);

  return (
    <>
      {/* Input handle */}
      <Handle
        type="target"
        position={Position.Left}
        id="input"
        className="w-3 h-3 bg-gray-400 border-2 border-white"
      />

      <Card className={`w-64 ${selected ? 'ring-2 ring-primary' : ''} ${data.isEntryBlock ? 'border-primary' : ''} ${data.hasError ? 'border-red-500' : ''}`}>
        <CardHeader className="p-3 pb-0 flex flex-row items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`w-6 h-6 rounded-full ${colorClasses.bg} flex items-center justify-center`}>
              <IconComponent className={`h-3 w-3 ${colorClasses.text}`} />
            </div>
            <CardTitle className="text-sm font-medium">{config.title}</CardTitle>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 text-destructive"
              onClick={(e) => {
                e.stopPropagation();
                data.onRemove();
              }}
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </CardHeader>
        <CardContent className="p-3 pt-2 space-y-3">
          {/* Method Selection */}
          <div className="space-y-1">
            <label className="text-xs font-medium block">Method</label>
            <Select value={parameters.method || currentMethod.value} onValueChange={handleMethodChange}>
              <SelectTrigger className="h-8 text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {config.methods.map(method => (
                  <SelectItem key={method.value} value={method.value}>
                    {method.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Parameter Inputs */}
          <div className="space-y-2">
            {renderParameterInputs()}
          </div>

          {/* Error Messages */}
          {data.hasError && data.errorMessages && (
            <div className="text-xs text-red-500 bg-red-50 p-2 rounded">
              {data.errorMessages.map((msg, idx) => (
                <div key={idx}>{msg}</div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Output handle */}
      <Handle
        type="source"
        position={Position.Right}
        id="output"
        className="w-3 h-3 bg-gray-400 border-2 border-white"
      />
    </>
  );
};

export default RiskManagementBlock;

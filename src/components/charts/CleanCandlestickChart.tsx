import React, { useRef, useEffect, useState, useCallback } from 'react';
import { format } from 'date-fns';
import { FocusedOHLCVData } from '@/services/tradeAnalysisService';

interface TradeMarker {
  timestamp: number;
  price: number;
  type: 'entry' | 'exit';
  label: string;
  color: string;
  tradeIndex?: number; // Add trade index for navigation
}

interface CleanCandlestickChartProps {
  symbol: string;
  data: FocusedOHLCVData[];
  tradeMarkers: TradeMarker[];
  height?: number;
  className?: string;
  currentTradeIndex?: number;
  onNavigateToTrade?: (tradeIndex: number) => void;
  allTrades?: any[]; // All trades for timeline navigation
}

const CleanCandlestickChart: React.FC<CleanCandlestickChartProps> = ({
  symbol,
  data,
  tradeMarkers,
  height = 400,
  className = '',
  currentTradeIndex,
  onNavigateToTrade,
  allTrades = []
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [canvasSize, setCanvasSize] = useState({ width: 800, height });

  // TradingView-style state
  const [timeOffset, setTimeOffset] = useState(0); // Time offset in milliseconds (how much we've panned)
  const [timeZoom, setTimeZoom] = useState(1); // Time zoom level (1 = show all data)
  const [priceScale, setPriceScale] = useState(1); // Price zoom level
  const [isDragging, setIsDragging] = useState(false);
  const [isPriceScaling, setIsPriceScaling] = useState(false);
  const [lastMousePos, setLastMousePos] = useState({ x: 0, y: 0 });
  const [crosshair, setCrosshair] = useState<{ x: number; y: number; price: number; time: number } | null>(null);

  // Timeline navigation logic
  const canNavigatePrevious = currentTradeIndex !== undefined && currentTradeIndex > 0;
  const canNavigateNext = currentTradeIndex !== undefined && currentTradeIndex < allTrades.length - 1;

  const handlePreviousTrade = () => {
    if (canNavigatePrevious && onNavigateToTrade && currentTradeIndex !== undefined) {
      onNavigateToTrade(currentTradeIndex - 1);
    }
  };

  const handleNextTrade = () => {
    if (canNavigateNext && onNavigateToTrade && currentTradeIndex !== undefined) {
      onNavigateToTrade(currentTradeIndex + 1);
    }
  };

  // Reset chart view
  const resetView = () => {
    setTimeOffset(0);
    setTimeZoom(1);
    setPriceScale(1);
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'ArrowLeft') {
        event.preventDefault();
        handlePreviousTrade();
      } else if (event.key === 'ArrowRight') {
        event.preventDefault();
        handleNextTrade();
      } else if (event.key === 'r' || event.key === 'R') {
        // Reset view with 'R' key
        event.preventDefault();
        resetView();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [currentTradeIndex, canNavigatePrevious, canNavigateNext]);

  // Handle canvas resize
  useEffect(() => {
    const updateCanvasSize = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setCanvasSize({ width: rect.width, height: height - 70 }); // Account for larger header
      }
    };

    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);
    return () => window.removeEventListener('resize', updateCanvasSize);
  }, [height]);

  // Calculate chart bounds with proper TradingView-style behavior
  const chartBounds = React.useMemo(() => {
    if (data.length === 0) {
      return {
        xMin: Date.now() - 24 * 60 * 60 * 1000,
        xMax: Date.now(),
        yMin: 100,
        yMax: 200
      };
    }

    const timestamps = data.map(d => d.timestamp);
    const prices = data.flatMap(d => [d.high, d.low]);

    // Include trade marker prices in bounds calculation
    const tradeMarkerPrices = tradeMarkers.map(m => m.price);
    const allPrices = [...prices, ...tradeMarkerPrices];

    // Base data bounds
    const baseXMin = Math.min(...timestamps);
    const baseXMax = Math.max(...timestamps);
    const baseYMin = Math.min(...allPrices);
    const baseYMax = Math.max(...allPrices);

    // TradingView-style X-axis calculation
    const baseTimeRange = baseXMax - baseXMin;

    // Apply time zoom (smaller zoom = more data visible)
    const visibleTimeRange = baseTimeRange / timeZoom;

    // Calculate the center point for the visible window
    const timeCenter = baseXMin + baseTimeRange / 2;

    // Apply time offset (panning) - this shifts the center
    const adjustedTimeCenter = timeCenter + timeOffset;

    // Calculate final visible time bounds
    const xMin = adjustedTimeCenter - visibleTimeRange / 2;
    const xMax = adjustedTimeCenter + visibleTimeRange / 2;

    // TradingView-style Y-axis: Apply price scaling with proper padding
    const basePriceRange = baseYMax - baseYMin;

    // Ensure minimum price range to prevent gaps
    const minPriceRange = Math.max(basePriceRange * 0.1, 1.0);
    const effectivePriceRange = Math.max(basePriceRange, minPriceRange);

    const scaledPriceRange = effectivePriceRange / priceScale;
    const priceCenter = (baseYMin + baseYMax) / 2;

    // Add 2% padding to Y-axis for better visibility
    const yPadding = scaledPriceRange * 0.02;
    const yMin = priceCenter - scaledPriceRange / 2 - yPadding;
    const yMax = priceCenter + scaledPriceRange / 2 + yPadding;

    return {
      xMin,
      xMax,
      yMin,
      yMax
    };
  }, [data, tradeMarkers, timeOffset, timeZoom, priceScale]);

  // Handle zoom with mouse wheel (TradingView style)
  const handleWheel = useCallback((event: WheelEvent) => {
    event.preventDefault();

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const mouseX = event.clientX - rect.left;

    const margin = { top: 20, right: 80, bottom: 50, left: 20 };
    const chartWidth = canvasSize.width - margin.left - margin.right;

    // Check if mouse is over price scale (right side)
    if (mouseX > margin.left + chartWidth && mouseX <= canvasSize.width - margin.right) {
      // Price scaling (Y-axis zoom)
      const scaleFactor = event.deltaY > 0 ? 0.9 : 1.1;
      setPriceScale(prev => Math.max(0.1, Math.min(10, prev * scaleFactor)));
    } else if (mouseX >= margin.left && mouseX <= margin.left + chartWidth) {
      // Time scaling (X-axis zoom) - TradingView style
      const zoomFactor = event.deltaY > 0 ? 0.9 : 1.1;

      // Calculate the time at mouse position before zoom
      const relativeX = (mouseX - margin.left) / chartWidth;
      const timeRange = chartBounds.xMax - chartBounds.xMin;
      const mouseTime = chartBounds.xMin + relativeX * timeRange;

      // Apply time zoom
      setTimeZoom(prev => Math.max(0.1, Math.min(10, prev * zoomFactor)));

      // Adjust time offset to keep mouse position stable during zoom
      const currentCenter = chartBounds.xMin + timeRange / 2;
      const offsetToMouse = mouseTime - currentCenter;
      const zoomAdjustment = offsetToMouse * (1 - 1/zoomFactor);
      setTimeOffset(prev => prev + zoomAdjustment);
    }
  }, [chartBounds, canvasSize]);

  // TradingView-style mouse handlers
  const handleMouseDown = useCallback((event: MouseEvent) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const mouseX = event.clientX - rect.left;
    const mouseY = event.clientY - rect.top;

    const margin = { top: 20, right: 80, bottom: 50, left: 20 };
    const chartWidth = canvasSize.width - margin.left - margin.right;

    // Check if mouse is over price scale (right side) for price scaling
    if (mouseX > margin.left + chartWidth && mouseX <= canvasSize.width - margin.right) {
      setIsPriceScaling(true);
    } else if (mouseX >= margin.left && mouseX <= margin.left + chartWidth) {
      setIsDragging(true);
    }

    setLastMousePos({ x: event.clientX, y: event.clientY });
  }, [canvasSize]);

  const handleNativeMouseMove = useCallback((event: MouseEvent) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;

    if (isDragging) {
      // TradingView-style horizontal panning - convert pixel movement to time movement
      const deltaX = event.clientX - lastMousePos.x;

      // Calculate how much time this pixel movement represents
      const timeRange = chartBounds.xMax - chartBounds.xMin;
      const margin = { top: 20, right: 80, bottom: 50, left: 20 };
      const chartWidth = canvasSize.width - margin.left - margin.right;
      const pixelsPerMs = chartWidth / timeRange;
      const deltaTime = deltaX / pixelsPerMs;

      // Apply time offset - positive deltaX should move chart right (show earlier data)
      setTimeOffset(prev => prev - deltaTime);
      setLastMousePos({ x: event.clientX, y: event.clientY });
    } else if (isPriceScaling) {
      // TradingView-style price scaling
      const deltaY = event.clientY - lastMousePos.y;
      const scaleFactor = 1 + (deltaY * 0.01); // Sensitivity adjustment
      setPriceScale(prev => Math.max(0.1, Math.min(10, prev * scaleFactor)));
      setLastMousePos({ x: event.clientX, y: event.clientY });
    } else {
      // Update crosshair position for TradingView-style info display
      const margin = { top: 20, right: 80, bottom: 50, left: 20 };
      const chartWidth = canvasSize.width - margin.left - margin.right;
      const chartHeight = canvasSize.height - margin.top - margin.bottom;

      if (x >= margin.left && x <= margin.left + chartWidth &&
          y >= margin.top && y <= margin.top + chartHeight) {

        // Calculate price and time at crosshair
        const timeRange = chartBounds.xMax - chartBounds.xMin;
        const priceRange = chartBounds.yMax - chartBounds.yMin;
        const relativeX = (x - margin.left) / chartWidth;
        const relativeY = (y - margin.top) / chartHeight;

        const time = chartBounds.xMin + relativeX * timeRange;
        const price = chartBounds.yMax - relativeY * priceRange;

        setCrosshair({ x, y, price, time });
      } else {
        setCrosshair(null);
      }
    }
  }, [isDragging, isPriceScaling, lastMousePos, canvasSize, chartBounds]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
    setIsPriceScaling(false);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsDragging(false);
    setIsPriceScaling(false);
    setCrosshair(null);
  }, []);

  // Add event listeners
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    canvas.addEventListener('wheel', handleWheel);
    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('mousemove', handleNativeMouseMove);
    canvas.addEventListener('mouseup', handleMouseUp);
    canvas.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      canvas.removeEventListener('wheel', handleWheel);
      canvas.removeEventListener('mousedown', handleMouseDown);
      canvas.removeEventListener('mousemove', handleNativeMouseMove);
      canvas.removeEventListener('mouseup', handleMouseUp);
      canvas.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, [handleWheel, handleMouseDown, handleNativeMouseMove, handleMouseUp, handleMouseLeave]);



  // Draw the chart
  const drawChart = useCallback(() => {
    const canvas = canvasRef.current;
    const ctx = canvas?.getContext('2d');
    if (!canvas || !ctx || data.length === 0) return;

    // Set canvas size for high DPI displays
    const dpr = window.devicePixelRatio || 1;
    canvas.width = canvasSize.width * dpr;
    canvas.height = canvasSize.height * dpr;
    canvas.style.width = `${canvasSize.width}px`;
    canvas.style.height = `${canvasSize.height}px`;
    ctx.scale(dpr, dpr);

    // Clear canvas with dark background
    ctx.fillStyle = '#101010';
    ctx.fillRect(0, 0, canvasSize.width, canvasSize.height);

    // Chart margins
    const margin = { top: 20, right: 80, bottom: 50, left: 20 };
    const chartWidth = canvasSize.width - margin.left - margin.right;
    const chartHeight = canvasSize.height - margin.top - margin.bottom;

    // Grid removed for cleaner appearance

    // Draw candlesticks
    drawCandlesticks(ctx, margin, chartWidth, chartHeight);

    // Draw trade markers
    drawTradeMarkers(ctx, margin, chartWidth, chartHeight);

    // Draw axes with TradingView-style info
    drawYAxisLabels(ctx, margin, chartWidth, chartHeight);
    drawXAxisLabels(ctx, margin, chartWidth, chartHeight);

    // Draw crosshair and price/time info (TradingView style)
    if (crosshair) {
      drawCrosshair(ctx, crosshair, margin, chartWidth, chartHeight);
    }
  }, [data, canvasSize, tradeMarkers, chartBounds, crosshair]);

  // Grid function removed for cleaner chart appearance

  // Draw candlesticks
  const drawCandlesticks = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (data.length === 0) return;

    const timeRange = chartBounds.xMax - chartBounds.xMin;
    const priceRange = chartBounds.yMax - chartBounds.yMin;

    // Filter candles that are visible in current viewport
    const visibleCandles = data.filter(candle =>
      candle.timestamp >= chartBounds.xMin && candle.timestamp <= chartBounds.xMax
    );

    if (visibleCandles.length === 0) return;

    // Calculate optimal candle width based on visible timeframe and zoom level
    const visibleTimeRange = timeRange;
    const pixelsPerMs = chartWidth / visibleTimeRange;

    // Estimate time between candles (assuming regular intervals)
    const avgTimeBetweenCandles = visibleCandles.length > 1
      ? (visibleCandles[visibleCandles.length - 1].timestamp - visibleCandles[0].timestamp) / (visibleCandles.length - 1)
      : 24 * 60 * 60 * 1000; // Default to 1 day

    // Calculate candle width based on time spacing and horizontal zoom
    let candleWidth = Math.max(2, Math.min(20, pixelsPerMs * avgTimeBetweenCandles * 0.6));

    // Adjust for time zoom (more data visible = thinner candles)
    candleWidth = Math.max(1, candleWidth);

    console.log(`[Chart] Candle rendering:`, {
      visibleCandles: visibleCandles.length,
      timeRange: timeRange,
      candleWidth: candleWidth.toFixed(2),
      priceScale: priceScale.toFixed(2),
      pixelsPerMs: pixelsPerMs.toFixed(6)
    });

    // Draw each visible candle with TradingView-style precision
    visibleCandles.forEach((candle) => {
      // Calculate X position based on timestamp (same as trade markers)
      const x = margin.left + ((candle.timestamp - chartBounds.xMin) / timeRange) * chartWidth;

      // Calculate Y positions based on prices with pixel precision
      const highY = Math.round(margin.top + chartHeight - ((candle.high - chartBounds.yMin) / priceRange) * chartHeight);
      const lowY = Math.round(margin.top + chartHeight - ((candle.low - chartBounds.yMin) / priceRange) * chartHeight);
      const openY = Math.round(margin.top + chartHeight - ((candle.open - chartBounds.yMin) / priceRange) * chartHeight);
      const closeY = Math.round(margin.top + chartHeight - ((candle.close - chartBounds.yMin) / priceRange) * chartHeight);

      // Skip candles that are outside the visible area
      if (x < margin.left - candleWidth || x > margin.left + chartWidth + candleWidth) {
        return;
      }

      const isGreen = candle.close >= candle.open;
      const isDoji = Math.abs(candle.close - candle.open) < (candle.high - candle.low) * 0.05;

      // TradingView-style colors - clean and professional
      const bullishColor = '#26a69a'; // TradingView green
      const bearishColor = '#ef5350'; // TradingView red
      const dojiColor = '#9e9e9e'; // Gray for doji

      const color = isDoji ? dojiColor : (isGreen ? bullishColor : bearishColor);
      const wickColor = color;

      // Draw wick (high-low line) with TradingView precision
      ctx.strokeStyle = wickColor;
      ctx.lineWidth = Math.max(1, Math.min(3, candleWidth * 0.2));
      ctx.lineCap = 'butt'; // Sharp ends like TradingView

      ctx.beginPath();
      ctx.moveTo(Math.round(x), highY);
      ctx.lineTo(Math.round(x), lowY);
      ctx.stroke();

      // Calculate body dimensions
      const bodyTop = Math.min(openY, closeY);
      const bodyHeight = Math.max(Math.abs(closeY - openY), 1); // Minimum 1px for doji
      const bodyLeft = Math.round(x - candleWidth / 2);
      const bodyWidth = Math.max(1, Math.round(candleWidth));

      // Draw body with TradingView-style fill
      if (isDoji || bodyHeight <= 1) {
        // Doji or very small body - draw as line
        ctx.strokeStyle = color;
        ctx.lineWidth = Math.max(1, bodyWidth);
        ctx.beginPath();
        ctx.moveTo(bodyLeft, Math.round(bodyTop));
        ctx.lineTo(bodyLeft + bodyWidth, Math.round(bodyTop));
        ctx.stroke();
      } else {
        // Regular candle body
        if (isGreen) {
          // Bullish candle - hollow (outline only) like TradingView
          ctx.strokeStyle = bullishColor;
          ctx.lineWidth = Math.max(1, Math.min(2, bodyWidth * 0.1));
          ctx.strokeRect(bodyLeft, bodyTop, bodyWidth, bodyHeight);
        } else {
          // Bearish candle - filled like TradingView
          ctx.fillStyle = bearishColor;
          ctx.fillRect(bodyLeft, bodyTop, bodyWidth, bodyHeight);

          // Add subtle border for definition
          ctx.strokeStyle = bearishColor;
          ctx.lineWidth = 1;
          ctx.strokeRect(bodyLeft, bodyTop, bodyWidth, bodyHeight);
        }
      }
    });
  };

  // Draw trade markers
  const drawTradeMarkers = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    if (tradeMarkers.length === 0) {
      console.log('[Chart] No trade markers to draw');
      return;
    }

    console.log(`[Chart] Drawing ${tradeMarkers.length} trade markers`);
    console.log(`[Chart] Chart bounds:`, chartBounds);

    const timeRange = chartBounds.xMax - chartBounds.xMin;

    tradeMarkers.forEach((marker, index) => {
      // Calculate position using same coordinate system as candlesticks
      const x = margin.left + ((marker.timestamp - chartBounds.xMin) / timeRange) * chartWidth;
      const y = margin.top + chartHeight - ((marker.price - chartBounds.yMin) / (chartBounds.yMax - chartBounds.yMin)) * chartHeight;

      console.log(`[Chart] Marker ${index}:`, {
        timestamp: new Date(marker.timestamp).toISOString(),
        price: marker.price,
        x: x.toFixed(2),
        y: y.toFixed(2),
        chartBounds: {
          xMin: new Date(chartBounds.xMin).toISOString(),
          xMax: new Date(chartBounds.xMax).toISOString(),
          yMin: chartBounds.yMin.toFixed(2),
          yMax: chartBounds.yMax.toFixed(2)
        }
      });

      // Skip markers that are outside the visible viewport
      if (marker.timestamp < chartBounds.xMin || marker.timestamp > chartBounds.xMax) {
        console.log(`[Chart] Skipping marker ${index} - outside viewport timeframe`);
        return;
      }

      // Skip if marker is outside chart area (with small buffer)
      if (x < margin.left - 20 || x > margin.left + chartWidth + 20) {
        console.log(`[Chart] Skipping marker ${index} - X position outside chart area (${x.toFixed(2)})`);
        return;
      }

      const isCurrentTrade = marker.tradeIndex === currentTradeIndex;
      const markerSize = isCurrentTrade ? 10 : 7; // Larger markers for better visibility

      // Trade markers are stationary - they don't move with mouse
      // Position is calculated purely from timestamp and price data
      const markerX = x;
      const markerY = y;

      // Keep markers within visible chart bounds
      const clampedX = Math.max(margin.left, Math.min(markerX, margin.left + chartWidth));
      const clampedY = Math.max(margin.top, Math.min(markerY, margin.top + chartHeight));

      // Draw clean glowing trade marker - stationary position based on setup data
      // Add glow effect for the trade marker
      ctx.shadowColor = marker.color;
      ctx.shadowBlur = isCurrentTrade ? 15 : 8;
      ctx.shadowOffsetX = 0;
      ctx.shadowOffsetY = 0;

      // Draw outer glow ring for current trade
      if (isCurrentTrade) {
        ctx.fillStyle = marker.color + '40'; // Add transparency
        ctx.beginPath();
        ctx.arc(clampedX, clampedY, markerSize + 6, 0, 2 * Math.PI);
        ctx.fill();
      }

      // Draw main marker circle with enhanced glow
      ctx.fillStyle = marker.color;
      ctx.beginPath();
      ctx.arc(clampedX, clampedY, markerSize, 0, 2 * Math.PI);
      ctx.fill();

      // Add white center dot for better visibility
      ctx.shadowBlur = 0;
      ctx.fillStyle = '#FFFFFF';
      ctx.beginPath();
      ctx.arc(clampedX, clampedY, markerSize * 0.3, 0, 2 * Math.PI);
      ctx.fill();

      // Reset shadow
      ctx.shadowColor = 'transparent';
      ctx.shadowBlur = 0;
    });
  };

  // Draw Y-axis price labels with TradingView-style precision
  const drawYAxisLabels = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    ctx.font = '11px -apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif';
    ctx.textAlign = 'left';

    const priceRange = chartBounds.yMax - chartBounds.yMin;
    const minPrice = chartBounds.yMin;

    // Calculate optimal number of price levels based on chart height
    const optimalLevels = Math.max(5, Math.min(12, Math.floor(chartHeight / 40)));
    const priceStep = priceRange / (optimalLevels - 1);

    // Determine decimal places based on price range
    const decimalPlaces = priceRange < 1 ? 4 : priceRange < 10 ? 3 : 2;

    for (let i = 0; i < optimalLevels; i++) {
      const price = minPrice + i * priceStep;
      const y = Math.round(margin.top + chartHeight - ((price - minPrice) / priceRange) * chartHeight);

      // Ensure labels are properly positioned within chart bounds
      if (y >= margin.top && y <= margin.top + chartHeight) {
        // Format price with appropriate precision
        const formattedPrice = price >= 1000
          ? price.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
          : price.toFixed(decimalPlaces);

        // Draw price label with background for better readability
        const text = `$${formattedPrice}`;
        const textWidth = ctx.measureText(text).width;

        // Background
        ctx.fillStyle = 'rgba(16, 16, 16, 0.8)';
        ctx.fillRect(margin.left + chartWidth + 4, y - 7, textWidth + 8, 14);

        // Text
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.fillText(text, margin.left + chartWidth + 8, y + 3);
      }
    }
  };

  // Draw X-axis time labels with TradingView-style formatting
  const drawXAxisLabels = (
    ctx: CanvasRenderingContext2D,
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    ctx.fillStyle = 'rgba(255, 255, 255, 0.7)';
    ctx.font = '11px -apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif';
    ctx.textAlign = 'center';

    const timeRange = chartBounds.xMax - chartBounds.xMin;
    const minTime = chartBounds.xMin;

    // Calculate optimal number of time labels based on chart width
    const optimalLabels = Math.max(3, Math.min(8, Math.floor(chartWidth / 120)));
    const timeStep = timeRange / (optimalLabels - 1);

    for (let i = 0; i < optimalLabels; i++) {
      const time = minTime + i * timeStep;
      const x = Math.round(margin.left + ((time - minTime) / timeRange) * chartWidth);

      // Ensure labels are properly positioned within chart bounds
      if (x >= margin.left && x <= margin.left + chartWidth) {
        // Format time based on range - more intelligent formatting
        let timeLabel: string;
        if (timeRange < 24 * 60 * 60 * 1000) { // Less than 1 day
          timeLabel = format(new Date(time), 'HH:mm');
        } else if (timeRange < 7 * 24 * 60 * 60 * 1000) { // Less than 1 week
          timeLabel = format(new Date(time), 'MMM dd HH:mm');
        } else {
          timeLabel = format(new Date(time), 'MMM dd');
        }

        // Draw time label with background
        const textWidth = ctx.measureText(timeLabel).width;

        // Background
        ctx.fillStyle = 'rgba(16, 16, 16, 0.8)';
        ctx.fillRect(x - textWidth / 2 - 4, margin.top + chartHeight + 4, textWidth + 8, 14);

        // Text
        ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.fillText(timeLabel, x, margin.top + chartHeight + 15);
      }
    }
  };

  // Draw TradingView-style crosshair and price/time info
  const drawCrosshair = (
    ctx: CanvasRenderingContext2D,
    crosshair: { x: number; y: number; price: number; time: number },
    margin: any,
    chartWidth: number,
    chartHeight: number
  ) => {
    const { x, y, price, time } = crosshair;

    // Draw subtle crosshair lines like TradingView
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.2)';
    ctx.lineWidth = 1;
    ctx.setLineDash([4, 4]);

    // Horizontal line (price level) - only in chart area
    ctx.beginPath();
    ctx.moveTo(margin.left, y);
    ctx.lineTo(margin.left + chartWidth, y);
    ctx.stroke();

    // Vertical line (time level) - only in chart area
    ctx.beginPath();
    ctx.moveTo(x, margin.top);
    ctx.lineTo(x, margin.top + chartHeight);
    ctx.stroke();

    ctx.setLineDash([]);

    // Price label on Y-axis (right side) - TradingView style
    const priceText = `$${price.toFixed(2)}`;
    ctx.font = '11px -apple-system, BlinkMacSystemFont, "SF Pro Text", system-ui, sans-serif';
    ctx.textAlign = 'left';
    const priceTextWidth = ctx.measureText(priceText).width;

    // Price label with TradingView-style background
    ctx.fillStyle = 'rgba(38, 166, 154, 0.9)'; // TradingView green background
    ctx.fillRect(margin.left + chartWidth + 1, y - 9, priceTextWidth + 10, 18);

    // Price label border
    ctx.strokeStyle = 'rgba(38, 166, 154, 1)';
    ctx.lineWidth = 1;
    ctx.strokeRect(margin.left + chartWidth + 1, y - 9, priceTextWidth + 10, 18);

    // Price label text
    ctx.fillStyle = '#ffffff';
    ctx.fillText(priceText, margin.left + chartWidth + 6, y + 3);

    // Time label on X-axis (bottom) - TradingView style
    const timeText = format(new Date(time), 'HH:mm');
    ctx.textAlign = 'center';
    const timeTextWidth = ctx.measureText(timeText).width;

    // Time label with TradingView-style background
    ctx.fillStyle = 'rgba(38, 166, 154, 0.9)';
    ctx.fillRect(x - timeTextWidth / 2 - 5, margin.top + chartHeight + 1, timeTextWidth + 10, 18);

    // Time label border
    ctx.strokeStyle = 'rgba(38, 166, 154, 1)';
    ctx.lineWidth = 1;
    ctx.strokeRect(x - timeTextWidth / 2 - 5, margin.top + chartHeight + 1, timeTextWidth + 10, 18);

    // Time label text
    ctx.fillStyle = '#ffffff';
    ctx.fillText(timeText, x, margin.top + chartHeight + 13);
  };

  // Redraw chart when state changes
  useEffect(() => {
    drawChart();
  }, [drawChart]);

  // React mouse handler - native handlers do the heavy lifting
  const handleReactMouseMove = () => {
    // Native event handlers handle all the logic for TradingView-style behavior
  };

  // Handle clicks on trade markers
  const handleCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current || !onNavigateToTrade) return;

    const rect = canvasRef.current.getBoundingClientRect();
    const mouseX = e.clientX - rect.left;
    const mouseY = e.clientY - rect.top;

    const margin = { top: 20, right: 80, bottom: 50, left: 20 };
    const chartWidth = canvasSize.width - margin.left - margin.right;
    const chartHeight = canvasSize.height - margin.top - margin.bottom;
    const timeRange = chartBounds.xMax - chartBounds.xMin;

    // Check if click is near any trade marker
    tradeMarkers.forEach((marker) => {
      const x = margin.left + ((marker.timestamp - chartBounds.xMin) / timeRange) * chartWidth;
      const y = margin.top + chartHeight - ((marker.price - chartBounds.yMin) / (chartBounds.yMax - chartBounds.yMin)) * chartHeight;

      // Check if click is within 15 pixels of the marker
      const distance = Math.sqrt(Math.pow(mouseX - x, 2) + Math.pow(mouseY - y, 2));
      if (distance <= 15 && marker.tradeIndex !== undefined) {
        onNavigateToTrade(marker.tradeIndex);
      }
    });
  };

  // Show error state if no data
  if (data.length === 0) {
    return (
      <div className={`w-full bg-[#101010] rounded-lg border border-red-500/20 ${className}`} style={{ height }}>
        <div className="p-3 border-b border-red-500/20">
          <h3 className="text-red-400 text-sm font-medium">{symbol} - Chart Data Error</h3>
        </div>
        <div className="flex items-center justify-center h-full">
          <div className="text-center p-6">
            <div className="text-red-400 text-lg mb-2">📊 No Data Available</div>
            <div className="text-white/60 text-sm">
              Unable to load real market data for this symbol.
              <br />
              Please verify the symbol and try again.
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div ref={containerRef} className={`w-full bg-[#101010] rounded-lg border border-[#1A1A1C] shadow-[inset_0_0_20px_rgba(34,197,94,0.1)] ${className}`} style={{ height }}>
      {/* Chart Canvas */}
      <div className="relative" style={{ height }}>
        <canvas
          ref={canvasRef}
          className="cursor-crosshair w-full"
          style={{ height }}
          onMouseMove={handleReactMouseMove}
          onMouseLeave={handleMouseLeave}
          onClick={handleCanvasClick}
        />

        {/* Reset Controls - positioned away from navigation */}
        {(priceScale !== 1 || timeOffset !== 0 || timeZoom !== 1) && (
          <div className="absolute top-4 right-4 bg-black/90 backdrop-blur-sm rounded-md px-2 py-1 text-white/60 text-xs border border-white/10">
            <button
              onClick={resetView}
              className="hover:text-white/90 transition-colors"
              title="Reset chart view (R)"
            >
              Reset View
            </button>
          </div>
        )}

        {/* Clean status indicator */}
        {crosshair && (
          <div className="absolute top-4 left-4 bg-black/80 backdrop-blur-sm rounded-md px-3 py-2 text-white/70 text-xs border border-white/10">
            <div className="font-mono">${crosshair.price.toFixed(2)}</div>
            <div className="text-white/50 text-[10px]">{format(new Date(crosshair.time), 'MMM dd HH:mm')}</div>
          </div>
        )}

        {/* Minimal help - only show on hover */}
        <div className="absolute bottom-4 right-4 opacity-0 hover:opacity-100 transition-opacity bg-black/90 backdrop-blur-sm rounded-md px-3 py-2 text-white/50 text-xs border border-white/10">
          <div>Drag: Pan | Scroll: Zoom</div>
          <div>Price area: Y-scale</div>
        </div>
      </div>
    </div>
  );
};

export default CleanCandlestickChart;

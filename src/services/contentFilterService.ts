import { supabase } from '@/integrations/supabase/client';
import { checkContentFilter, type ContentFilterResult } from './adminService';

/**
 * Validate agent content before publishing to marketplace
 */
export async function validateAgentContent(
  name: string, 
  description?: string
): Promise<{
  isValid: boolean;
  violations: ContentFilterResult['violations'];
  message?: string;
}> {
  try {
    // Combine name and description for filtering
    const content = [name, description].filter(Boolean).join(' ');
    
    if (!content.trim()) {
      return {
        isValid: false,
        violations: [],
        message: 'Agent name is required'
      };
    }

    // Check content against bad words filter
    const filterResult = await checkContentFilter(content);
    
    if (filterResult.has_violations) {
      const highSeverityViolations = filterResult.violations.filter(v => v.severity === 'high');
      const mediumSeverityViolations = filterResult.violations.filter(v => v.severity === 'medium');
      
      // Block high severity violations completely
      if (highSeverityViolations.length > 0) {
        return {
          isValid: false,
          violations: filterResult.violations,
          message: `Content contains prohibited words: ${highSeverityViolations.map(v => v.word).join(', ')}. Please revise your agent name and description.`
        };
      }
      
      // Warn about medium severity violations but allow with warning
      if (mediumSeverityViolations.length > 0) {
        return {
          isValid: false,
          violations: filterResult.violations,
          message: `Content contains flagged words: ${mediumSeverityViolations.map(v => v.word).join(', ')}. Please consider revising for better marketplace acceptance.`
        };
      }
    }

    return {
      isValid: true,
      violations: filterResult.violations
    };
  } catch (error) {
    console.error('Error validating agent content:', error);
    // Allow content if filter service fails (fail open)
    return {
      isValid: true,
      violations: [],
      message: 'Content validation service unavailable, proceeding with publication'
    };
  }
}

/**
 * Check if user is banned from marketplace
 */
export async function checkUserMarketplaceBan(): Promise<{
  isBanned: boolean;
  reason?: string;
  expiresAt?: string;
}> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      return { isBanned: false };
    }

    // Check if user is banned
    const { data: ban } = await supabase
      .from('user_bans')
      .select('reason, expires_at, ban_type')
      .eq('user_id', user.id)
      .eq('is_active', true)
      .or('expires_at.is.null,expires_at.gt.now()')
      .in('ban_type', ['marketplace', 'full'])
      .single();

    if (ban) {
      return {
        isBanned: true,
        reason: ban.reason,
        expiresAt: ban.expires_at
      };
    }

    return { isBanned: false };
  } catch (error) {
    console.error('Error checking user ban status:', error);
    // Fail open - allow if check fails
    return { isBanned: false };
  }
}

/**
 * Comprehensive pre-publication validation
 */
export async function validateAgentPublication(
  name: string,
  description?: string,
  price?: number
): Promise<{
  canPublish: boolean;
  errors: string[];
  warnings: string[];
}> {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    // Check if user is banned
    const banCheck = await checkUserMarketplaceBan();
    if (banCheck.isBanned) {
      errors.push(`You are banned from the marketplace. Reason: ${banCheck.reason}`);
      if (banCheck.expiresAt) {
        errors.push(`Ban expires: ${new Date(banCheck.expiresAt).toLocaleDateString()}`);
      }
    }

    // Validate content
    const contentValidation = await validateAgentContent(name, description);
    if (!contentValidation.isValid) {
      if (contentValidation.violations.some(v => v.severity === 'high')) {
        errors.push(contentValidation.message || 'Content contains prohibited words');
      } else {
        warnings.push(contentValidation.message || 'Content contains flagged words');
      }
    }

    // Validate pricing if provided
    if (price !== undefined && price !== null) {
      if (price < 0) {
        errors.push('Price cannot be negative');
      }
      if (price > 10000) {
        warnings.push('Price is very high ($10,000+). Consider if this is appropriate for your agent.');
      }
    }

    // Basic content validation
    if (!name || name.trim().length < 3) {
      errors.push('Agent name must be at least 3 characters long');
    }

    if (name && name.length > 100) {
      errors.push('Agent name must be less than 100 characters');
    }

    if (description && description.length > 2000) {
      errors.push('Agent description must be less than 2000 characters');
    }

    return {
      canPublish: errors.length === 0,
      errors,
      warnings
    };
  } catch (error) {
    console.error('Error in publication validation:', error);
    return {
      canPublish: true, // Fail open
      errors: [],
      warnings: ['Validation service unavailable, proceeding with publication']
    };
  }
}

/**
 * Log content filter violation for admin review
 */
export async function logContentViolation(
  agentId: string,
  content: string,
  violations: ContentFilterResult['violations']
): Promise<void> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return;

    // Log the violation for admin review
    await supabase.rpc('log_admin_action', {
      p_action_type: 'content_filter',
      p_target_type: 'agent',
      p_target_id: agentId,
      p_details: {
        content_snippet: content.substring(0, 200),
        violations: violations,
        user_id: user.id,
        timestamp: new Date().toISOString()
      },
      p_notes: `Content filter triggered: ${violations.map(v => `${v.word} (${v.severity})`).join(', ')}`
    });
  } catch (error) {
    console.error('Error logging content violation:', error);
    // Don't throw - this is just for logging
  }
}

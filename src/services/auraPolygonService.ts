import axios from 'axios';

const POLYGON_API_KEY = import.meta.env.VITE_POLYGON_API_KEY || 'JAWMu4zOUA4W7HrLCOx2GU2Cpml4_Faz';
const BASE_URL = 'https://api.polygon.io';

// Interface for the stock data
export interface StockCandle {
  c: number; // close price
  h: number; // highest price
  l: number; // lowest price
  o: number; // open price
  t: number; // timestamp
  v: number; // volume
}

// Interface for the API response
interface PolygonAggregatesResponse {
  ticker: string;
  status: string;
  queryCount: number;
  resultsCount: number;
  adjusted: boolean;
  results: StockCandle[];
  request_id: string;
  count: number;
}

// Function to fetch stock data from Polygon API
export const fetchStockData = async (
  ticker: string,
  multiplier: number = 1,
  timespan: string = 'hour',
  from: string,
  to: string
): Promise<StockCandle[]> => {
  try {
    const url = `${BASE_URL}/v2/aggs/ticker/${ticker}/range/${multiplier}/${timespan}/${from}/${to}?adjusted=true&sort=asc&limit=5000&apiKey=${POLYGON_API_KEY}`;

    const response = await axios.get<PolygonAggregatesResponse>(url);

    // Accept both OK and DELAYED status as valid responses
    if (response.data.status === 'OK' || response.data.status === 'DELAYED') {
      // Check if we have results
      if (response.data.results && response.data.results.length > 0) {
        return response.data.results;
      } else {
        throw new Error('No data available for the selected timeframe');
      }
    } else {
      throw new Error(`Failed to fetch data: ${response.data.status}`);
    }
  } catch (error) {
    console.error('Error fetching stock data:', error);
    throw error;
  }
};

// Function to format date to YYYY-MM-DD
export const formatDate = (date: Date): string => {
  return date.toISOString().split('T')[0];
};

// Function to get date range for the last N days
export const getDateRangeForLastDays = (days: number): { from: string; to: string } => {
  const to = new Date();
  const from = new Date();
  from.setDate(from.getDate() - days);

  return {
    from: formatDate(from),
    to: formatDate(to)
  };
};

// Function to convert Polygon data to Lightweight Charts format
export const convertToChartData = (data: StockCandle[]) => {
  return data.map(candle => ({
    time: candle.t / 1000, // Convert milliseconds to seconds for Lightweight Charts
    open: candle.o,
    high: candle.h,
    low: candle.l,
    close: candle.c,
    volume: candle.v
  }));
};

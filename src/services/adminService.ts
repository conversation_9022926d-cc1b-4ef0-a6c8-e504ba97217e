import { supabase } from '@/integrations/supabase/client';

// Types for admin service
export interface AdminUser {
  id: string;
  email: string;
  full_name?: string;
  role: 'user' | 'admin';
  is_banned: boolean;
  banned_at?: string;
  banned_reason?: string;
  created_at: string;
}

export interface AdminActionLog {
  id: string;
  admin_id: string;
  action_type: string;
  target_type: string;
  target_id: string;
  details: any;
  notes?: string;
  created_at: string;
  admin_name?: string;
}

export interface UserBan {
  id: string;
  user_id: string;
  banned_by: string;
  reason: string;
  ban_type: 'marketplace' | 'full' | 'temporary';
  expires_at?: string;
  is_active: boolean;
  created_at: string;
  user_email?: string;
  admin_name?: string;
}

export interface BadWord {
  id: string;
  word: string;
  severity: 'low' | 'medium' | 'high';
  category: string;
  is_active: boolean;
  created_at: string;
}

export interface ContentFilterResult {
  has_violations: boolean;
  violations: Array<{
    word: string;
    severity: string;
    category: string;
  }>;
  violation_count: number;
}

/**
 * Check if current user is admin
 */
export async function isCurrentUserAdmin(): Promise<boolean> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return false;

    const { data, error } = await supabase.rpc('is_admin', { user_id: user.id });
    if (error) throw error;

    return data || false;
  } catch (error) {
    console.error('Error checking admin status:', error);
    return false;
  }
}

/**
 * Check if user has unlimited access (admin bypass)
 */
export async function hasUnlimitedAccess(): Promise<boolean> {
  return await isCurrentUserAdmin();
}

/**
 * Verify an agent as official
 */
export async function verifyAgent(agentId: string, notes?: string): Promise<void> {
  try {
    const { error } = await supabase.rpc('verify_agent', {
      p_agent_id: agentId,
      p_notes: notes
    });

    if (error) throw error;
  } catch (error) {
    console.error('Error verifying agent:', error);
    throw error;
  }
}

/**
 * Remove verification from an agent
 */
export async function unverifyAgent(agentId: string, notes?: string): Promise<void> {
  try {
    const { error } = await supabase.rpc('unverify_agent', {
      p_agent_id: agentId,
      p_notes: notes
    });

    if (error) throw error;
  } catch (error) {
    console.error('Error unverifying agent:', error);
    throw error;
  }
}

/**
 * Ban a user from the marketplace
 */
export async function banUser(
  userId: string, 
  reason: string, 
  banType: 'marketplace' | 'full' | 'temporary' = 'marketplace',
  expiresAt?: string
): Promise<void> {
  try {
    const { error } = await supabase.rpc('ban_user', {
      p_user_id: userId,
      p_reason: reason,
      p_ban_type: banType,
      p_expires_at: expiresAt
    });

    if (error) throw error;
  } catch (error) {
    console.error('Error banning user:', error);
    throw error;
  }
}

/**
 * Unban a user
 */
export async function unbanUser(userId: string, notes?: string): Promise<void> {
  try {
    const { error } = await supabase.rpc('unban_user', {
      p_user_id: userId,
      p_notes: notes
    });

    if (error) throw error;
  } catch (error) {
    console.error('Error unbanning user:', error);
    throw error;
  }
}

/**
 * Delete an agent (admin only)
 */
export async function adminDeleteAgent(agentId: string, reason?: string): Promise<void> {
  try {
    const { error } = await supabase.rpc('admin_delete_agent', {
      p_agent_id: agentId,
      p_reason: reason
    });

    if (error) throw error;
  } catch (error) {
    console.error('Error deleting agent:', error);
    throw error;
  }
}

/**
 * Check content for bad words
 */
export async function checkContentFilter(content: string): Promise<ContentFilterResult> {
  try {
    const { data, error } = await supabase.rpc('check_content_filter', {
      p_content: content
    });

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error checking content filter:', error);
    throw error;
  }
}

/**
 * Get admin action logs
 */
export async function getAdminActionLogs(limit: number = 50): Promise<AdminActionLog[]> {
  try {
    const { data, error } = await supabase
      .from('admin_action_logs')
      .select(`
        *,
        admin:profiles!admin_action_logs_admin_id_fkey(full_name)
      `)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;

    return data.map(log => ({
      ...log,
      admin_name: log.admin?.full_name || 'Unknown Admin'
    }));
  } catch (error) {
    console.error('Error fetching admin action logs:', error);
    throw error;
  }
}

/**
 * Get user bans
 */
export async function getUserBans(activeOnly: boolean = true): Promise<UserBan[]> {
  try {
    let query = supabase
      .from('user_bans')
      .select(`
        *,
        user:profiles!user_bans_user_id_fkey(email),
        admin:profiles!user_bans_banned_by_fkey(full_name)
      `)
      .order('created_at', { ascending: false });

    if (activeOnly) {
      query = query.eq('is_active', true);
    }

    const { data, error } = await query;
    if (error) throw error;

    return data.map(ban => ({
      ...ban,
      user_email: ban.user?.email || 'Unknown User',
      admin_name: ban.admin?.full_name || 'Unknown Admin'
    }));
  } catch (error) {
    console.error('Error fetching user bans:', error);
    throw error;
  }
}

/**
 * Get bad words filter
 */
export async function getBadWords(): Promise<BadWord[]> {
  try {
    const { data, error } = await supabase
      .from('bad_words_filter')
      .select('*')
      .order('word');

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching bad words:', error);
    throw error;
  }
}

/**
 * Add bad word to filter
 */
export async function addBadWord(
  word: string, 
  severity: 'low' | 'medium' | 'high' = 'medium',
  category: string = 'general'
): Promise<void> {
  try {
    const { error } = await supabase
      .from('bad_words_filter')
      .insert({
        word: word.toLowerCase(),
        severity,
        category,
        added_by: (await supabase.auth.getUser()).data.user?.id
      });

    if (error) throw error;
  } catch (error) {
    console.error('Error adding bad word:', error);
    throw error;
  }
}

/**
 * Remove bad word from filter
 */
export async function removeBadWord(wordId: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('bad_words_filter')
      .delete()
      .eq('id', wordId);

    if (error) throw error;
  } catch (error) {
    console.error('Error removing bad word:', error);
    throw error;
  }
}

/**
 * Get all users for admin management
 */
export async function getAllUsers(limit: number = 100): Promise<AdminUser[]> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching users:', error);
    throw error;
  }
}

// Intelligent Block Suggestions System
import { AgentB<PERSON> } from '@/services/agentService';
import { BlockType } from '@/hooks/useAgentBuilder';
import { Edge } from 'reactflow';
import { getBlockCategory } from '@/utils/canvasLayoutEngine';

export interface BlockSuggestion {
  blockType: BlockType;
  title: string;
  description: string;
  reasoning: string;
  confidence: number; // 0-100
  category: string;
  suggestedParameters?: Record<string, any>;
  priority: 'high' | 'medium' | 'low';
}

export interface SuggestionContext {
  selectedBlock?: AgentBlock;
  connectedBlocks: AgentBlock[];
  allBlocks: AgentBlock[];
  edges: Edge[];
  dragPosition?: { x: number; y: number };
}

// Block flow patterns for intelligent suggestions
const FLOW_PATTERNS = {
  // Data source patterns
  [BlockType.PRICE]: {
    common_next: [
      BlockType.CANDLE_PATTERN,
      BlockType.CHART_PATTERN,
      BlockType.SUPPORT_RESISTANCE,
      BlockType.PRICE_ACTION_SIGNAL,
      BlockType.CONDITION
    ],
    reasoning: 'Price data commonly flows to pattern recognition or condition checks'
  },
  
  [BlockType.INDICATOR]: {
    common_next: [
      BlockType.CONDITION,
      BlockType.DIVERGENCE_DETECTION,
      BlockType.MOMENTUM_SHIFT,
      BlockType.TREND_STRENGTH
    ],
    reasoning: 'Technical indicators typically connect to conditions or advanced analysis'
  },

  // Analysis patterns
  [BlockType.CANDLE_PATTERN]: {
    common_next: [
      BlockType.CONDITION,
      BlockType.SIGNAL_QUALITY_FILTER,
      BlockType.VOLUME_CONFIRMATION,
      BlockType.AND_OPERATOR
    ],
    reasoning: 'Candlestick patterns often need confirmation or filtering'
  },

  [BlockType.CONDITION]: {
    common_next: [
      BlockType.IF_THEN_ELSE,
      BlockType.AND_OPERATOR,
      BlockType.OR_OPERATOR,
      BlockType.SIGNAL_QUALITY_FILTER,
      BlockType.TRIGGER
    ],
    reasoning: 'Conditions typically flow to logic operators or direct signals'
  },

  // Logic patterns
  [BlockType.AND_OPERATOR]: {
    common_next: [
      BlockType.SIGNAL_QUALITY_FILTER,
      BlockType.CONFIDENCE_THRESHOLD,
      BlockType.RISK_ANALYZER,
      BlockType.TRIGGER
    ],
    reasoning: 'Logic operators often connect to quality filters or signal generation'
  },

  [BlockType.OR_OPERATOR]: {
    common_next: [
      BlockType.SIGNAL_QUALITY_FILTER,
      BlockType.CONFIDENCE_THRESHOLD,
      BlockType.TRIGGER
    ],
    reasoning: 'OR operators typically lead to signal filtering or generation'
  }
};

// Strategy-based suggestions
const STRATEGY_COMPLETIONS = {
  'momentum_strategy': {
    required_blocks: [
      BlockType.MOMENTUM_INDICATOR,
      BlockType.TREND_STRENGTH,
      BlockType.VOLUME_CONFIRMATION,
      BlockType.RISK_ANALYZER
    ],
    description: 'Complete momentum-based trading strategy'
  },
  
  'mean_reversion': {
    required_blocks: [
      BlockType.MOMENTUM_INDICATOR,
      BlockType.SUPPORT_RESISTANCE,
      BlockType.VOLATILITY_FILTER,
      BlockType.TARGET_ANALYZER
    ],
    description: 'Mean reversion strategy with support/resistance'
  },
  
  'breakout_strategy': {
    required_blocks: [
      BlockType.SUPPORT_RESISTANCE,
      BlockType.VOLUME_CONFIRMATION,
      BlockType.MOMENTUM_SHIFT,
      BlockType.RISK_ANALYZER
    ],
    description: 'Breakout strategy with volume confirmation'
  }
};

// Generate intelligent suggestions based on context
export function generateBlockSuggestions(context: SuggestionContext): BlockSuggestion[] {
  const suggestions: BlockSuggestion[] = [];

  // Get suggestions based on selected block
  if (context.selectedBlock) {
    suggestions.push(...getFlowBasedSuggestions(context.selectedBlock, context));
  }

  // Get suggestions based on missing components
  suggestions.push(...getMissingComponentSuggestions(context));

  // Get strategy completion suggestions
  suggestions.push(...getStrategyCompletionSuggestions(context));

  // Get quality improvement suggestions
  suggestions.push(...getQualityImprovementSuggestions(context));

  // Sort by confidence and priority
  return suggestions
    .sort((a, b) => {
      const priorityWeight = { high: 3, medium: 2, low: 1 };
      const priorityDiff = priorityWeight[b.priority] - priorityWeight[a.priority];
      if (priorityDiff !== 0) return priorityDiff;
      return b.confidence - a.confidence;
    })
    .slice(0, 8); // Limit to top 8 suggestions
}

function getFlowBasedSuggestions(selectedBlock: AgentBlock, context: SuggestionContext): BlockSuggestion[] {
  const suggestions: BlockSuggestion[] = [];
  const blockType = selectedBlock.type as BlockType;
  const pattern = FLOW_PATTERNS[blockType];

  if (!pattern) return suggestions;

  // Check which suggested blocks are not already connected
  const connectedTypes = context.connectedBlocks.map(block => block.type);
  const availableTypes = pattern.common_next.filter(type => !connectedTypes.includes(type));

  availableTypes.forEach(suggestedType => {
    const suggestion = createBlockSuggestion(
      suggestedType,
      `Connect to ${selectedBlock.type}`,
      pattern.reasoning,
      85,
      'high'
    );
    suggestions.push(suggestion);
  });

  return suggestions;
}

function getMissingComponentSuggestions(context: SuggestionContext): BlockSuggestion[] {
  const suggestions: BlockSuggestion[] = [];
  const existingTypes = context.allBlocks.map(block => block.type);

  // Check for missing essential components
  const essentialComponents = [
    {
      type: BlockType.WHEN_RUN,
      title: 'Entry Point',
      description: 'Every agent needs an entry point',
      priority: 'high' as const,
      confidence: 95
    },
    {
      type: BlockType.PRICE,
      title: 'Data Source',
      description: 'Add price data for market analysis',
      priority: 'high' as const,
      confidence: 90
    },
    {
      type: BlockType.TRIGGER,
      title: 'Signal Output',
      description: 'Generate trading signals',
      priority: 'high' as const,
      confidence: 95
    }
  ];

  essentialComponents.forEach(component => {
    if (!existingTypes.includes(component.type)) {
      suggestions.push(createBlockSuggestion(
        component.type,
        component.title,
        component.description,
        component.confidence,
        component.priority
      ));
    }
  });

  return suggestions;
}

function getStrategyCompletionSuggestions(context: SuggestionContext): BlockSuggestion[] {
  const suggestions: BlockSuggestion[] = [];
  const existingTypes = context.allBlocks.map(block => block.type);

  // Detect potential strategy patterns
  const detectedStrategy = detectStrategyPattern(existingTypes);
  
  if (detectedStrategy) {
    const completion = STRATEGY_COMPLETIONS[detectedStrategy];
    const missingBlocks = completion.required_blocks.filter(type => !existingTypes.includes(type));

    missingBlocks.forEach(blockType => {
      suggestions.push(createBlockSuggestion(
        blockType,
        `Complete ${detectedStrategy.replace('_', ' ')}`,
        completion.description,
        75,
        'medium'
      ));
    });
  }

  return suggestions;
}

function getQualityImprovementSuggestions(context: SuggestionContext): BlockSuggestion[] {
  const suggestions: BlockSuggestion[] = [];
  const existingTypes = context.allBlocks.map(block => block.type);

  // Suggest quality improvements
  const qualityImprovements = [
    {
      type: BlockType.SIGNAL_QUALITY_FILTER,
      condition: () => !existingTypes.includes(BlockType.SIGNAL_QUALITY_FILTER),
      title: 'Improve Signal Quality',
      description: 'Filter out low-quality signals',
      confidence: 70
    },
    {
      type: BlockType.RISK_ANALYZER,
      condition: () => !existingTypes.includes(BlockType.RISK_ANALYZER),
      title: 'Add Risk Management',
      description: 'Calculate optimal stop loss levels',
      confidence: 80
    },
    {
      type: BlockType.VOLUME_CONFIRMATION,
      condition: () => existingTypes.includes(BlockType.PRICE_ACTION_SIGNAL) && 
                      !existingTypes.includes(BlockType.VOLUME_CONFIRMATION),
      title: 'Volume Confirmation',
      description: 'Confirm signals with volume analysis',
      confidence: 75
    }
  ];

  qualityImprovements.forEach(improvement => {
    if (improvement.condition()) {
      suggestions.push(createBlockSuggestion(
        improvement.type,
        improvement.title,
        improvement.description,
        improvement.confidence,
        'medium'
      ));
    }
  });

  return suggestions;
}

function createBlockSuggestion(
  blockType: BlockType,
  title: string,
  description: string,
  confidence: number,
  priority: 'high' | 'medium' | 'low',
  suggestedParameters?: Record<string, any>
): BlockSuggestion {
  return {
    blockType,
    title,
    description,
    reasoning: description,
    confidence,
    category: getBlockCategory(blockType),
    suggestedParameters,
    priority
  };
}

function detectStrategyPattern(existingTypes: string[]): string | null {
  // Momentum strategy detection
  if (existingTypes.includes(BlockType.MOMENTUM_INDICATOR) && 
      existingTypes.includes(BlockType.TREND_INDICATOR)) {
    return 'momentum_strategy';
  }

  // Mean reversion detection
  if (existingTypes.includes(BlockType.MOMENTUM_INDICATOR) && 
      existingTypes.includes(BlockType.SUPPORT_RESISTANCE)) {
    return 'mean_reversion';
  }

  // Breakout strategy detection
  if (existingTypes.includes(BlockType.SUPPORT_RESISTANCE) && 
      existingTypes.includes(BlockType.VOLUME_CONFIRMATION)) {
    return 'breakout_strategy';
  }

  return null;
}

// Get compatible blocks for drag and drop
export function getCompatibleBlocks(
  sourceBlockType: BlockType,
  allBlockTypes: BlockType[]
): BlockType[] {
  const pattern = FLOW_PATTERNS[sourceBlockType];
  if (!pattern) return [];

  return pattern.common_next.filter(type => allBlockTypes.includes(type));
}

// Get suggested parameters for a block based on context
export function getSuggestedParameters(
  blockType: BlockType,
  context: SuggestionContext
): Record<string, any> {
  const suggestions: Record<string, any> = {};

  // Context-aware parameter suggestions
  switch (blockType) {
    case BlockType.CONDITION:
      if (context.connectedBlocks.some(b => b.type === BlockType.MOMENTUM_INDICATOR)) {
        suggestions.operator = 'greater_than';
        suggestions.value = 70; // RSI overbought
      }
      break;

    case BlockType.RISK_ANALYZER:
      suggestions.method = 'atr_based';
      suggestions.atr_multiplier = 2;
      suggestions.risk_tolerance = 'moderate';
      break;

    case BlockType.SIGNAL_QUALITY_FILTER:
      suggestions.min_confidence = 70;
      suggestions.require_volume_confirmation = true;
      break;
  }

  return suggestions;
}

/**
 * Utility functions for converting drawing coordinates between absolute and relative positions
 * to maintain drawing positions when timeframes change
 */

export interface ChartBounds {
  timeMin: number;
  timeMax: number;
  priceMin: number;
  priceMax: number;
}

export interface AbsolutePoint {
  time: string;
  price: number;
}

export interface RelativePoint {
  timePercent: number; // 0-1 representing position along time axis
  pricePercent: number; // 0-1 representing position along price axis
}

export interface Drawing {
  id: number;
  type: string;
  points?: AbsolutePoint[];
  relativePoints?: RelativePoint[];
  center?: AbsolutePoint;
  relativeCenter?: RelativePoint;
  radiusPoint?: AbsolutePoint;
  relativeRadiusPoint?: RelativePoint;
  entryPrice?: number;
  targetPrice?: number;
  stopPrice?: number;
}

/**
 * Convert absolute time/price coordinates to relative percentages
 */
export function convertToRelativeCoordinates(
  point: AbsolutePoint,
  bounds: ChartBounds
): RelativePoint {
  const timeMs = new Date(point.time).getTime();
  const timePercent = (timeMs - bounds.timeMin) / (bounds.timeMax - bounds.timeMin);
  const pricePercent = (point.price - bounds.priceMin) / (bounds.priceMax - bounds.priceMin);
  
  return {
    timePercent: Math.max(0, Math.min(1, timePercent)),
    pricePercent: Math.max(0, Math.min(1, pricePercent))
  };
}

/**
 * Convert relative percentages back to absolute time/price coordinates
 */
export function convertToAbsoluteCoordinates(
  relativePoint: RelativePoint,
  bounds: ChartBounds
): AbsolutePoint {
  const timeMs = bounds.timeMin + (relativePoint.timePercent * (bounds.timeMax - bounds.timeMin));
  const price = bounds.priceMin + (relativePoint.pricePercent * (bounds.priceMax - bounds.priceMin));
  
  return {
    time: new Date(timeMs).toISOString(),
    price: price
  };
}

/**
 * Convert a drawing to use relative coordinates for persistence across timeframes
 */
export function convertDrawingToRelative(drawing: Drawing, bounds: ChartBounds): Drawing {
  const relativeDrawing: Drawing = {
    ...drawing,
    relativePoints: drawing.points?.map(point => 
      convertToRelativeCoordinates(point, bounds)
    )
  };

  // Handle circle center and radius point
  if (drawing.center) {
    relativeDrawing.relativeCenter = convertToRelativeCoordinates(drawing.center, bounds);
  }
  
  if (drawing.radiusPoint) {
    relativeDrawing.relativeRadiusPoint = convertToRelativeCoordinates(drawing.radiusPoint, bounds);
  }

  return relativeDrawing;
}

/**
 * Convert a drawing from relative coordinates back to absolute coordinates
 */
export function convertDrawingToAbsolute(drawing: Drawing, bounds: ChartBounds): Drawing {
  const absoluteDrawing: Drawing = {
    ...drawing,
    points: drawing.relativePoints?.map(relativePoint => 
      convertToAbsoluteCoordinates(relativePoint, bounds)
    )
  };

  // Handle circle center and radius point
  if (drawing.relativeCenter) {
    absoluteDrawing.center = convertToAbsoluteCoordinates(drawing.relativeCenter, bounds);
  }
  
  if (drawing.relativeRadiusPoint) {
    absoluteDrawing.radiusPoint = convertToAbsoluteCoordinates(drawing.relativeRadiusPoint, bounds);
  }

  return absoluteDrawing;
}

/**
 * Calculate chart bounds from stock data
 */
export function calculateChartBounds(stockData: any[]): ChartBounds {
  if (!stockData || stockData.length === 0) {
    return {
      timeMin: Date.now() - 24 * 60 * 60 * 1000,
      timeMax: Date.now(),
      priceMin: 0,
      priceMax: 100
    };
  }

  const times = stockData.map(d => {
    if (typeof d.time === 'number') {
      return d.time * 1000; // Convert seconds to milliseconds
    }
    return new Date(d.time).getTime();
  });
  
  const prices = stockData.flatMap(d => [d.low, d.high, d.open, d.close]);
  
  return {
    timeMin: Math.min(...times),
    timeMax: Math.max(...times),
    priceMin: Math.min(...prices),
    priceMax: Math.max(...prices)
  };
}

/**
 * Update all drawings to use new chart bounds when timeframe changes
 */
export function updateDrawingsForNewTimeframe(
  drawings: Drawing[],
  newBounds: ChartBounds
): Drawing[] {
  return drawings.map(drawing => {
    // If drawing has relative coordinates, convert back to absolute with new bounds
    if (drawing.relativePoints || drawing.relativeCenter) {
      return convertDrawingToAbsolute(drawing, newBounds);
    }
    
    // If drawing only has absolute coordinates, it stays as is
    // (this handles legacy drawings or drawings that couldn't be converted)
    return drawing;
  });
}

/**
 * Store drawings with both absolute and relative coordinates for maximum compatibility
 */
export function storeDrawingWithBothCoordinates(
  drawing: Drawing,
  bounds: ChartBounds
): Drawing {
  const relativeDrawing = convertDrawingToRelative(drawing, bounds);
  
  return {
    ...drawing,
    relativePoints: relativeDrawing.relativePoints,
    relativeCenter: relativeDrawing.relativeCenter,
    relativeRadiusPoint: relativeDrawing.relativeRadiusPoint
  };
}

// Comprehensive Agent Validation System
import { AgentBlock } from '@/services/agentService';
import { BlockType } from '@/hooks/useAgentBuilder';
import { Edge } from 'reactflow';

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  suggestions: ValidationSuggestion[];
}

export interface ValidationError {
  id: string;
  type: 'error';
  blockId?: string;
  message: string;
  severity: 'critical' | 'high' | 'medium';
}

export interface ValidationWarning {
  id: string;
  type: 'warning';
  blockId?: string;
  message: string;
  severity: 'medium' | 'low';
}

export interface ValidationSuggestion {
  id: string;
  type: 'suggestion';
  blockId?: string;
  message: string;
  suggestedBlockType?: BlockType;
  suggestedAction?: string;
}

// Required block types for a complete agent
const REQUIRED_BLOCK_TYPES = {
  ENTRY: [BlockType.WHEN_RUN],
  DATA_SOURCE: [
    BlockType.PRICE, 
    BlockType.FUNDAMENTAL, 
    BlockType.INDICATOR,
    BlockType.MOVING_AVERAGE,
    BlockType.MOMENTUM_INDICATOR,
    BlockType.TREND_INDICATOR,
    BlockType.VOLUME_INDICATOR,
    BlockType.VOLATILITY_INDICATOR
  ],
  OUTPUT: [
    BlockType.TRIGGER,
    BlockType.SIGNAL,
    BlockType.CONFIDENCE_BOOST
  ]
};

// Block compatibility matrix
const BLOCK_COMPATIBILITY = {
  [BlockType.WHEN_RUN]: {
    canConnectTo: [
      BlockType.PRICE,
      BlockType.FUNDAMENTAL,
      BlockType.INDICATOR,
      BlockType.MOVING_AVERAGE,
      BlockType.MOMENTUM_INDICATOR,
      BlockType.TREND_INDICATOR,
      BlockType.VOLUME_INDICATOR,
      BlockType.VOLATILITY_INDICATOR
    ],
    requiredOutputs: 1
  },
  [BlockType.PRICE]: {
    canConnectTo: [
      BlockType.CONDITION,
      BlockType.CANDLE_PATTERN,
      BlockType.CHART_PATTERN,
      BlockType.TRIGGER,
      BlockType.SIGNAL,
      BlockType.BULLISH_CONFIDENCE_BOOST,
      BlockType.BEARISH_CONFIDENCE_BOOST
    ],
    requiredInputs: 1
  },

  [BlockType.MOMENTUM_INDICATOR]: {
    canConnectTo: [
      BlockType.CONDITION,
      BlockType.CANDLE_PATTERN,
      BlockType.CHART_PATTERN,
      BlockType.TRIGGER,
      BlockType.SIGNAL,
      BlockType.BULLISH_CONFIDENCE_BOOST,
      BlockType.BEARISH_CONFIDENCE_BOOST
    ],
    requiredInputs: 1
  },
  [BlockType.MOVING_AVERAGE]: {
    canConnectTo: [
      BlockType.CONDITION,
      BlockType.CANDLE_PATTERN,
      BlockType.CHART_PATTERN,
      BlockType.TRIGGER,
      BlockType.SIGNAL,
      BlockType.BULLISH_CONFIDENCE_BOOST,
      BlockType.BEARISH_CONFIDENCE_BOOST
    ],
    requiredInputs: 1
  },
  [BlockType.TREND_INDICATOR]: {
    canConnectTo: [
      BlockType.CONDITION,
      BlockType.CANDLE_PATTERN,
      BlockType.CHART_PATTERN,
      BlockType.TRIGGER,
      BlockType.SIGNAL,
      BlockType.BULLISH_CONFIDENCE_BOOST,
      BlockType.BEARISH_CONFIDENCE_BOOST
    ],
    requiredInputs: 1
  },
  [BlockType.VOLUME_INDICATOR]: {
    canConnectTo: [
      BlockType.CONDITION,
      BlockType.CANDLE_PATTERN,
      BlockType.CHART_PATTERN,
      BlockType.TRIGGER,
      BlockType.SIGNAL,
      BlockType.BULLISH_CONFIDENCE_BOOST,
      BlockType.BEARISH_CONFIDENCE_BOOST
    ],
    requiredInputs: 1
  },
  [BlockType.VOLATILITY_INDICATOR]: {
    canConnectTo: [
      BlockType.CONDITION,
      BlockType.CANDLE_PATTERN,
      BlockType.CHART_PATTERN,
      BlockType.TRIGGER,
      BlockType.SIGNAL,
      BlockType.BULLISH_CONFIDENCE_BOOST,
      BlockType.BEARISH_CONFIDENCE_BOOST
    ],
    requiredInputs: 1
  },
  [BlockType.CONSOLE_LOG]: {
    canConnectTo: [
      BlockType.CONDITION,
      BlockType.CANDLE_PATTERN,
      BlockType.CHART_PATTERN,
      BlockType.TRIGGER,
      BlockType.SIGNAL,
      BlockType.BULLISH_CONFIDENCE_BOOST,
      BlockType.BEARISH_CONFIDENCE_BOOST,
      BlockType.CONSOLE_LOG
    ],
    requiredInputs: 1
  },
  [BlockType.CONDITION]: {
    canConnectTo: [
      BlockType.IF_THEN_ELSE,
      BlockType.AND_OPERATOR,
      BlockType.OR_OPERATOR,
      BlockType.TRIGGER,
      BlockType.SIGNAL,
      BlockType.BULLISH_CONFIDENCE_BOOST,
      BlockType.BEARISH_CONFIDENCE_BOOST
    ],
    requiredInputs: 1
  },
  [BlockType.TRIGGER]: {
    canConnectTo: [],
    requiredInputs: 1,
    isTerminal: true
  }
};

// Validate complete agent structure
export function validateAgent(blocks: AgentBlock[], edges: Edge[]): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];
  const suggestions: ValidationSuggestion[] = [];

  // Check for required blocks
  validateRequiredBlocks(blocks, errors, suggestions);
  
  // Check block connections
  validateBlockConnections(blocks, edges, errors, warnings);
  
  // Check for logical flow
  validateLogicalFlow(blocks, edges, errors, warnings, suggestions);
  
  // Check block parameters
  validateBlockParameters(blocks, errors, warnings);
  
  // Check for best practices
  validateBestPractices(blocks, edges, warnings, suggestions);

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    suggestions
  };
}

function validateRequiredBlocks(
  blocks: AgentBlock[], 
  errors: ValidationError[], 
  suggestions: ValidationSuggestion[]
) {
  // Check for entry block
  const hasEntryBlock = blocks.some(block => 
    REQUIRED_BLOCK_TYPES.ENTRY.includes(block.type as BlockType)
  );
  
  if (!hasEntryBlock) {
    errors.push({
      id: 'missing-entry',
      type: 'error',
      message: 'Agent must have an entry block (WHEN_RUN)',
      severity: 'critical'
    });
    suggestions.push({
      id: 'add-entry',
      type: 'suggestion',
      message: 'Add a WHEN_RUN block to start your agent',
      suggestedBlockType: BlockType.WHEN_RUN,
      suggestedAction: 'Add entry block'
    });
  }

  // Check for data source
  const hasDataSource = blocks.some(block => 
    REQUIRED_BLOCK_TYPES.DATA_SOURCE.includes(block.type as BlockType)
  );
  
  if (!hasDataSource) {
    errors.push({
      id: 'missing-data-source',
      type: 'error',
      message: 'Agent must have at least one data source block',
      severity: 'high'
    });
    suggestions.push({
      id: 'add-data-source',
      type: 'suggestion',
      message: 'Add a data source like Price Data or Technical Indicator',
      suggestedBlockType: BlockType.PRICE,
      suggestedAction: 'Add data source'
    });
  }

  // Check for output block
  const hasOutputBlock = blocks.some(block => 
    REQUIRED_BLOCK_TYPES.OUTPUT.includes(block.type as BlockType)
  );
  
  if (!hasOutputBlock) {
    errors.push({
      id: 'missing-output',
      type: 'error',
      message: 'Agent must have an output block (TRIGGER or SIGNAL)',
      severity: 'critical'
    });
    suggestions.push({
      id: 'add-output',
      type: 'suggestion',
      message: 'Add a TRIGGER block to generate signals',
      suggestedBlockType: BlockType.TRIGGER,
      suggestedAction: 'Add output block'
    });
  }
}

function validateBlockConnections(
  blocks: AgentBlock[], 
  edges: Edge[], 
  errors: ValidationError[], 
  warnings: ValidationWarning[]
) {
  // Check for disconnected blocks
  const connectedBlockIds = new Set();
  edges.forEach(edge => {
    connectedBlockIds.add(edge.source);
    connectedBlockIds.add(edge.target);
  });

  blocks.forEach(block => {
    if (!connectedBlockIds.has(block.id) && blocks.length > 1) {
      warnings.push({
        id: `disconnected-${block.id}`,
        type: 'warning',
        blockId: block.id,
        message: `Block "${block.type}" is not connected to any other blocks`,
        severity: 'medium'
      });
    }
  });

  // Check for invalid connections
  edges.forEach(edge => {
    const sourceBlock = blocks.find(b => b.id === edge.source);
    const targetBlock = blocks.find(b => b.id === edge.target);
    
    if (!sourceBlock || !targetBlock) return;

    const compatibility = BLOCK_COMPATIBILITY[sourceBlock.type as BlockType];
    if (compatibility && compatibility.canConnectTo) {
      const canConnect = compatibility.canConnectTo.includes(targetBlock.type as BlockType);
      if (!canConnect) {
        errors.push({
          id: `invalid-connection-${edge.id}`,
          type: 'error',
          message: `Invalid connection: ${sourceBlock.type} cannot connect to ${targetBlock.type}`,
          severity: 'medium'
        });
      }
    }
  });
}

function validateLogicalFlow(
  blocks: AgentBlock[], 
  edges: Edge[], 
  errors: ValidationError[], 
  warnings: ValidationWarning[],
  suggestions: ValidationSuggestion[]
) {
  // Check for circular dependencies
  const visited = new Set<string>();
  const recursionStack = new Set<string>();
  
  function hasCycle(blockId: string): boolean {
    if (recursionStack.has(blockId)) return true;
    if (visited.has(blockId)) return false;
    
    visited.add(blockId);
    recursionStack.add(blockId);
    
    const outgoingEdges = edges.filter(edge => edge.source === blockId);
    for (const edge of outgoingEdges) {
      if (hasCycle(edge.target)) return true;
    }
    
    recursionStack.delete(blockId);
    return false;
  }

  blocks.forEach(block => {
    if (hasCycle(block.id)) {
      errors.push({
        id: `circular-dependency-${block.id}`,
        type: 'error',
        blockId: block.id,
        message: 'Circular dependency detected in agent flow',
        severity: 'high'
      });
    }
  });

  // Check for proper signal flow
  const entryBlocks = blocks.filter(block => 
    REQUIRED_BLOCK_TYPES.ENTRY.includes(block.type as BlockType)
  );
  
  const outputBlocks = blocks.filter(block => 
    REQUIRED_BLOCK_TYPES.OUTPUT.includes(block.type as BlockType)
  );

  if (entryBlocks.length > 0 && outputBlocks.length > 0) {
    // Check if there's a path from entry to output
    const hasPath = hasPathBetweenBlocks(entryBlocks[0].id, outputBlocks[0].id, edges);
    if (!hasPath) {
      errors.push({
        id: 'no-path-entry-output',
        type: 'error',
        message: 'No valid path from entry block to output block',
        severity: 'high'
      });
    }
  }
}

function validateBlockParameters(
  blocks: AgentBlock[], 
  errors: ValidationError[], 
  warnings: ValidationWarning[]
) {
  blocks.forEach(block => {
    // Check for missing required parameters
    const requiredParams = getRequiredParameters(block.type as BlockType);
    requiredParams.forEach(param => {
      if (!block.parameters || block.parameters[param] === undefined) {
        errors.push({
          id: `missing-param-${block.id}-${param}`,
          type: 'error',
          blockId: block.id,
          message: `Missing required parameter: ${param}`,
          severity: 'medium'
        });
      }
    });

    // Check for invalid parameter values
    if (block.parameters) {
      Object.entries(block.parameters).forEach(([key, value]) => {
        if (typeof value === 'number' && (isNaN(value) || !isFinite(value))) {
          warnings.push({
            id: `invalid-param-${block.id}-${key}`,
            type: 'warning',
            blockId: block.id,
            message: `Invalid parameter value for ${key}: ${value}`,
            severity: 'medium'
          });
        }
      });
    }
  });
}

function validateBestPractices(
  blocks: AgentBlock[], 
  edges: Edge[], 
  warnings: ValidationWarning[],
  suggestions: ValidationSuggestion[]
) {
  // Suggest condition blocks for better logic flow
  const hasConditions = blocks.some(block =>
    block.type === BlockType.CONDITION
  );

  if (!hasConditions && blocks.length > 3) {
    suggestions.push({
      id: 'add-conditions',
      type: 'suggestion',
      message: 'Consider adding condition blocks for better signal logic',
      suggestedBlockType: BlockType.CONDITION,
      suggestedAction: 'Add condition logic'
    });
  }

  // Suggest multiple data sources
  const dataSourceCount = blocks.filter(block =>
    [BlockType.PRICE, BlockType.FUNDAMENTAL, BlockType.INDICATOR,
     BlockType.MOMENTUM_INDICATOR, BlockType.MOVING_AVERAGE, BlockType.TREND_INDICATOR].includes(block.type as BlockType)
  ).length;

  if (dataSourceCount === 1 && blocks.length > 2) {
    suggestions.push({
      id: 'add-data-sources',
      type: 'suggestion',
      message: 'Consider adding multiple data sources for more robust signals',
      suggestedBlockType: BlockType.INDICATOR,
      suggestedAction: 'Add more indicators'
    });
  }

  // Check for too many blocks without organization
  if (blocks.length > 10) {
    suggestions.push({
      id: 'organize-blocks',
      type: 'suggestion',
      message: 'Consider organizing your agent with logical groupings',
      suggestedAction: 'Use auto-arrange feature'
    });
  }
}

// Helper functions
function hasPathBetweenBlocks(sourceId: string, targetId: string, edges: Edge[]): boolean {
  if (sourceId === targetId) return true;
  
  const visited = new Set<string>();
  const queue = [sourceId];
  
  while (queue.length > 0) {
    const currentId = queue.shift()!;
    if (visited.has(currentId)) continue;
    visited.add(currentId);
    
    if (currentId === targetId) return true;
    
    const outgoingEdges = edges.filter(edge => edge.source === currentId);
    outgoingEdges.forEach(edge => {
      if (!visited.has(edge.target)) {
        queue.push(edge.target);
      }
    });
  }
  
  return false;
}

function getRequiredParameters(blockType: BlockType): string[] {
  const requiredParams: { [key in BlockType]?: string[] } = {
    [BlockType.INDICATOR]: ['indicator', 'period'],
    [BlockType.CONDITION]: ['operator', 'value'],
    [BlockType.MOVING_AVERAGE]: ['averageType', 'period'],
    [BlockType.MOMENTUM_INDICATOR]: ['indicator', 'period'],
    [BlockType.TREND_INDICATOR]: ['indicator'],
    [BlockType.TRIGGER]: ['signal']
  };
  
  return requiredParams[blockType] || [];
}

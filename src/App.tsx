import React, { useState, useEffect } from "react";
import { setRefreshTokenFunction } from "./utils/tokenUtils";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation } from "react-router-dom";

import AppLayout from "./components/layout/AppLayout";
import Home from "./pages/Home";
import { supabase } from "./integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import Settings from './pages/Settings';
import About from './pages/About';
import UnifiedSettings from './pages/UnifiedSettings';
import ManageSubscription from './pages/ManageSubscription';
import Subscription from './pages/Subscription';
import ModelSettings from './pages/ModelSettings';
import Trades from './pages/Trades';
import PortfolioManager from './pages/PortfolioManager';
import PortfolioNews from './pages/PortfolioNews';
import ApiTest from './pages/ApiTest';
import AgentBuilder from './pages/AgentBuilder';
import AgentManagement from './pages/AgentManagement';
import AgentScanner from './pages/AgentScanner';
import AgentBacktesting from './pages/AgentBacktesting';
import BacktestResults from './pages/BacktestResults';
import AgentBuilderDocs from './pages/AgentBuilderDocs';
import Discover from './pages/Discover';
import StockSearch from './pages/StockSearch';
import StockDetail from './pages/StockDetail';
import StripeTest from './pages/StripeTest';
import Admin from './pages/Admin';
import AdminTest from './pages/AdminTest';
import AuthModal from './components/auth/AuthModal';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { WhopProvider, useWhop } from './contexts/WhopContext';
import { GamificationProvider } from './contexts/GamificationContext';
import GamificationWrapper from './components/gamification/GamificationWrapper';
import WhopCallback from './pages/WhopCallback';
import TermsOfService from './pages/TermsOfService';
import PrivacyPolicy from './pages/PrivacyPolicy';
import { cn } from "@/lib/utils";
import Onboarding from './components/Onboarding';
// Login component removed - using AuthModal instead
import { AnimatePresence } from "framer-motion";
import LandingPage from './pages/LandingPage';
import { getSubscriptionCookie, setSubscriptionCookie } from './utils/cookieUtils';
import { setSelectedPlanType, PLAN_TYPES } from './utils/planUtils';
import VideoPopup from './components/VideoPopup';
import { useVideoPopup } from './hooks/useVideoPopup';
import { getAffiliateCode } from './utils/growiUtils';
import { initializeGrowiTracking } from './utils/growiLoader';
import { testEnvironmentVariables } from './utils/envTest';
import MobileAwareRoute from './components/mobile/MobileAwareRoute';

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      staleTime: 5 * 60 * 1000,
      refetchOnWindowFocus: true,
      refetchOnReconnect: true,
    },
  },
});

// Protected route component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated } = useAuth();
  const { isWhopUser } = useWhop();
  const location = useLocation();
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);

  useEffect(() => {
    // Give a short delay to ensure auth state is loaded
    const timer = setTimeout(() => {
      setIsCheckingAuth(false);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // Show loading state while checking authentication
  if (isCheckingAuth) {
    console.log('🔐 ProtectedRoute: Checking authentication...', {
      path: location.pathname,
      isAuthenticated,
      isWhopUser
    });
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white/30"></div>
      </div>
    );
  }

  // Redirect if not authenticated after checking
  if (!isAuthenticated && !isWhopUser) {
    console.log('🚫 ProtectedRoute: Not authenticated, redirecting to home', {
      path: location.pathname,
      isAuthenticated,
      isWhopUser
    });
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  console.log('✅ ProtectedRoute: Authentication passed', {
    path: location.pathname,
    isAuthenticated,
    isWhopUser
  });

  return <>{children}</>;
};

// Create a wrapper component to handle the auth modal logic
const AppContent = () => {
  const { isAuthenticated, user, refreshToken } = useAuth();
  const { isWhopUser } = useWhop();
  const [isAuthenticating, setIsAuthenticating] = useState(false);
  const location = useLocation();
  const { showVideoPopup, closeVideoPopup } = useVideoPopup();

  // Set the refreshToken function from AuthContext (only once)
  useEffect(() => {
    if (refreshToken) {
      console.log('[App] Setting refreshToken function');
      setRefreshTokenFunction(refreshToken);
    }
  }, []); // Remove refreshToken dependency to prevent excessive calls

  // Initialize Growi tracking on app load
  useEffect(() => {
    // Test environment variables first
    const envTest = testEnvironmentVariables();
    console.log('🧪 Environment test result:', envTest);

    initializeGrowiTracking();
  }, []);

  // Define paths where auth modal should not appear
  const noAuthModalPaths = ['/terms', '/privacy', '/subscription', '/subscription/manage'];

  // Check authentication status
  const isUserAuthenticated = isAuthenticated || isWhopUser;

  // Start with landing page shown and onboarding hidden
  const [hasSeenOnboarding, setHasSeenOnboarding] = useState(false);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [isCheckingOnboarding, setIsCheckingOnboarding] = useState(false);

  // Function to handle direct checkout
  const handleDirectCheckout = async (premiumNoTrial: boolean, annualPlan: boolean, user: any) => {
    try {
      // Get user creation date for price ID selection
      const { data: profile } = await supabase
        .from('profiles')
        .select('created_at')
        .eq('id', user.id)
        .single();

      const userCreatedAt = profile?.created_at || user.created_at;

      // Determine billing period and price ID
      const billingPeriod = annualPlan ? 'yearly' : 'weekly';

      // Import the function dynamically to avoid circular imports
      const { getPriceIdForPlanType } = await import('./utils/planUtils');
      const priceId = getPriceIdForPlanType('premium', billingPeriod, userCreatedAt);

      console.log('Direct checkout params:', {
        premiumNoTrial,
        annualPlan,
        billingPeriod,
        priceId,
        userCreatedAt
      });

      // Get affiliate code for Growi tracking
      const affiliateCode = getAffiliateCode();

      // Create checkout session
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-checkout`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
        },
        body: JSON.stringify({
          action: 'create-checkout-session',
          priceId,
          returnUrl: window.location.origin + '/subscription/manage?success=true',
          skipTrial: premiumNoTrial,
          affiliateCode
        })
      });

      if (!response.ok) {
        throw new Error(`Server returned ${response.status}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      // Redirect to Stripe checkout
      if (data.url) {
        window.location.href = data.url;
      } else {
        throw new Error('No checkout URL returned');
      }

    } catch (err) {
      console.error('Direct checkout error:', err);
      // Fall back to showing onboarding
      setShowOnboarding(true);
    }
  };

  // Function to check onboarding status
  const checkOnboardingStatus = async () => {
    // Check for URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const subscriptionSuccess = urlParams.get('subscription_success') === 'true';
    const subscriptionPending = urlParams.get('subscription_pending') === 'true';
    const sessionId = urlParams.get('session_id');
    const success = urlParams.get('success') === 'true';
    const customerId = urlParams.get('customer_id');
    const canceled = urlParams.get('canceled') === 'true';

    // Check for plan selection parameters
    const premiumPlan = urlParams.get('premium') !== null;
    const premiumNoTrial = urlParams.get('premiumnotrial') !== null;
    const annualPlan = urlParams.get('annual') !== null;

    // If user is authenticated and we have direct checkout parameters, trigger checkout
    if (isAuthenticated && user && (premiumNoTrial || annualPlan)) {
      // Trigger direct checkout
      handleDirectCheckout(premiumNoTrial, annualPlan, user);
      return;
    }

    // Store the selected plan type and billing period
    setSelectedPlanType(PLAN_TYPES.PREMIUM);

    // Store billing period preference
    if (annualPlan) {
      localStorage.setItem('selected_billing_period', 'yearly');
    } else {
      localStorage.setItem('selected_billing_period', 'weekly');
    }

    // Clean up URL but preserve other parameters
    if (premiumPlan || premiumNoTrial || annualPlan) {
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('premium');
      newUrl.searchParams.delete('premiumnotrial');
      newUrl.searchParams.delete('annual');
      window.history.replaceState({}, document.title, newUrl.toString());
    }

    // Handle Stripe checkout cancellation explicitly
    if (canceled && isAuthenticated && user) {
      try {
        // Clean up URL parameters
        window.history.replaceState({}, document.title, window.location.pathname);

        // Force show onboarding for users who canceled checkout
        setHasSeenOnboarding(false);
        setShowOnboarding(true);

        // Clear any subscription cookies to ensure fresh state
        setSubscriptionCookie(false);

        console.log('User canceled Stripe checkout, showing onboarding');
        return; // Exit early to prevent other logic from running
      } catch (error) {
        console.error('Error handling Stripe cancellation:', error);
      }
    }

    // If we have a session_id and success=true, this is a Stripe redirect
    if (sessionId && success && isAuthenticated && user) {
      try {
        // Handle the checkout redirect
        const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${(await supabase.auth.getSession()).data.session?.access_token}`
          },
          body: JSON.stringify({
            action: 'handle-checkout-redirect',
            sessionId,
            customerId
          })
        });

        const { session, error } = await response.json();

        if (!error && session) {
          // Update the database
          await supabase
            .from('profiles')
            .update({
              has_seen_onboarding: true,
              updated_at: new Date().toISOString()
            })
            .eq('id', user.id);

          // Immediately close the onboarding popup
          setHasSeenOnboarding(true);
          setShowOnboarding(false);

          // Clean up URL
          window.history.replaceState({}, document.title, window.location.pathname);

          // If session was processed successfully, no need to continue with the rest of the checks
          return;
        }
      } catch (error) {
        console.error('Error handling Stripe redirect:', error);
      }
    }

    // If we detect successful subscription, mark onboarding as seen and close it immediately
    if (subscriptionSuccess && isAuthenticated && user) {
      try {
        // Update the database
        await supabase
          .from('profiles')
          .update({
            has_seen_onboarding: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', user.id);

        // Immediately close the onboarding popup
        setHasSeenOnboarding(true);
        setShowOnboarding(false);

        // Clean up URL
        window.history.replaceState({}, document.title, window.location.pathname);

        // If subscription was successful, no need to continue with the rest of the checks
        return;
      } catch (error) {
        console.error('Error updating subscription success:', error);
      }
    }

    // If we detect pending subscription after OAuth login, show the paywall
    if (subscriptionPending && isAuthenticated && user) {
      try {
        // Clean up URL but keep the onboarding open
        window.history.replaceState({}, document.title, window.location.pathname);

        // Force show the onboarding with paywall
        setHasSeenOnboarding(false);
        setShowOnboarding(true);
        return;
      } catch (error) {
        console.error('Error handling subscription pending:', error);
      }
    }

    // Only check onboarding status if user is authenticated
    if (isAuthenticated && user) {
      try {
        setIsCheckingOnboarding(true);

        // First check if the user profile exists
        const { data: profileExists, error: profileCheckError } = await supabase
          .from('profiles')
          .select('id')
          .eq('id', user.id)
          .maybeSingle();

        // If profile doesn't exist, create it
        if (!profileExists) {
          const { error: createError } = await supabase
            .from('profiles')
            .insert({
              id: user.id,
              subscription_type: null,
              has_seen_onboarding: false,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString()
            });

          if (createError) {
            console.error('Error creating profile:', createError);
          }
        }

        // Now get profile data
        const { data, error } = await supabase
          .from('profiles')
          .select('subscription_type, has_seen_onboarding')
          .eq('id', user.id)
          .single();

        if (error) {
          console.error('Error fetching profile:', error);
          // Default to showing onboarding if there's an error
          setHasSeenOnboarding(false);
          setShowOnboarding(true);
          return;
        }

        // Get subscription status from the subscriptions table
        const { data: subscriptionData } = await supabase
          .from('subscriptions')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(1)
          .maybeSingle();

        // First check if the user has an active subscription from subscriptions table
        const hasActiveSubscription =
          subscriptionData &&
          subscriptionData.status &&
          ['active'].includes(subscriptionData.status);

        // Also check subscription_mappings table using user's email
        let hasSubscriptionMapping = false;
        try {
          const userEmail = user.email;
          if (userEmail) {
            const { data: mappingData, error: mappingError } = await supabase
              .from('subscription_mappings')
              .select('*')
              .eq('email', userEmail)
              .maybeSingle();

            if (mappingError) {
              console.error('Error checking subscription_mappings:', mappingError);
            } else {
              // If there's any entry for this email in subscription_mappings, consider them paid
              hasSubscriptionMapping = !!mappingData;
            }
          }
        } catch (mappingError) {
          console.error('Error checking subscription_mappings:', mappingError);
        }

        // User has a subscription if they have an active subscription OR a subscription mapping
        const userHasSubscription = hasActiveSubscription || hasSubscriptionMapping;

        // Update subscription cookie based on database check
        if (userHasSubscription) {
          setSubscriptionCookie(true);
        } else {
          // Clear any existing subscription cookie if user doesn't have a subscription
          setSubscriptionCookie(false);
        }

        // IMPORTANT: We're changing the logic here to ALWAYS show onboarding for users without a subscription
        // regardless of whether they've seen it before
        const hasSeenOnboarding = data?.has_seen_onboarding === true;

        // ONLY consider a user as having seen onboarding if they have an active subscription
        // This ensures users without a subscription always see the onboarding/payment flow
        setHasSeenOnboarding(userHasSubscription);
        setShowOnboarding(!userHasSubscription);
      } catch (error) {
        console.error('Error checking onboarding status:', error);
        // Default to showing onboarding if there's an error
        setHasSeenOnboarding(false);
        setShowOnboarding(true);
      } finally {
        setIsCheckingOnboarding(false);
      }
    }
  };

  useEffect(() => {
    checkOnboardingStatus();
  }, [isAuthenticated, user]);

  // Expose the checkOnboardingStatus function to window for other components to call
  useEffect(() => {
    // @ts-ignore - Adding to window object
    window.refreshOnboardingStatus = checkOnboardingStatus;

    return () => {
      // @ts-ignore - Removing from window object
      delete window.refreshOnboardingStatus;
    };
  }, [isAuthenticated, user]);

  useEffect(() => {
    // Check for auth tokens in the URL (from OAuth redirects)
    const checkForAuthTokens = async () => {
      try {
        // Check if we have an access token in the URL hash (from OAuth)
        const hashParams = new URLSearchParams(window.location.hash.substring(1));
        const accessToken = hashParams.get('access_token');

        if (accessToken) {
          // This will be handled by AuthContext now
        }
      } catch (error) {
      } finally {
        setIsAuthenticating(false);
      }
    };

    checkForAuthTokens();
  }, [location.pathname]);

  // Add visibility change handler
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // Force refresh QueryClient
        queryClient.invalidateQueries();

        // Reconnect Supabase
        supabase.auth.startAutoRefresh();

        // Refresh onboarding status when tab becomes visible
        checkOnboardingStatus();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  const handleCloseOnboarding = async () => {
    // Immediately update UI state
    setShowOnboarding(false);
    setHasSeenOnboarding(true);

    // If user is authenticated, update their profile
    if (isAuthenticated && user) {
      try {
        const { error } = await supabase
          .from('profiles')
          .update({
            has_seen_onboarding: true,
            updated_at: new Date().toISOString()
          })
          .eq('id', user.id);

        if (error) {
          console.error('Error updating onboarding status:', error);
        }
      } catch (error) {
        console.error('Error updating onboarding status:', error);
      }
    }
  };

  // Show landing page first if not authenticated and not showing onboarding
  if (location.pathname === '/' && !isUserAuthenticated && !showOnboarding) {
    return <LandingPage setShowOnboarding={setShowOnboarding} />;
  }

  return (
    <div className={cn(
      "bg-[#0A0A0A]",
      // Only fix position when not on terms/privacy pages
      !noAuthModalPaths.includes(location.pathname) ? "fixed inset-0 overflow-hidden" : "min-h-screen"
    )}>
      {/* Only show AppLayout and Routes if not showing onboarding AND user has subscription */}
      {!showOnboarding && (
        <GamificationProvider>
          <GamificationWrapper>
            <Routes>
              {/* Full-screen routes without sidebar - these must come first */}
              <Route path="/backtest-results" element={<ProtectedRoute><BacktestResults /></ProtectedRoute>} />

              {/* Routes with AppLayout and sidebar */}
              <Route path="/home" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><Home /></ProtectedRoute></AppLayout>} />
              <Route path="/settings" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><UnifiedSettings /></ProtectedRoute></AppLayout>} />
              <Route path="/model-settings" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><ModelSettings /></ProtectedRoute></AppLayout>} />
              <Route path="/subscription" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><Subscription /></ProtectedRoute></AppLayout>} />
              <Route path="/subscription/manage" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><ManageSubscription /></ProtectedRoute></AppLayout>} />
              <Route path="/trades" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><Trades /></ProtectedRoute></AppLayout>} />
              <Route path="/portfolio-builder" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><PortfolioManager /></ProtectedRoute></AppLayout>} />
              <Route path="/portfolio-manager" element={<Navigate to="/portfolio-builder" replace />} />
              <Route path="/portfolio-news" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><PortfolioNews /></ProtectedRoute></AppLayout>} />
              <Route path="/api-test" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><ApiTest /></ProtectedRoute></AppLayout>} />
              <Route path="/agents" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><AgentManagement /></ProtectedRoute></AppLayout>} />
              <Route path="/agent-library" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><AgentManagement /></ProtectedRoute></AppLayout>} />
              <Route path="/agent-builder" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><MobileAwareRoute><AgentManagement /></MobileAwareRoute></ProtectedRoute></AppLayout>} />
              <Route path="/agent-builder/new" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><MobileAwareRoute><AgentBuilder /></MobileAwareRoute></ProtectedRoute></AppLayout>} />
              <Route path="/agent-builder/:id" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><MobileAwareRoute><AgentBuilder /></MobileAwareRoute></ProtectedRoute></AppLayout>} />
              <Route path="/discover" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><Discover /></ProtectedRoute></AppLayout>} />
              <Route path="/agent-scanner" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><AgentScanner /></ProtectedRoute></AppLayout>} />
              <Route path="/agent-backtesting" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><AgentBacktesting /></ProtectedRoute></AppLayout>} />
              <Route path="/agent-builder-docs" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><AgentBuilderDocs /></ProtectedRoute></AppLayout>} />
              <Route path="/stock-search" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><StockSearch /></ProtectedRoute></AppLayout>} />
              <Route path="/stock/:symbol" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><StockDetail /></ProtectedRoute></AppLayout>} />
              <Route path="/stripe-test" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><StripeTest /></ProtectedRoute></AppLayout>} />
              <Route path="/admin" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><Admin /></ProtectedRoute></AppLayout>} />
              <Route path="/admin-test" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><AdminTest /></ProtectedRoute></AppLayout>} />

              {/* Routes without AppLayout */}
              <Route path="/about" element={<About />} />
              <Route path="/terms" element={<TermsOfService />} />
              <Route path="/privacy" element={<PrivacyPolicy />} />
              <Route path="/callback/whop" element={<WhopCallback />} />
              <Route path="/login" element={<Navigate to="/" replace />} />

              {/* Default route - redirect to home */}
              <Route path="/" element={<AppLayout loading={isCheckingOnboarding}><ProtectedRoute><Home /></ProtectedRoute></AppLayout>} />
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </GamificationWrapper>
        </GamificationProvider>
      )}

      <AnimatePresence>
        {showOnboarding && (
          <Onboarding
            isOpen={showOnboarding}
            onClose={handleCloseOnboarding}
            supabase={supabase}
          />
        )}
      </AnimatePresence>

      {/* Video Popup - only show on main page when authenticated and not loading */}
      <AnimatePresence>
        {showVideoPopup && isUserAuthenticated && !isCheckingOnboarding && !showOnboarding && (
          <VideoPopup
            videoUrl="https://pajqstbgncpbpcaffbpm.supabase.co/storage/v1/object/public/videos//osislaunch.mp4"
            onClose={closeVideoPopup}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

const App = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: 1,
        staleTime: 5 * 60 * 1000,
        refetchOnWindowFocus: true,
        refetchOnReconnect: true,
      },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <WhopProvider>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <Router
              future={{
                v7_startTransition: true,
                v7_relativeSplatPath: true
              }}
            >
              <AppContent />
            </Router>
          </TooltipProvider>
        </WhopProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
};

export default App;

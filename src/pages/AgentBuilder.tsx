import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '@/components/ui/resizable';
import { Save, Play, ArrowLeft, Trash2, Sparkles, Settings, Circle, PenSquare, ChevronRight, ChevronLeft, Edit3, X, LayoutGrid } from 'lucide-react';
import { useAgentBuilder } from '@/hooks/useAgentBuilder';
import { getAgentById, saveAgent, runAgent } from '@/services/agentService';
import { useAuth } from '@/contexts/AuthContext';
import { WelcomeHeading } from '@/components/ui/WelcomeHeading';
import { useGamification } from '@/contexts/GamificationContext';
import AnimatedButton from '@/components/gamification/AnimatedButton';
import BlockPalette from '@/components/agent-builder/BlockPalette';
import BuildCanvas from '@/components/agent-builder/BuildCanvas';
import AgentRunDialog from '@/components/agent-builder/AgentRunDialog';
import AIAgentBuilder from '@/components/agent-builder/AIAgentBuilder';
import AgentPerformanceChart from '@/components/agent-builder/AgentPerformanceChart';

import AgentSettings from '@/components/agent-builder/AgentSettings';

// Add custom animation styles for the gradient heading
const animationStyles = `
  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .gradient-text {
    background: linear-gradient(90deg, rgba(255,255,255,0.95), rgba(200,200,200,0.8), rgba(170,170,170,0.9));
    background-size: 200% auto;
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient 8s ease infinite;
  }
`;

const AgentBuilder: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();
  const { isAuthenticated, user } = useAuth();
  const { trackAction } = useGamification();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [isRunDialogOpen, setIsRunDialogOpen] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<'ai' | 'canvas'>('ai');
  const [isPaletteExpanded, setIsPaletteExpanded] = useState<boolean>(false);
  const [isSettingsOpen, setIsSettingsOpen] = useState<boolean>(false);
  const [isFullscreen, setIsFullscreen] = useState<boolean>(false);
  const [showOverlay, setShowOverlay] = useState<'ai' | 'blocks' | false>(false);

  // Use ref to track saving state to avoid dependency loops
  const isSavingRef = useRef<boolean>(false);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (isAuthenticated === false) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to access the agent builder',
        variant: 'destructive'
      });
      navigate('/auth');
    }
  }, [isAuthenticated, navigate, toast]);

  // Initialize agent builder hook
  const {
    blocks,
    connections,
    entryBlockId,
    agentName,
    agentDescription,
    isPublic,
    publicDescription,
    tags,
    setAgentName,
    setAgentDescription,
    setIsPublic,
    setPublicDescription,
    setTags,
    addBlock,
    updateBlock,
    removeBlock,
    addConnection,
    removeConnection,
    setEntryBlock,
    clearBuilder,
    loadAgent,
    getAgentConfiguration,
    validateAgent,
    hasChanges,
    resetChanges,
    fixIllogicalConnections
  } = useAgentBuilder();

  // Load agent if ID is provided
  useEffect(() => {
    if (id) {
      setIsLoading(true);
      getAgentById(id)
        .then(agent => {
          if (agent) {
            loadAgent(agent);
          } else {
            toast({
              title: 'Error',
              description: 'Agent not found',
              variant: 'destructive'
            });
            navigate('/agents');
          }
        })
        .catch(error => {
          console.error('Error loading agent:', error);
          toast({
            title: 'Error',
            description: 'Failed to load agent',
            variant: 'destructive'
          });
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [id, loadAgent, navigate, toast]);

  // Handle imported agent from Discovery page
  useEffect(() => {
    const state = location.state as { importedAgent?: any };
    if (state?.importedAgent && !id) {
      // Load the imported agent
      loadAgent(state.importedAgent);

      toast({
        title: 'Agent Imported',
        description: 'Agent imported successfully. You can now customize it.'
      });

      // Clear the state to prevent re-loading
      navigate(location.pathname, { replace: true, state: {} });
    }
  }, [location.state, id, loadAgent, navigate, toast]);

  // Validate agent and get error details
  const { valid, errors, errorDetails, disconnectedBlocks } = validateAgent();

  // Automatically fix illogical connections when detected
  useEffect(() => {
    // Check if there are any illogical connection errors
    const hasIllogicalConnections = errors.some(error =>
      error.includes('illogical connections') ||
      error.includes('both TRUE and FALSE outputs connect to the same target')
    );

    if (hasIllogicalConnections) {
      console.log('🚨 Detected illogical connections, automatically fixing...');
      const fixedCount = fixIllogicalConnections();

      if (fixedCount > 0) {
        toast({
          title: 'Fixed Illogical Connections',
          description: `Automatically removed ${fixedCount} illogical connection(s) where both TRUE and FALSE outputs connected to the same target.`,
          variant: 'default'
        });
      }
    }
  }, [errors, fixIllogicalConnections, toast]);

  // Handle save
  const handleSave = async () => {
    // Check if user is authenticated
    if (!isAuthenticated || !user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to save your agent',
        variant: 'destructive'
      });
      navigate('/auth');
      return;
    }

    // Validate agent
    const { valid, errors } = validateAgent();
    if (!valid) {
      toast({
        title: 'Validation Error',
        description: errors.join(', '),
        variant: 'destructive'
      });
      return;
    }

    // Save agent
    setIsSaving(true);
    isSavingRef.current = true;
    try {
      const agentConfig = getAgentConfiguration();
      console.log('Saving agent with config:', agentConfig);
      console.log('Public settings:', {
        isPublic: agentConfig.is_public,
        publicDescription: agentConfig.public_description,
        tags: agentConfig.tags
      });

      // Check if agent was made public for the first time
      // For existing agents, check if it was private before and is now public
      // For new agents, check if it's being created as public
      let wasPrivate = true;
      let isBeingMadePublic = agentConfig.is_public;

      if (id) {
        // For existing agents, get the current state from the loaded agent
        try {
          const currentAgent = await getAgentById(id);
          wasPrivate = !currentAgent?.is_public;
        } catch (error) {
          console.error('Error checking current agent state:', error);
          // Default to assuming it was private
          wasPrivate = true;
        }
      }

      const savedAgent = await saveAgent({
        ...agentConfig,
        id
      });

      toast({
        title: 'Success',
        description: `Agent saved successfully${agentConfig.is_public ? ' and made public' : ''}`
      });

      // Trigger gamification for agent creation/save
      trackAction('agent_built');

      // Track public agent creation if agent was made public for the first time
      if (wasPrivate && isBeingMadePublic) {
        console.log('🌟 Agent made public for the first time, tracking public agent creation');
        await trackAction('public_agent_created');
      }

      // Reset changes flag
      resetChanges();

      // Navigate to the agent page if this is a new agent
      if (!id) {
        navigate(`/agent-builder/${savedAgent.id}`);
      }
    } catch (error: any) {
      console.error('Error saving agent:', error);

      // Handle authentication errors
      if (error.message === 'User not authenticated') {
        toast({
          title: 'Authentication Required',
          description: 'Please log in to save your agent',
          variant: 'destructive'
        });
        navigate('/auth');
      } else {
        toast({
          title: 'Error',
          description: error.message || 'Failed to save agent',
          variant: 'destructive'
        });
      }
    } finally {
      setIsSaving(false);
      isSavingRef.current = false;
    }
  };

  // Autosave functionality
  useEffect(() => {
    if (!hasChanges || !isAuthenticated || !user) return;

    // Debounce the autosave to avoid too many requests
    const autosaveTimeout = setTimeout(async () => {
      // Check if we're already saving to prevent conflicts
      if (isSavingRef.current) return;

      // Only autosave if the agent is valid
      const { valid } = validateAgent();
      if (!valid) return;

      // Don't autosave if we don't have an ID yet
      if (!id) return;

      setIsSaving(true);
      isSavingRef.current = true;
      try {
        const agentConfig = getAgentConfiguration();
        await saveAgent({
          ...agentConfig,
          id
        });

        // Reset changes flag
        resetChanges();

        console.log('Agent autosaved');
      } catch (error: any) {
        console.error('Error autosaving agent:', error);

        // If authentication error, don't keep trying to autosave
        if (error.message === 'User not authenticated') {
          // We'll let the user know when they try to manually save
          console.log('Autosave disabled: User not authenticated');
        }
      } finally {
        setIsSaving(false);
        isSavingRef.current = false;
      }
    }, 2000); // 2 second debounce

    return () => clearTimeout(autosaveTimeout);
  }, [hasChanges, id, isAuthenticated, user, getAgentConfiguration, validateAgent, resetChanges]);

  // Handle run
  const handleRun = () => {
    // Check if user is authenticated
    if (!isAuthenticated || !user) {
      toast({
        title: 'Authentication Required',
        description: 'Please log in to run your agent',
        variant: 'destructive'
      });
      navigate('/auth');
      return;
    }

    // Validate agent
    const { valid, errors } = validateAgent();
    if (!valid) {
      toast({
        title: 'Validation Error',
        description: errors.join(', '),
        variant: 'destructive'
      });
      return;
    }

    // Open run dialog
    setIsRunDialogOpen(true);
  };



  // Handle block drop
  const handleBlockDrop = (type: string, position: { x: number; y: number }, properties?: Record<string, any>) => {
    addBlock(type, position, properties);
    // Trigger gamification for block addition
    trackAction('block_added');
  };

  // Handle AI agent generation
  const handleAIAgentGenerated = (aiAgent: {
    blocks: any[];
    entryBlockId: string;
    name: string;
    description: string;
  }) => {
    // Clear existing agent
    clearBuilder();

    // Load the AI-generated agent
    loadAgent({
      name: aiAgent.name,
      description: aiAgent.description,
      configuration: {
        blocks: aiAgent.blocks,
        entryBlockId: aiAgent.entryBlockId
      }
    });

    toast({
      title: 'Simple Agent Created! 🎉',
      description: `Your "${aiAgent.name}" is ready to use. Switch to the Design tab to see how it works, or click Test to try it out!`
    });
  };

  // Handle settings save
  const handleSettingsSave = (settings: {
    name: string;
    description: string;
    isPublic: boolean;
    publicDescription: string;
    tags: string[];
  }) => {
    setAgentName(settings.name);
    setAgentDescription(settings.description);
    setIsPublic(settings.isPublic);
    setPublicDescription(settings.publicDescription);
    setTags(settings.tags);

      toast({
        title: "Settings Updated",
      description: "Agent settings have been updated successfully.",
      });
  };

  // Handle fullscreen toggle (canvas-only mode)
  const handleFullscreenToggle = () => {
    setIsFullscreen(!isFullscreen);
    if (isFullscreen) {
      setShowOverlay(false);
    }
  };

  return (
    <div className={`${isFullscreen ? 'fixed inset-0 z-50' : 'h-full'} bg-[#0A0A0A] text-white flex flex-col`}>
      <style>{animationStyles}</style>

      {/* Header Section - Hidden in fullscreen */}
      {!isFullscreen && (
        <div className="flex items-start justify-between px-4 pt-2 pb-3 border-b border-[#1A1A1A]/30">
          <div className="flex items-start gap-3">
            <button
              onClick={() => navigate('/agents')}
              className="text-white/60 hover:text-white transition-colors duration-200 mt-1"
            >
              <ArrowLeft className="h-4 w-4" />
            </button>
            <div className="flex flex-col">
              <div className="flex items-center gap-2">
                <WelcomeHeading
                  text={agentName || 'Build Your Agent'}
                  className="gradient-text text-lg font-semibold leading-tight"
                  speed={80}
                />
                <button
                  onClick={() => setIsSettingsOpen(true)}
                  className="text-white/40 hover:text-white/70 transition-colors duration-200 p-1 rounded-md hover:bg-white/[0.05]"
                  title="Edit agent settings"
                >
                  <Settings className="h-4 w-4" />
                </button>
              </div>
              {agentDescription && (
                <p className="text-xs text-white/60 mt-0.5">{agentDescription}</p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Main Content Area */}
      {isFullscreen ? (
        /* Fullscreen Canvas Only */
        <div className="flex-1 h-full relative">
          <BuildCanvas
            blocks={blocks}
            connections={connections}
            entryBlockId={entryBlockId}
            onBlockUpdate={updateBlock}
            onBlockRemove={removeBlock}
            onConnectionAdd={addConnection}
            onConnectionRemove={removeConnection}
            onSetEntryBlock={setEntryBlock}
            addBlock={addBlock}
            errorDetails={errorDetails}
            disconnectedBlocks={disconnectedBlocks}
            onFullscreenToggle={handleFullscreenToggle}
            isFullscreen={isFullscreen}
          />

          {/* Top-Right Control Panel */}
          <div className="absolute top-4 right-4 flex flex-col gap-3 z-50">
            {/* Control Buttons */}
            <div className="flex gap-2">
              <button
                onClick={() => setShowOverlay(showOverlay === 'ai' ? false : 'ai')}
                className={`px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 flex items-center gap-2 shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] ${
                  showOverlay === 'ai'
                    ? 'bg-gradient-to-b from-green-500/20 to-green-600/20 text-green-300 border border-green-500/40 shadow-[0_0_20px_rgba(34,197,94,0.3)]'
                    : 'bg-gradient-to-b from-white/[0.08] to-white/[0.04] text-white/80 hover:text-white border border-white/[0.12] hover:border-white/[0.2] hover:shadow-[0_0_15px_rgba(255,255,255,0.1)]'
                }`}
              >
                <Sparkles className="w-3.5 h-3.5" />
                AI
              </button>
              <button
                onClick={() => setShowOverlay(showOverlay === 'blocks' ? false : 'blocks')}
                className={`px-3 py-2 rounded-lg text-xs font-medium transition-all duration-300 flex items-center gap-2 shadow-[inset_0_1px_0_rgba(255,255,255,0.1)] ${
                  showOverlay === 'blocks'
                    ? 'bg-gradient-to-b from-blue-500/20 to-blue-600/20 text-blue-300 border border-blue-500/40 shadow-[0_0_20px_rgba(59,130,246,0.3)]'
                    : 'bg-gradient-to-b from-white/[0.08] to-white/[0.04] text-white/80 hover:text-white border border-white/[0.12] hover:border-white/[0.2] hover:shadow-[0_0_15px_rgba(255,255,255,0.1)]'
                }`}
              >
                <LayoutGrid className="w-3.5 h-3.5" />
                Blocks
              </button>
            </div>

            {/* Floating AI Assistant Card */}
            {showOverlay === 'ai' && (
              <div className="w-80 h-96 bg-gradient-to-b from-[#0A0A0A] to-[#0F0F0F] border border-white/[0.12] rounded-xl shadow-[0_8px_32px_rgba(0,0,0,0.3)] backdrop-blur-sm flex flex-col overflow-hidden">
                <div className="flex-1 overflow-hidden">
                  <AIAgentBuilder
                    onAgentGenerated={handleAIAgentGenerated}
                    userId={user?.id || ''}
                  />
                </div>
              </div>
            )}

            {/* Floating Blocks Palette Card */}
            {showOverlay === 'blocks' && (
              <div className="w-72 h-[500px] bg-gradient-to-b from-[#0A0A0A] to-[#0F0F0F] border border-white/[0.12] rounded-xl shadow-[0_8px_32px_rgba(0,0,0,0.3)] backdrop-blur-sm flex flex-col overflow-hidden">
                <div className="flex-1 overflow-y-auto p-3">
                  <BlockPalette onBlockDrop={handleBlockDrop} />
                </div>
              </div>
            )}
          </div>
        </div>
      ) : (
        /* Normal Layout */
        <div className="flex-1 flex overflow-hidden bg-[#0A0A0A]">
          <ResizablePanelGroup direction="horizontal" className="h-full">
            {/* Block Palette Sidebar - Only show on Design Canvas tab */}
            {activeTab === 'canvas' && (
              <>
                <ResizablePanel
                  defaultSize={30}
                  minSize={20}
                  maxSize={40}
                  className="relative"
                >
                  <div className="h-full bg-[#0A0A0A] border-r border-[#1A1A1A]/30 overflow-y-auto custom-scrollbar">
                    <div className="p-4">
                      {/* Toggle switch positioned in left corner above Block Palette */}
                      <div className="relative bg-[#0A0A0A] border-b border-[#1A1A1A]/30">
                        <div className="flex items-center justify-start py-3 px-2">
                          <div className="bg-[#0A0A0A] border border-[#1A1A1A]/30 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] rounded-full p-0.5 flex">
                            <button
                              onClick={() => setActiveTab('ai')}
                              className={`flex items-center gap-1.5 ${activeTab === 'ai' ? 'bg-[#141414] text-white shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)]' : ''} rounded-full px-3 py-1.5 transition-all duration-300 text-sm`}
                            >
                              <Sparkles className="h-3.5 w-3.5" />
                              Build with AI
                            </button>
                            <button
                              onClick={() => setActiveTab('canvas')}
                              className={`flex items-center gap-1.5 ${activeTab === 'canvas' ? 'bg-[#141414] text-white shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)]' : ''} rounded-full px-3 py-1.5 transition-all duration-300 text-sm`}
                            >
                              <Settings className="h-3.5 w-3.5" />
                              Design Canvas
                            </button>
                          </div>
                        </div>
                        {/* Subtle bottom glow */}
                        <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/10 to-transparent"></div>
                      </div>
                      <BlockPalette onBlockDrop={handleBlockDrop} />
                    </div>
                  </div>
                </ResizablePanel>
                <ResizableHandle withHandle className="bg-[#1A1A1A]/30 hover:bg-[#1A1A1A]/50 w-2" />
              </>
            )}

          {/* Main Canvas Area */}
          <ResizablePanel defaultSize={activeTab === 'canvas' ? 70 : 100}>
            <Tabs
              value={activeTab}
              className="h-full flex flex-col"
              onValueChange={(value) => {
                setActiveTab(value as 'ai' | 'canvas');
              }}
            >
              {/* Toggle switch positioned above content - only show on AI tab */}
              {activeTab === 'ai' && (
                <div className="flex items-center justify-between py-4 px-4 border-b border-[#1A1A1A]/30">
                  {/* Toggle switch */}
                  <TabsList className="bg-[#0A0A0A] border border-[#1A1A1A]/30 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] rounded-full p-1">
                    <TabsTrigger
                      value="ai"
                      className="flex items-center gap-2 data-[state=active]:bg-[#141414] data-[state=active]:text-white data-[state=active]:shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] rounded-full px-4 py-2 transition-all duration-300"
                    >
                      <Sparkles className="h-4 w-4" />
                      Build with AI
                    </TabsTrigger>
                    <TabsTrigger
                      value="canvas"
                      className="flex items-center gap-2 data-[state=active]:bg-[#141414] data-[state=active]:text-white data-[state=active]:shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] rounded-full px-4 py-2 transition-all duration-300"
                    >
                      <Settings className="h-4 w-4" />
                      Design Canvas
                    </TabsTrigger>
                  </TabsList>
                  {/* Action buttons */}
                  <div className="flex gap-2">
                    <button
                      onClick={clearBuilder}
                      className="flex items-center gap-2 px-2 py-1 text-xs font-medium text-white/70 hover:text-white/90 transition-colors duration-200"
                    >
                      <Trash2 className="h-3.5 w-3.5" />
                      Clear
                    </button>
                    <button
                      onClick={handleRun}
                      disabled={isLoading || isSaving}
                      className="flex items-center gap-2 px-3 py-2 text-xs font-medium bg-[#141414]/60 border border-[#303035]/50 rounded-md hover:bg-[#1A1A1A]/80 transition-all duration-200 text-white/70 hover:text-white/90 disabled:opacity-50 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] hover:shadow-[inset_0_2px_4px_0_rgba(0,0,0,0.3)]"
                    >
                      <Play className="h-3.5 w-3.5" />
                      Test
                    </button>
                    <button
                      onClick={handleSave}
                      disabled={isLoading || isSaving}
                      className="flex items-center gap-2 px-3 py-2 text-xs font-medium bg-[#141414]/60 border border-[#303035]/50 rounded-md hover:bg-[#1A1A1A]/80 transition-all duration-200 text-white/70 hover:text-white/90 disabled:opacity-50 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] hover:shadow-[inset_0_2px_4px_0_rgba(0,0,0,0.3)]"
                    >
                      <Save className="h-3.5 w-3.5" />
                      {isSaving ? 'Saving...' : 'Save'}
                    </button>
                  </div>
                </div>
              )}

              <TabsContent value="ai" className="flex-1 p-0 m-0 h-full animate-fade-in relative">
                <ResizablePanelGroup direction="horizontal" className="h-full">
                  <ResizablePanel defaultSize={40} minSize={30} maxSize={50} className="h-full">
                     <AIAgentBuilder
                       onAgentGenerated={handleAIAgentGenerated}
                       userId={user?.id || ''}
                     />
                  </ResizablePanel>
                  <ResizableHandle withHandle className="bg-[#1A1A1A]/30 hover:bg-[#1A1A1A]/50 w-2" />
                  <ResizablePanel defaultSize={60} className="h-full relative">
                    <div className="h-full flex flex-col">
                      {/* Canvas */}
                      <div className="flex-1">
                        <BuildCanvas
                          blocks={blocks}
                          connections={connections}
                          entryBlockId={entryBlockId}
                          onBlockUpdate={updateBlock}
                          onBlockRemove={removeBlock}
                          onConnectionAdd={addConnection}
                          onConnectionRemove={removeConnection}
                          onSetEntryBlock={setEntryBlock}
                          addBlock={addBlock}
                          errorDetails={errorDetails}
                          disconnectedBlocks={disconnectedBlocks}
                          onFullscreenToggle={handleFullscreenToggle}
                          isFullscreen={isFullscreen}
                        />
                      </div>
                    </div>
                  </ResizablePanel>
                </ResizablePanelGroup>
              </TabsContent>

              <TabsContent value="canvas" className="flex-1 p-0 m-0 animate-fade-in relative">
                {/* Action buttons positioned at top-right on canvas - matching AI tab styling */}
                <div className="absolute top-4 right-4 z-10 flex gap-2">
                  <button
                    onClick={clearBuilder}
                    className="flex items-center gap-2 px-2 py-1 text-xs font-medium text-white/70 hover:text-white/90 transition-colors duration-200"
                  >
                    <Trash2 className="h-3.5 w-3.5" />
                    Clear
                  </button>
                  <button
                    onClick={handleRun}
                    disabled={isLoading || isSaving}
                    className="flex items-center gap-2 px-3 py-2 text-xs font-medium bg-[#141414]/60 border border-[#303035]/50 rounded-md hover:bg-[#1A1A1A]/80 transition-all duration-200 text-white/70 hover:text-white/90 disabled:opacity-50 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] hover:shadow-[inset_0_2px_4px_0_rgba(0,0,0,0.3)]"
                  >
                    <Play className="h-3.5 w-3.5" />
                    Test
                  </button>
                  <button
                    onClick={handleSave}
                    disabled={isLoading || isSaving}
                    className="flex items-center gap-2 px-3 py-2 text-xs font-medium bg-[#141414]/60 border border-[#303035]/50 rounded-md hover:bg-[#1A1A1A]/80 transition-all duration-200 text-white/70 hover:text-white/90 disabled:opacity-50 shadow-[inset_0_1px_0_0_rgba(255,255,255,0.05)] hover:shadow-[inset_0_2px_4px_0_rgba(0,0,0,0.3)]"
                  >
                    <Save className="h-3.5 w-3.5" />
                    {isSaving ? 'Saving...' : 'Save'}
                  </button>
                </div>

                {/* Canvas with top padding to avoid button overlap */}
                <div className="pt-16 h-full">
                  <BuildCanvas
                    blocks={blocks}
                    connections={connections}
                    entryBlockId={entryBlockId}
                    onBlockUpdate={updateBlock}
                    onBlockRemove={removeBlock}
                    onConnectionAdd={addConnection}
                    onConnectionRemove={removeConnection}
                    onSetEntryBlock={setEntryBlock}
                    addBlock={addBlock}
                    errorDetails={errorDetails}
                    disconnectedBlocks={disconnectedBlocks}
                    onFullscreenToggle={handleFullscreenToggle}
                    isFullscreen={isFullscreen}
                  />
                </div>
              </TabsContent>
            </Tabs>
          </ResizablePanel>
        </ResizablePanelGroup>
        </div>
      )}



      {/* Run Dialog */}
      <AgentRunDialog
        open={isRunDialogOpen}
        onOpenChange={setIsRunDialogOpen}
        agentId={id}
        agentConfig={!id ? getAgentConfiguration().configuration : undefined}
      />

      {/* Agent Settings Dialog */}
      <AgentSettings
        open={isSettingsOpen}
        onOpenChange={setIsSettingsOpen}
        agentName={agentName}
        agentDescription={agentDescription}
        isPublic={isPublic}
        publicDescription={publicDescription}
        tags={tags}
        onSave={handleSettingsSave}
      />

      {/* Global Styles */}
      <style>
        {`
          .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
          }
          .custom-scrollbar::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 3px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
          }
          .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.2);
          }

          /* Tab switching animations */
          @keyframes fadeIn {
            from {
              opacity: 0;
              transform: translateY(8px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }

          .animate-fade-in {
            animation: fadeIn 400ms cubic-bezier(0.22, 1, 0.36, 1) forwards;
          }

          /* Focus styles for inputs */
          input:focus-visible,
          textarea:focus-visible {
            outline: none !important;
            border-color: #303035 !important;
            box-shadow: none !important;
            ring: 0 !important;
          }
          input:focus,
          textarea:focus {
            outline: none !important;
            border-color: #303035 !important;
            box-shadow: none !important;
            ring: 0 !important;
          }
        `}
      </style>
    </div>
  );
};

export default AgentBuilder;

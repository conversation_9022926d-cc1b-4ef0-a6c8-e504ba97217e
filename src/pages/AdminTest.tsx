import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Shield, CheckCircle, AlertTriangle, Users, Ban, Trash2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import VerificationBadge from '@/components/ui/verification-badge';
import { supabase } from '@/integrations/supabase/client';

const AdminTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [testResults, setTestResults] = useState<any>({});
  const { toast } = useToast();

  useEffect(() => {
    getCurrentUser();
  }, []);

  const getCurrentUser = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      setCurrentUser(user);
      
      if (user) {
        // Get user profile
        const { data: profile } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
        
        setTestResults(prev => ({
          ...prev,
          userProfile: profile,
          userEmail: user.email
        }));
      }
    } catch (error) {
      console.error('Error getting user:', error);
    }
  };

  const testAdminFunctions = async () => {
    if (!currentUser) {
      toast({
        title: "Error",
        description: "Please log in first",
        variant: "destructive"
      });
      return;
    }

    setLoading(true);
    const results: any = {};

    try {
      // Test 1: Check if admin functions exist
      try {
        const { data, error } = await supabase.rpc('is_admin', { user_id: currentUser.id });
        results.isAdminFunctionExists = !error;
        results.isAdminResult = data;
        results.isAdminError = error?.message;
      } catch (error) {
        results.isAdminFunctionExists = false;
        results.isAdminError = error.message;
      }

      // Test 2: Check if content filter function exists
      try {
        const { data, error } = await supabase.rpc('check_content_filter', { 
          p_content: 'test content' 
        });
        results.contentFilterExists = !error;
        results.contentFilterResult = data;
        results.contentFilterError = error?.message;
      } catch (error) {
        results.contentFilterExists = false;
        results.contentFilterError = error.message;
      }

      // Test 3: Check if admin tables exist
      try {
        const { data, error } = await supabase
          .from('admin_action_logs')
          .select('count')
          .limit(1);
        results.adminTablesExist = !error;
        results.adminTablesError = error?.message;
      } catch (error) {
        results.adminTablesExist = false;
        results.adminTablesError = error.message;
      }

      // Test 4: Check if verification columns exist on agents table
      try {
        const { data, error } = await supabase
          .from('agents')
          .select('is_verified, verified_at, verified_by')
          .limit(1);
        results.verificationColumnsExist = !error;
        results.verificationColumnsError = error?.message;
      } catch (error) {
        results.verificationColumnsExist = false;
        results.verificationColumnsError = error.message;
      }

      // Test 5: Check if role column exists on profiles table
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('role')
          .eq('id', currentUser.id)
          .single();
        results.roleColumnExists = !error;
        results.userRole = data?.role;
        results.roleColumnError = error?.message;
      } catch (error) {
        results.roleColumnExists = false;
        results.roleColumnError = error.message;
      }

      setTestResults(prev => ({ ...prev, ...results }));

      toast({
        title: "Tests Complete",
        description: "Check the results below"
      });

    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to run tests",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const makeUserAdmin = async () => {
    if (!currentUser) return;

    try {
      const { error } = await supabase
        .from('profiles')
        .update({ role: 'admin' })
        .eq('id', currentUser.id);

      if (error) throw error;

      toast({
        title: "Success",
        description: "User role updated to admin"
      });

      // Refresh test results
      testAdminFunctions();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update user role",
        variant: "destructive"
      });
    }
  };

  return (
    <div className="min-h-screen bg-[#0A0A0A] text-white p-8">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="flex items-center gap-3">
          <Shield className="w-8 h-8 text-blue-400" />
          <div>
            <h1 className="text-2xl font-bold text-white">Admin System Test</h1>
            <p className="text-white/60">Test admin functionality and database setup</p>
          </div>
        </div>

        {/* Current User Info */}
        <Card className="bg-white/[0.02] border-white/[0.08]">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Users className="w-5 h-5" />
              Current User
            </CardTitle>
          </CardHeader>
          <CardContent>
            {currentUser ? (
              <div className="space-y-2">
                <p><strong>Email:</strong> {testResults.userEmail}</p>
                <p><strong>User ID:</strong> {currentUser.id}</p>
                <p><strong>Role:</strong> {testResults.userRole || 'user'}</p>
                {testResults.userProfile && (
                  <p><strong>Profile:</strong> {JSON.stringify(testResults.userProfile, null, 2)}</p>
                )}
              </div>
            ) : (
              <p className="text-white/60">Not logged in</p>
            )}
          </CardContent>
        </Card>

        {/* Test Controls */}
        <Card className="bg-white/[0.02] border-white/[0.08]">
          <CardHeader>
            <CardTitle className="text-white">Test Controls</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={testAdminFunctions}
              disabled={loading || !currentUser}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading ? 'Testing...' : 'Run Admin Tests'}
            </Button>
            
            <Button
              onClick={makeUserAdmin}
              disabled={!currentUser}
              className="bg-red-600 hover:bg-red-700"
            >
              Make Current User Admin
            </Button>
          </CardContent>
        </Card>

        {/* Test Results */}
        <Card className="bg-white/[0.02] border-white/[0.08]">
          <CardHeader>
            <CardTitle className="text-white">Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Admin Function Test */}
              <div className="flex items-center gap-3">
                {testResults.isAdminFunctionExists ? (
                  <CheckCircle className="w-5 h-5 text-green-400" />
                ) : (
                  <AlertTriangle className="w-5 h-5 text-red-400" />
                )}
                <div>
                  <p className="font-medium">is_admin() Function</p>
                  <p className="text-sm text-white/60">
                    {testResults.isAdminFunctionExists ? 
                      `Exists - Result: ${testResults.isAdminResult}` : 
                      `Missing - Error: ${testResults.isAdminError}`
                    }
                  </p>
                </div>
              </div>

              {/* Content Filter Test */}
              <div className="flex items-center gap-3">
                {testResults.contentFilterExists ? (
                  <CheckCircle className="w-5 h-5 text-green-400" />
                ) : (
                  <AlertTriangle className="w-5 h-5 text-red-400" />
                )}
                <div>
                  <p className="font-medium">check_content_filter() Function</p>
                  <p className="text-sm text-white/60">
                    {testResults.contentFilterExists ? 
                      'Exists and working' : 
                      `Missing - Error: ${testResults.contentFilterError}`
                    }
                  </p>
                </div>
              </div>

              {/* Admin Tables Test */}
              <div className="flex items-center gap-3">
                {testResults.adminTablesExist ? (
                  <CheckCircle className="w-5 h-5 text-green-400" />
                ) : (
                  <AlertTriangle className="w-5 h-5 text-red-400" />
                )}
                <div>
                  <p className="font-medium">Admin Tables</p>
                  <p className="text-sm text-white/60">
                    {testResults.adminTablesExist ? 
                      'admin_action_logs table exists' : 
                      `Missing - Error: ${testResults.adminTablesError}`
                    }
                  </p>
                </div>
              </div>

              {/* Verification Columns Test */}
              <div className="flex items-center gap-3">
                {testResults.verificationColumnsExist ? (
                  <CheckCircle className="w-5 h-5 text-green-400" />
                ) : (
                  <AlertTriangle className="w-5 h-5 text-red-400" />
                )}
                <div>
                  <p className="font-medium">Agent Verification Columns</p>
                  <p className="text-sm text-white/60">
                    {testResults.verificationColumnsExist ? 
                      'is_verified, verified_at, verified_by columns exist' : 
                      `Missing - Error: ${testResults.verificationColumnsError}`
                    }
                  </p>
                </div>
              </div>

              {/* Role Column Test */}
              <div className="flex items-center gap-3">
                {testResults.roleColumnExists ? (
                  <CheckCircle className="w-5 h-5 text-green-400" />
                ) : (
                  <AlertTriangle className="w-5 h-5 text-red-400" />
                )}
                <div>
                  <p className="font-medium">User Role Column</p>
                  <p className="text-sm text-white/60">
                    {testResults.roleColumnExists ? 
                      `role column exists - Current role: ${testResults.userRole}` : 
                      `Missing - Error: ${testResults.roleColumnError}`
                    }
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Verification Badge Test */}
        <Card className="bg-white/[0.02] border-white/[0.08]">
          <CardHeader>
            <CardTitle className="text-white">UI Components Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <p className="text-sm text-white/60 mb-2">Verification Badge (Verified):</p>
              <VerificationBadge 
                isVerified={true}
                verifiedAt={new Date().toISOString()}
                size="md"
              />
            </div>
            
            <div>
              <p className="text-sm text-white/60 mb-2">Verification Badge (Not Verified):</p>
              <VerificationBadge 
                isVerified={false}
                size="md"
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminTest;

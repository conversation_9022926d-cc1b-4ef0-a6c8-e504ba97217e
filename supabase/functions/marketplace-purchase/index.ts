import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import Stripe from "https://esm.sh/stripe@14.21.0"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};

// Handle OPTIONS requests for CORS
function handleOptions() {
  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
}

// Initialize Supabase client
const supabase = createClient(
  Deno.env.get('SUPABASE_URL') || '',
  Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
);

// Initialize Stripe client
const stripe = new Stripe(Deno.env.get('STRIPE_SECRET_KEY_TEST') || '', {
  apiVersion: '2023-10-16',
});

interface PurchaseAgentRequest {
  action: 'purchase-agent';
  agent_id: string;
  custom_name?: string;
  affiliateCode?: string;
}

interface ConfirmPaymentRequest {
  action: 'confirm-payment';
  payment_intent_id: string;
  custom_name?: string;
}

interface GetPurchasedAgentsRequest {
  action: 'get-purchased-agents';
}

interface GetSellerEarningsRequest {
  action: 'get-seller-earnings';
}

type RequestBody = PurchaseAgentRequest | ConfirmPaymentRequest | GetPurchasedAgentsRequest | GetSellerEarningsRequest;

serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return handleOptions();
  }

  try {
    // Get the authenticated user
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      throw new Error('No authorization header');
    }

    const { data: { user }, error: authError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    );

    if (authError || !user) {
      throw new Error('Invalid authentication');
    }

    const requestData: RequestBody = await req.json();

    switch (requestData.action) {
      case 'purchase-agent': {
        console.log(`Initiating purchase for agent ${requestData.agent_id} by user ${user.id}`);

        // Get agent details
        const { data: agent, error: agentError } = await supabase
          .from('agents')
          .select('*')
          .eq('id', requestData.agent_id)
          .eq('is_for_sale', true)
          .single();

        if (agentError || !agent) {
          throw new Error('Agent not found or not for sale');
        }

        if (!agent.price || agent.price <= 0) {
          throw new Error('Agent price is invalid');
        }

        // Check if user is trying to buy their own agent
        if (agent.user_id === user.id) {
          throw new Error('You cannot purchase your own agent');
        }

        // Check if user already owns this agent
        const { data: existingPurchase } = await supabase
          .from('purchased_agents')
          .select('id')
          .eq('buyer_id', user.id)
          .eq('original_agent_id', requestData.agent_id)
          .single();

        if (existingPurchase) {
          throw new Error('You already own this agent');
        }

        // Get seller's Stripe account
        const { data: sellerAccount, error: sellerError } = await supabase
          .from('seller_accounts')
          .select('*')
          .eq('user_id', agent.user_id)
          .single();

        if (sellerError || !sellerAccount) {
          throw new Error('Seller account not found');
        }

        if (!sellerAccount.charges_enabled) {
          throw new Error('Seller account is not ready to receive payments');
        }

        // Calculate platform fee (15%)
        const platformFeePercentage = 15;
        const platformFee = Math.round(agent.price * (platformFeePercentage / 100) * 100); // in cents
        const sellerAmount = Math.round(agent.price * 100) - platformFee; // in cents

        // Create Stripe Checkout Session
        const session = await stripe.checkout.sessions.create({
          payment_method_types: ['card'],
          line_items: [
            {
              price_data: {
                currency: 'usd',
                product_data: {
                  name: agent.name,
                  description: agent.description || 'AI Trading Agent',
                },
                unit_amount: Math.round(agent.price * 100),
              },
              quantity: 1,
            },
          ],
          mode: 'payment',
          success_url: `${Deno.env.get('SITE_URL') || 'http://localhost:8080'}/marketplace?purchase=success&session_id={CHECKOUT_SESSION_ID}`,
          cancel_url: `${Deno.env.get('SITE_URL') || 'http://localhost:8080'}/marketplace?purchase=cancelled`,
          payment_intent_data: {
            application_fee_amount: platformFee,
            transfer_data: {
              destination: sellerAccount.stripe_account_id,
            },
            metadata: {
              agent_id: requestData.agent_id,
              buyer_id: user.id,
              seller_id: agent.user_id,
              custom_name: requestData.custom_name || '',
              ...(requestData.affiliateCode && { growi_affiliate_code: requestData.affiliateCode }),
            }
          },
          metadata: {
            agent_id: requestData.agent_id,
            buyer_id: user.id,
            seller_id: agent.user_id,
            custom_name: requestData.custom_name || '',
            ...(requestData.affiliateCode && { growi_affiliate_code: requestData.affiliateCode }),
          }
        });

        return new Response(JSON.stringify({
          success: true,
          checkout_url: session.url,
          session_id: session.id
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      case 'confirm-payment': {
        console.log(`Confirming payment for payment intent ${requestData.payment_intent_id}`);

        // Get payment intent from Stripe
        const paymentIntent = await stripe.paymentIntents.retrieve(requestData.payment_intent_id);

        if (paymentIntent.status !== 'succeeded') {
          throw new Error('Payment has not succeeded');
        }

        // Get transaction from database
        const { data: transaction, error: transactionError } = await supabase
          .from('marketplace_transactions')
          .select('*')
          .eq('stripe_payment_intent_id', requestData.payment_intent_id)
          .single();

        if (transactionError || !transaction) {
          throw new Error('Transaction not found');
        }

        // SECURITY: Get original agent and encrypt it if it's a paid agent
        const { data: originalAgent, error: agentError } = await supabase
          .from('agents')
          .select('*')
          .eq('id', transaction.agent_id)
          .single();

        if (agentError || !originalAgent) {
          throw new Error('Original agent not found');
        }

        // SECURITY: Encrypt agent configuration if it's a paid agent
        if (originalAgent.price && originalAgent.price > 0 && !originalAgent.is_encrypted) {
          const { error: encryptError } = await supabase
            .rpc('encrypt_agent_config', {
              p_agent_id: originalAgent.id
            });

          if (encryptError) {
            console.error('Failed to encrypt agent config:', encryptError);
            // Continue anyway - encryption is not critical for this transaction
          }
        }

        // SECURITY: Generate secure license instead of creating agent copy
        const { data: licenseKey, error: licenseError } = await supabase
          .rpc('create_secure_agent_license', {
            p_buyer_id: transaction.buyer_id,
            p_original_agent_id: transaction.agent_id,
            p_custom_name: requestData.custom_name,
            p_max_usage_limit: null, // Unlimited usage for now
            p_license_expires_at: null // Never expires for now
          });

        if (licenseError || !licenseKey) {
          throw new Error('Failed to create secure license');
        }

        // Generate buyer fingerprint for anti-sharing protection
        const buyerFingerprint = await crypto.subtle.digest(
          'SHA-256',
          new TextEncoder().encode(transaction.buyer_id + Date.now().toString())
        );
        const fingerprintHex = Array.from(new Uint8Array(buyerFingerprint))
          .map(b => b.toString(16).padStart(2, '0'))
          .join('');

        // Create secure license record
        const { error: licenseRecordError } = await supabase
          .from('agent_licenses')
          .insert({
            license_key: licenseKey,
            buyer_id: transaction.buyer_id,
            seller_id: transaction.seller_id,
            agent_id: transaction.agent_id,
            transaction_id: transaction.id,
            custom_name: requestData.custom_name,
            purchase_price: transaction.amount_total,
            buyer_fingerprint: fingerprintHex,
            is_active: true
          });

        if (licenseRecordError) {
          throw new Error('Failed to create license record');
        }

        // Create seller earnings record
        const { error: earningsError } = await supabase
          .from('seller_earnings')
          .insert({
            seller_id: transaction.seller_id,
            transaction_id: transaction.id,
            amount: transaction.seller_amount,
            currency: transaction.currency,
            status: 'available'
          });

        if (earningsError) {
          throw new Error('Failed to create earnings record');
        }

        // Update transaction status
        const { error: updateError } = await supabase
          .from('marketplace_transactions')
          .update({
            status: 'completed',
            updated_at: new Date().toISOString()
          })
          .eq('id', transaction.id);

        if (updateError) {
          throw new Error('Failed to update transaction status');
        }

        // Update agent sales count
        const { error: salesError } = await supabase
          .from('agents')
          .update({
            sales_count: (originalAgent.sales_count || 0) + 1,
            updated_at: new Date().toISOString()
          })
          .eq('id', transaction.agent_id);

        if (salesError) {
          console.error('Failed to update sales count:', salesError);
        }

        return new Response(JSON.stringify({
          success: true,
          message: 'Payment confirmed and agent purchased successfully',
          purchased_agent_id: newAgent.id,
          agent_name: agentName
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      case 'get-purchased-agents': {
        console.log(`Getting licensed agents for user ${user.id}`);

        // SECURITY: Get user's agent licenses instead of copied agents
        const { data: agentLicenses, error } = await supabase
          .from('agent_licenses')
          .select(`
            *,
            agent:agent_id (
              id,
              name,
              description,
              tags,
              created_at,
              is_encrypted,
              security_level
            )
          `)
          .eq('buyer_id', user.id)
          .eq('is_active', true)
          .order('created_at', { ascending: false });

        if (error) {
          throw new Error(`Failed to get agent licenses: ${error.message}`);
        }

        // Transform licenses to match expected format for frontend compatibility
        const transformedLicenses = (agentLicenses || []).map(license => ({
          id: license.id,
          license_key: license.license_key,
          custom_name: license.custom_name,
          purchase_price: license.purchase_price,
          usage_count: license.usage_count,
          max_usage_limit: license.max_usage_limit,
          license_expires_at: license.license_expires_at,
          created_at: license.created_at,
          agent: {
            id: license.agent.id,
            name: license.agent.name,
            description: license.agent.description,
            tags: license.agent.tags,
            created_at: license.agent.created_at,
            is_encrypted: license.agent.is_encrypted,
            security_level: license.agent.security_level
          }
        }));

        return new Response(JSON.stringify({
          success: true,
          purchased_agents: transformedLicenses
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      case 'get-seller-earnings': {
        console.log(`Getting seller earnings for user ${user.id}`);

        const { data: earnings, error } = await supabase
          .from('seller_earnings')
          .select(`
            *,
            transaction:transaction_id (
              agent_id,
              buyer_id,
              amount_total,
              created_at
            )
          `)
          .eq('seller_id', user.id)
          .order('created_at', { ascending: false });

        if (error) {
          throw new Error(`Failed to get seller earnings: ${error.message}`);
        }

        // Calculate totals
        const totalEarnings = (earnings || []).reduce((sum, earning) => sum + parseFloat(earning.amount), 0);
        const availableEarnings = (earnings || [])
          .filter(earning => earning.status === 'available')
          .reduce((sum, earning) => sum + parseFloat(earning.amount), 0);
        const paidOutEarnings = (earnings || [])
          .filter(earning => earning.status === 'paid_out')
          .reduce((sum, earning) => sum + parseFloat(earning.amount), 0);

        return new Response(JSON.stringify({
          success: true,
          earnings: earnings || [],
          summary: {
            total_earnings: totalEarnings,
            available_earnings: availableEarnings,
            paid_out_earnings: paidOutEarnings,
            total_sales: (earnings || []).length
          }
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }

      default:
        throw new Error('Invalid action');
    }

  } catch (error) {
    console.error('Marketplace purchase error:', error);
    return new Response(JSON.stringify({
      success: false,
      error: error.message
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});

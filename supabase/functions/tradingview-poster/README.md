# TradingView Poster Edge Function

This Supabase Edge Function automatically selects a stock from a list of 20 most active stocks based on the time of day, analyzes it using Aura's technical analysis, gathers real-time market information, generates human-like sentiment messages with Gemini AI, and posts them to TradingView.

## How It Works

1. **Stock Selection**: The function selects one of 20 most active stocks based on the current time, rotating through the stocks every 21 minutes during market hours (9am-4pm) without duplicates. It uses a combination of time-based factors to ensure variety even when called at irregular intervals.

2. **Technical Analysis**: The selected stock is analyzed using Aura's public test endpoint. The function compares bullish and bearish scores to determine a definitive signal (always either bullish or bearish, never neutral).

3. **Real-time Information Gathering**: Using Google Search grounding, the function gathers current news, recent developments, earnings reports, analyst upgrades/downgrades, and significant events related to the selected stock from the past 24-48 hours.

4. **Message Generation**: Based on both Aura's technical analysis and real-time information, the function uses Gemini AI with enhanced prompting to generate natural, human-sounding messages:
   - BULLISH: Messages about buying calls or going long, incorporating current positive developments
   - BEARISH: Messages about shorting or buying puts, referencing current negative factors

   Note: The function never generates neutral messages. It compares bullish and bearish scores and always chooses one of these two signals.

5. **Osis.co Mention Strategy**: The function implements a 50% randomization system for mentioning Osis.co:
   - 50% of posts naturally integrate Osis.co as the analysis source
   - 50% of posts present the analysis as the trader's own insights
   - This creates more natural variation and avoids repetitive promotional content

6. **TradingView Posting**: The generated message is posted to TradingView's API for the specific stock.

## Enhanced AI Features

- **Google Search Grounding**: Real-time information gathering about stocks, news, and market developments
- **Advanced Prompt Engineering**: Sophisticated prompts that generate human-like, varied commentary
- **Multiple Writing Styles**: Analytical, casual, urgent, and confident tones based on market context
- **50% Osis.co Randomization**: Natural variation in brand mentions to avoid repetitive content
- **Current Event Integration**: Commentary incorporates recent news, earnings, and analyst actions
- **Enhanced Generation Config**: Higher temperature (0.8) and expanded token limits for more creative responses

## Core Features

- **Time-based Stock Selection**: Ensures variety and prevents duplicates during market hours
- **Definitive Signals**: Always generates either bullish or bearish signals, never neutral
- **Human-like Commentary**: Advanced AI generates authentic, varied trading insights
- **Exchange Compatibility**: Properly formats symbols for different exchanges
- **Special Symbol Handling**: Handles ETF-to-Index conversions (VOO→SPX, QQQ→NDX)
- **Error Handling**: Comprehensive error handling and logging
- **Manual Override**: Supports manual stock selection via query parameter

## Environment Variables

The function requires the following environment variables:

- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key
- `SUPABASE_ANON_KEY`: Your Supabase anon/public key (needed for Aura API authentication)
- `GEMINI_API_KEY`: Your Google Gemini API key

Note: The TradingView cookie is hard-coded in the function for authentication.

## Deployment

To deploy this function, run:

```bash
supabase functions deploy tradingview-poster
```

Or use the deployment script:

```bash
node scripts/deploy-tradingview-poster.js
```

## Scheduling

This function can be triggered manually by making a POST request to the function URL, or it can be scheduled to run automatically using a cron job or a service like GitHub Actions.

## Manual Stock Selection

You can manually select a specific stock by adding a `stockIndex` query parameter to the URL:

```
https://your-supabase-url/functions/v1/tradingview-poster?stockIndex=5
```

This will use the stock at index 5 in the list (in this case, Amazon) regardless of the current time.

## Example Output

### With Osis.co Mention (50% probability)
```json
{
  "success": true,
  "stock": {
    "symbol": "AAPL",
    "name": "Apple",
    "exchange": "NASDAQ"
  },
  "message": "Just ran Apple through Osis.co and the signals are looking bullish - picking up some calls based on this setup and the strong earnings beat yesterday.",
  "tradingViewResult": {
    "id": "12345678",
    "status": "success"
  }
}
```

### Without Osis.co Mention (50% probability)
```json
{
  "success": true,
  "stock": {
    "symbol": "TSLA",
    "name": "Tesla",
    "exchange": "NASDAQ"
  },
  "message": "My analysis on Tesla is showing bearish signals, especially with the recent production concerns from Shanghai - considering puts here.",
  "tradingViewResult": {
    "id": "12345679",
    "status": "success"
  }
}
```

## Special Cases

The function handles special cases for certain symbols:

- For S&P 500 analysis, it analyzes VOO but posts to TradingView as SPX
- For Nasdaq 100 analysis, it analyzes QQQ but posts to TradingView as NDX
- Different exchanges (NASDAQ, NYSE, CRYPTO, INDEX) are properly formatted for TradingView

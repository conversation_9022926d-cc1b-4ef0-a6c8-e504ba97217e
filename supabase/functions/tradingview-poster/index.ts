// TradingView Poster Edge Function
// This function selects a Magnificent 7 stock, analyzes it with Aura,
// generates a sentiment message with Gemini, and posts it to TradingView

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': '*',
  'Access-Control-Allow-Methods': '*'
};

// Initialize Supabase client with admin privileges
const supabaseUrl = Deno.env.get('SUPABASE_URL') || ''
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') || ''
const supabaseAnonKey = Deno.env.get('SUPABASE_ANON_KEY') || ''
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// Gemini API key
const GEMINI_API_KEY = Deno.env.get('GEMINI_API_KEY') || '';

// Define the list of 20 most active stocks to analyze and post about
const STOCK_LIST = [
  { symbol: "AAPL", name: "Apple", exchange: "NASDAQ" },
  { symbol: "TSLA", name: "Tesla", exchange: "NASDAQ" },
  { symbol: "NVDA", name: "NVIDIA", exchange: "NASDAQ" },
  { symbol: "AMD", name: "AMD", exchange: "NASDAQ" },
  { symbol: "META", name: "Meta", exchange: "NASDAQ" },
  { symbol: "AMZN", name: "Amazon", exchange: "NASDAQ" },
  { symbol: "MSFT", name: "Microsoft", exchange: "NASDAQ" },
  { symbol: "GOOG", name: "Google", exchange: "NASDAQ" },
  { symbol: "VOO", name: "S&P 500", exchange: "NYSE", postAs: { symbol: "SPX", name: "S&P 500", exchange: "INDEX" } },
  { symbol: "QQQ", name: "Nasdaq 100", exchange: "NASDAQ", postAs: { symbol: "NDX", name: "Nasdaq 100", exchange: "INDEX" } },
  { symbol: "SMCI", name: "Super Micro Computer", exchange: "NASDAQ" },
  { symbol: "PLTR", name: "Palantir", exchange: "NYSE" },
  { symbol: "COIN", name: "Coinbase", exchange: "NASDAQ" },
  { symbol: "MSTR", name: "MicroStrategy", exchange: "NASDAQ" },
  { symbol: "SOFI", name: "SoFi", exchange: "NASDAQ" },
  { symbol: "GME", name: "Gamestop", exchange: "NYSE" },
  { symbol: "NFLX", name: "Netflix", exchange: "NASDAQ" },
  { symbol: "BTC:USD", name: "Bitcoin", exchange: "CRYPTO" },
  { symbol: "RIOT", name: "Riot", exchange: "NASDAQ" },
  { symbol: "MARA", name: "Marathon Digital", exchange: "NASDAQ" }
];

// Function to select a stock based on time
function selectStockByTime(manualOverrideIndex?: number): { symbol: string, name: string, exchange: string, postAs?: { symbol: string, name: string, exchange: string } } {
  // If a manual override index is provided, use it
  if (manualOverrideIndex !== undefined && manualOverrideIndex >= 0 && manualOverrideIndex < STOCK_LIST.length) {
    console.log(`Using manual override index: ${manualOverrideIndex}`);
    return STOCK_LIST[manualOverrideIndex];
  }

  const now = new Date();

  // Calculate minutes since 9am (market open)
  const currentHour = now.getHours();
  const currentMinute = now.getMinutes();

  // Check if we're within market hours (9am to 4pm)
  if (currentHour < 9 || currentHour >= 16) {
    console.log(`Current time: ${currentHour}:${currentMinute} - Outside market hours (9am-4pm). Using default stock.`);
    return STOCK_LIST[0]; // Use default stock outside market hours
  }

  // Calculate minutes since 9am
  const minutesSince9am = ((currentHour - 9) * 60) + currentMinute;

  // Use exactly 21 minutes per stock to match the requested interval
  const cycleMinutes = 21;

  // Calculate which stock to use based on the current time
  // This will ensure that running every 21 minutes will always select a different stock
  const stockIndex = Math.floor(minutesSince9am / cycleMinutes) % STOCK_LIST.length;

  // Add a secondary time-based factor to ensure variety
  // Use the current minute as an additional offset if the same stock is selected multiple times
  const currentMinuteOffset = currentMinute % STOCK_LIST.length;
  const finalIndex = (stockIndex + currentMinuteOffset) % STOCK_LIST.length;

  console.log(`Current time: ${currentHour}:${currentMinute}, Minutes since 9am: ${minutesSince9am}`);
  console.log(`Base stock index: ${stockIndex}, Minute offset: ${currentMinuteOffset}, Final index: ${finalIndex}`);

  return STOCK_LIST[finalIndex];
}

// Function to call Aura's public test endpoint
async function analyzeStockWithAura(symbol: string): Promise<any> {
  try {
    const currentDate = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format

    // Prepare the request body for Aura
    const requestBody = {
      action: "public_test",
      tickers: [symbol],
      start_date: new Date(new Date().setDate(new Date().getDate() - 365)).toISOString().split('T')[0], // Use date from a year ago
      end_date: currentDate,
      timeframe: "day"
    };

    // Call the Aura API
    const response = await fetch(`${supabaseUrl}/functions/v1/aura`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseAnonKey}`
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Aura API error: ${response.status} - ${errorText}`);
      throw new Error(`Aura API error: ${response.status}`);
    }

    const data = await response.json();
    console.log(`Aura API response for ${symbol}:`, JSON.stringify(data, null, 2));

    // Validate Aura response structure
    if (!data.success || !data.data || !data.data.results || !Array.isArray(data.data.results)) {
      console.error('Invalid Aura response structure:', data);
      throw new Error('Invalid response structure from Aura API');
    }

    return data;
  } catch (error) {
    console.error("Error analyzing stock with Aura:", error);
    throw error;
  }
}

// Function to determine if Osis.co should be mentioned (50% probability)
function shouldMentionOsis(): boolean {
  return Math.random() < 0.5;
}

// Function to generate a fallback message when Gemini fails or produces incomplete responses
function generateFallbackMessage(stock: {
  symbol: string,
  name: string,
  exchange: string,
  postAs?: { symbol: string, name: string, exchange: string }
}, signal: string, mentionOsis: boolean): string {
  const displaySymbol = stock.postAs?.symbol || stock.symbol;
  const displayName = stock.postAs?.name || stock.name;

  const fallbackTemplates = mentionOsis ? [
    `Osis.co analysis shows ${signal.toLowerCase()} signals on ${displayName} - ${signal === 'BULLISH' ? 'looking at calls' : 'considering puts'} here.`,
    `${displayName} showing ${signal.toLowerCase()} momentum per Osis.co analysis - ${signal === 'BULLISH' ? 'going long' : 'might short'} this setup.`,
    `Osis.co signals ${signal.toLowerCase()} on ${displayName} - ${signal === 'BULLISH' ? 'calls look attractive' : 'puts in play'}.`,
    `Osis.co technical analysis on ${displayName} pointing ${signal.toLowerCase()} - ${signal === 'BULLISH' ? 'bullish setup here' : 'bearish pressure building'}.`,
    `${displayName} ${signal.toLowerCase()} signals from Osis.co - ${signal === 'BULLISH' ? 'calls could work' : 'puts might be the move'}.`
  ] : [
    `${displayName} technical setup looks ${signal.toLowerCase()} - ${signal === 'BULLISH' ? 'calls might be interesting' : 'puts could be the play'}.`,
    `${displayName} charts showing ${signal.toLowerCase()} momentum - ${signal === 'BULLISH' ? 'going long here' : 'looking at shorts'}.`,
    `Technical analysis on ${displayName} pointing ${signal.toLowerCase()} - ${signal === 'BULLISH' ? 'bullish on this one' : 'bearish outlook developing'}.`,
    `${displayName} showing ${signal.toLowerCase()} signals - ${signal === 'BULLISH' ? 'calls look good' : 'puts in play'}.`,
    `Been watching ${displayName} and the ${signal.toLowerCase()} setup is building - ${signal === 'BULLISH' ? 'upside potential here' : 'downside risk increasing'}.`
  ];

  // Select a random template
  const randomIndex = Math.floor(Math.random() * fallbackTemplates.length);
  return fallbackTemplates[randomIndex];
}

// Function to generate a sentiment message with Gemini
async function generateSentimentMessage(stock: {
  symbol: string,
  name: string,
  exchange: string,
  postAs?: { symbol: string, name: string, exchange: string }
}, auraResult: any): Promise<string> {
  // Use postAs values if available (for special cases like VOO/SPX and QQQ/NDX)
  const displaySymbol = stock.postAs?.symbol || stock.symbol;
  const displayName = stock.postAs?.name || stock.name;
  try {
    // Extract the signal and confidence from Aura's response
    const stockResult = auraResult.data.results.find((r: any) => r.symbol === stock.symbol);

    if (!stockResult) {
      throw new Error(`No analysis found for ${stock.symbol}`);
    }

    // Get the scores from the result - fix data structure access
    const bullishScore = stockResult.bullishScore || stockResult.scores?.bullish || 0;
    const bearishScore = stockResult.bearishScore || stockResult.scores?.bearish || 0;

    console.log(`Aura scores for ${stock.symbol}: bullish=${bullishScore}, bearish=${bearishScore}`);

    // Determine signal based on comparing bullish vs bearish scores
    // Never use NEUTRAL - if bullish > bearish, use BULLISH, otherwise BEARISH
    let signal = "BULLISH";
    if (bearishScore > bullishScore) {
      signal = "BEARISH";
    }

    // Use the original confidence if available, or calculate a simple one
    const confidence = stockResult.confidence || Math.max(bullishScore, bearishScore);

    // Determine if we should mention Osis.co (50% probability)
    const mentionOsis = shouldMentionOsis();
    console.log(`Osis.co mention decision: ${mentionOsis ? 'YES' : 'NO'}`);

    // Create direct prompt that forces immediate trading comment without meta-commentary
    const prompt = `Write a direct trading comment for ${displayName} ($${displaySymbol}). Signal: ${signal}.

${mentionOsis ? `Start with: "Osis.co analysis shows ${signal.toLowerCase()} signals on ${displayName}"` : `Start with: "${displayName} technical setup looks ${signal.toLowerCase()}"`}

Add recent market context and ${signal === 'BULLISH' ? 'mention calls or going long' : 'mention puts or shorting'}. 1-2 sentences only. No introduction, no "here's a comment", just the direct trading insight.`;

    // Call Gemini API with Google Search grounding enabled
    const response = await fetch('https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-goog-api-key': GEMINI_API_KEY,
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              { text: prompt }
            ]
          }
        ],
        tools: [{ googleSearch: {} }], // Enable Google Search grounding
        generationConfig: {
          temperature: 0.7, // Slightly reduced for more consistent outputs
          maxOutputTokens: 300, // Increased to prevent truncation
          topK: 40,
          topP: 0.9 // Slightly reduced for more focused responses
        }
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Gemini API error: ${response.status} - ${errorText}`);
      throw new Error(`Gemini API error: ${response.status}`);
    }

    const data = await response.json();
    console.log('Gemini API response:', JSON.stringify(data, null, 2));

    // Validate response structure
    if (!data.candidates || !data.candidates[0] || !data.candidates[0].content || !data.candidates[0].content.parts || !data.candidates[0].content.parts[0]) {
      console.error('Invalid Gemini response structure:', data);
      throw new Error('Invalid response structure from Gemini API');
    }

    let message = data.candidates[0].content.parts[0].text.trim();

    // Validate message quality and completeness
    if (!message || message.length < 10) {
      console.error('Generated message too short or empty:', message);
      throw new Error('Generated message is too short or empty');
    }

    // Check for incomplete responses (common patterns)
    const incompletePatterns = [
      /^(Okay|Alright|Sure),?\s*I'm\s*(ready\s*to|going\s*to)/i,
      /^(Okay|Alright|Sure),?\s*here'?s?\s*(a|an|the)/i, // "Okay, here's a trader's comment..."
      /^Let me\s*(create|craft|generate)/i,
      /^I'll\s*(create|craft|generate)/i,
      /^Here'?s?\s*(a|an|the)\s*(trader'?s?\s*)?(comment|analysis|insight)/i, // "Here's a trader's comment..."
      /based on your requirements:?\s*$/i, // Ends with "based on your requirements:"
      /\[.*\]$/, // Ends with placeholder brackets
      /\.\.\.$/, // Ends with ellipsis
      /\(\s*\)/, // Contains empty parentheses like "Apple ( )"
    ];

    const isIncomplete = incompletePatterns.some(pattern => pattern.test(message));

    // Additional check: message should start with expected content, not meta-commentary
    const expectedStarts = mentionOsis
      ? [`Osis.co analysis`, `Osis.co`, `The Osis.co`]
      : [`${displayName}`, `My analysis`, `Technical`, `Looking at`, `Been watching`];

    const startsCorrectly = expectedStarts.some(start =>
      message.toLowerCase().startsWith(start.toLowerCase())
    );

    if (isIncomplete || !startsCorrectly) {
      console.error('Detected incomplete or meta-commentary response:', message);
      console.error('Expected to start with one of:', expectedStarts);
      // Generate a fallback message
      const fallbackMessage = generateFallbackMessage(stock, signal, mentionOsis);
      console.log('Using fallback message:', fallbackMessage);
      message = fallbackMessage;
    }

    console.log(`Final generated message for ${stock.symbol}: ${message}`);
    return message;
  } catch (error) {
    console.error("Error generating sentiment message:", error);
    throw error;
  }
}

// Function to post a message to TradingView
async function postToTradingView(stock: {
  symbol: string,
  name: string,
  exchange: string,
  postAs?: { symbol: string, name: string, exchange: string }
}, message: string): Promise<any> {
  try {
    // Use postAs values if available (for special cases like VOO/SPX and QQQ/NDX)
    const displaySymbol = stock.postAs?.symbol || stock.symbol;
    const displayExchange = stock.postAs?.exchange || stock.exchange;

    // Format the exchange prefix based on the exchange type
    let exchangePrefix = "";
    switch (displayExchange) {
      case "NASDAQ":
        exchangePrefix = "NASDAQ";
        break;
      case "NYSE":
        exchangePrefix = "NYSE";
        break;
      case "CRYPTO":
        exchangePrefix = "";
        break;
      case "INDEX":
        // For indices, we don't need an exchange prefix
        exchangePrefix = "";
        break;
      default:
        exchangePrefix = "NASDAQ"; // Default to NASDAQ if unknown
    }

    // Format the symbol with exchange prefix
    const formattedSymbol = displayExchange === "INDEX" ? displaySymbol : `${exchangePrefix}:${displaySymbol}`;

    // Prepare the request body for TradingView
    const requestBody = {
      text: message,
      symbols: {
        [displaySymbol]: formattedSymbol
      }
    };

    // Post to TradingView
    const response = await fetch('https://www.tradingview.com/api/v1/minds/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Origin': 'https://www.tradingview.com',
        'Referer': `https://www.tradingview.com/symbols/${formattedSymbol.replace(':', '-')}/minds/`,
        'Cookie': 'cookiePrivacyPreferenceBannerProduction=notApplicable; cookiesSettings={\"analytics\":true,\"advertising\":true}; g_state={\"i_l\":0}; device_t=RUVHS0F3OjA.dk5dwpuXCXoeIKpyR-S-JO9gP-ubUeNApS_NW3-OmQY; sessionid=h40d3yf6s0zn70g0c91c48hf9g435pkd; sessionid_sign=v3:slWXt3YtGsCd0ViqwT+svV4lIu+U17Im0w7fXtH9tZo=; cachec=undefined; etg=undefined; _sp_ses.cf1a=*; _dd_s=logs=1&id=79cdbaf8-1b4b-441a-b1cb-df65679050f9&created=1747189559225&expire=1747190553505; _sp_id.cf1a=0a4a39c4-f311-4041-ae67-b1bbd5354aed.1747027363.5.1747190173.1747164100.762fac86-83de-4c80-ac40-953d2a458ca9.f8ca6d80-5f3a-49e9-bf58-00b07319857e.3f329992-272a-4815-a0b0-86bbba14af6e.1747189299456.29'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      throw new Error(`TradingView API error: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error posting to TradingView:", error);
    throw error;
  }
}

// Main function to process the request
async function processRequest(stockIndex?: number): Promise<any> {
  try {
    // 1. Select a stock based on time of day or manual override
    const stock = selectStockByTime(stockIndex);
    console.log(`Selected stock: ${stock.name} (${stock.symbol})`);

    // 2. Analyze the stock with Aura
    const auraResult = await analyzeStockWithAura(stock.symbol);
    console.log(`Aura analysis complete for ${stock.symbol}:`, JSON.stringify(auraResult, null, 2));

    // 3. Generate a sentiment message with Gemini
    const message = await generateSentimentMessage(stock, auraResult);
    console.log(`Generated message for ${stock.symbol}: "${message}"`);

    // Final validation before posting - ensure no incomplete patterns made it through
    const finalIncompletePatterns = [
      /^(Okay|Alright|Sure)/i,
      /here'?s?\s*(a|an|the)/i,
      /based on.*requirements/i,
      /\(\s*\)/,
      /\[.*\]/,
      /\.\.\.$/
    ];

    const stillIncomplete = finalIncompletePatterns.some(pattern => pattern.test(message));
    if (!message || message.length < 10 || stillIncomplete) {
      console.error(`Final validation failed for message: "${message}"`);
      // Generate one more fallback as last resort - use simple fallback without external variables
      const emergencyFallback = `${stock.name} technical setup looks bullish - calls might be interesting.`;

      console.log(`Using emergency fallback: "${emergencyFallback}"`);
      message = emergencyFallback;
    }

    // 4. Post the message to TradingView
    const tradingViewResult = await postToTradingView(stock, message);
    console.log(`Posted to TradingView: ${JSON.stringify(tradingViewResult)}`);

    return {
      success: true,
      stock: stock,
      message: message,
      tradingViewResult: tradingViewResult
    };
  } catch (error) {
    console.error("Error processing request:", error);
    throw error;
  }
}

// Serve the edge function
serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: corsHeaders
    });
  }

  try {
    // Parse query parameters
    const url = new URL(req.url);
    const stockIndexParam = url.searchParams.get('stockIndex');
    let stockIndex: number | undefined = undefined;

    // If stockIndex is provided, parse it
    if (stockIndexParam) {
      stockIndex = parseInt(stockIndexParam, 10);
      if (isNaN(stockIndex) || stockIndex < 0 || stockIndex >= STOCK_LIST.length) {
        return new Response(JSON.stringify({
          error: `Invalid stockIndex. Must be between 0 and ${STOCK_LIST.length - 1}.`,
          validStocks: STOCK_LIST.map((stock, index) => `${index}: ${stock.symbol} (${stock.name})`)
        }), {
          status: 400,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }
        });
      }
    }

    // Process the request with optional stock index
    const result = await processRequest(stockIndex);

    // Return the result
    return new Response(JSON.stringify(result), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    // Handle errors
    console.error("Error in edge function:", error);

    return new Response(JSON.stringify({
      error: error.message || 'Unknown error',
      type: error.constructor.name
    }), {
      status: 500,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});

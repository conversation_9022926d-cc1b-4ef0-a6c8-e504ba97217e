import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    const { agentId, symbol, timeframe, interval, neutralAction = 'exit', isMarketplaceAgent = false, agentConfiguration } = await req.json();

    console.log(`Starting backtest for agent ${agentId}, symbol ${symbol}, timeframe ${timeframe}, interval ${interval}, neutralAction: ${neutralAction}, isMarketplaceAgent: ${isMarketplaceAgent}`);

    // Get environment variables
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
    const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
    const polygonApiKey = Deno.env.get('POLYGON_API_KEY')!;

    // Initialize Supabase client
    const supabase = createClient(supabaseUrl, supabaseServiceKey);

    let agent;

    if (isMarketplaceAgent && agentConfiguration) {
      // For marketplace agents, use the provided configuration directly
      agent = {
        id: agentId,
        configuration: agentConfiguration
      };
      console.log('Using marketplace agent configuration directly');
    } else {
      // For regular agents, fetch from database
      const { data: agentData, error: agentError } = await supabase
        .from('agents')
        .select('*')
        .eq('id', agentId)
        .single();

      if (agentError || !agentData) {
        throw new Error(`Agent not found: ${agentError?.message}`);
      }

      console.log('Agent fetched from database:', JSON.stringify(agentData, null, 2));
      console.log('Agent configuration blocks:', agentData.configuration?.blocks?.length || 'NO BLOCKS');

      agent = agentData;
    }

    // Fetch historical data for the timeframe
    console.log(`Fetching historical data for ${symbol} over ${timeframe}`);

    // Calculate date range based on timeframe
    const endDate = new Date();
    let startDate = new Date();

    if (timeframe === '3M') {
      startDate.setMonth(endDate.getMonth() - 3);
    } else if (timeframe === '6M') {
      startDate.setMonth(endDate.getMonth() - 6);
    } else if (timeframe === '1Y') {
      startDate.setFullYear(endDate.getFullYear() - 1);
    } else if (timeframe === '2Y') {
      startDate.setFullYear(endDate.getFullYear() - 2);
    } else if (timeframe === '5Y') {
      startDate.setFullYear(endDate.getFullYear() - 5);
    } else {
      throw new Error(`Unsupported timeframe: ${timeframe}`);
    }

    // Fetch historical price data from Polygon
    const startDateStr = startDate.toISOString().split('T')[0];
    const endDateStr = endDate.toISOString().split('T')[0];

    const historicalUrl = `https://api.polygon.io/v2/aggs/ticker/${symbol}/range/1/day/${startDateStr}/${endDateStr}?apiKey=${polygonApiKey}&limit=5000`;

    console.log(`Fetching historical data from: ${startDateStr} to ${endDateStr}`);

    const historicalResponse = await fetch(historicalUrl);
    if (!historicalResponse.ok) {
      throw new Error(`Failed to fetch historical data: ${historicalResponse.statusText}`);
    }

    const historicalData = await historicalResponse.json();
    if (!historicalData.results || historicalData.results.length === 0) {
      throw new Error(`No historical data available for ${symbol}`);
    }

    console.log(`Received ${historicalData.results.length} historical data points`);

    // Convert interval to step size
    let stepSize = 1; // Default to daily
    if (interval === '1W') stepSize = 7;
    else if (interval === '1M') stepSize = 30;

    // Initialize trading variables
    let balance = 10000; // Starting balance
    let position = null;
    const trades = [];
    const performanceChart = []; // Track portfolio value over time

    console.log(`Starting backtest with ${historicalData.results.length} data points, step size: ${stepSize}`);

    // Add initial performance chart data point
    const firstDataPoint = historicalData.results[0];
    const firstDate = new Date(firstDataPoint.t).toISOString().split('T')[0];
    performanceChart.push({
      date: firstDate,
      agentValue: 10000, // Starting balance
      buyHoldValue: 10000 // Starting buy and hold value
    });

    // Import the agent execution functions directly
    const { fetchPolygonData } = await import("../agent-runner/polygon-api.ts");
    const { executeAgent } = await import("../agent-runner/agent-executor.ts");

    // Run the agent for each historical day
    for (let i = 0; i < historicalData.results.length; i += stepSize) {
      const dataPoint = historicalData.results[i];
      const currentDate = new Date(dataPoint.t).toISOString().split('T')[0];
      const currentPrice = dataPoint.c;

      console.log(`\n=== BACKTEST DAY ${Math.floor(i / stepSize)} ===`);
      console.log(`Date: ${currentDate}, Price: $${currentPrice}`);

      try {
        // Run the actual agent by calling the same functions the agent runner uses
        console.log(`Running agent for ${symbol} on ${currentDate}`);

        // For backtesting, we need to fetch RSI data as it would have been available on this historical day
        // We'll call the Polygon RSI API with a date range ending on this historical day
        const { fetchRsiData } = await import("../agent-runner/polygon-api.ts");

        console.log(`Fetching historical RSI data for ${symbol} as of ${currentDate}`);

        // Fetch RSI data as it would have been available on this historical day
        // We need to get RSI data up to this point in time, not current RSI
        const historicalRsiUrl = `https://api.polygon.io/v1/indicators/rsi/${symbol}?timespan=day&adjusted=true&window=14&series_type=close&order=desc&limit=100&timestamp.lte=${dataPoint.t}&apiKey=${polygonApiKey}`;

        let rsiData;
        try {
          console.log(`Fetching RSI data as of ${currentDate} (timestamp: ${dataPoint.t})`);
          const rsiResponse = await fetch(historicalRsiUrl);

          if (!rsiResponse.ok) {
            throw new Error(`RSI API error: ${rsiResponse.status} ${rsiResponse.statusText}`);
          }

          const rsiResult = await rsiResponse.json();

          if (!rsiResult.results || !rsiResult.results.values || rsiResult.results.values.length === 0) {
            console.warn(`No RSI data available for ${symbol} as of ${currentDate}`);
            continue; // Skip this day if no RSI data
          }

          // Extract RSI values and reverse to get chronological order
          rsiData = rsiResult.results.values.map((item: any) => {
            const value = item.value;
            if (typeof value === 'number') {
              return value;
            } else {
              const numericValue = Number(value);
              if (isNaN(numericValue)) {
                throw new Error(`Invalid RSI value: ${value}`);
              }
              return numericValue;
            }
          }).reverse();

          console.log(`Historical RSI data fetched: ${rsiData.length} points, latest: ${rsiData[rsiData.length - 1]}`);

        } catch (error) {
          console.error(`Error fetching historical RSI for ${currentDate}:`, error);
          continue; // Skip this day if RSI fetch fails
        }

        // Create historical data window for pattern detection (last 10 days including current)
        const windowSize = 10;
        const startIndex = Math.max(0, i - windowSize + 1);
        const historicalWindow = historicalData.results.slice(startIndex, i + 1);

        console.log(`Creating historical window: ${historicalWindow.length} candles for pattern detection (index ${startIndex} to ${i})`);

        // Check if agent has candle pattern blocks and skip if insufficient data
        const hasCandlePatternBlocks = agent.configuration.blocks.some(block => block.type === 'CANDLE_PATTERN');
        if (hasCandlePatternBlocks && historicalWindow.length < 3) {
          console.log(`Skipping agent execution for ${currentDate}: Only ${historicalWindow.length} candles available, need at least 3 for pattern detection`);
          continue;
        }

        // Create polygon data structure with rolling historical window
        const polygonData = {
          symbol: symbol,
          price: {
            current: currentPrice,
            open: dataPoint.o,
            high: dataPoint.h,
            low: dataPoint.l,
            close: currentPrice,
            volume: dataPoint.v,
            timestamp: dataPoint.t
          },
          historical: {
            open: historicalWindow.map(d => d.o),
            high: historicalWindow.map(d => d.h),
            low: historicalWindow.map(d => d.l),
            close: historicalWindow.map(d => d.c),
            volume: historicalWindow.map(d => d.v),
            timestamp: historicalWindow.map(d => d.t)
          },
          indicators: {
            rsi: rsiData
          },
          fundamentals: {}
        };

        // Execute the agent with the fetched data
        const agentResult = await executeAgent(agent.configuration, polygonData);

        if (!agentResult) {
          console.error(`Agent execution failed for ${currentDate}: No result returned`);
          continue;
        }
        console.log(`Agent result: ${agentResult.signal} (${agentResult.confidence}%)`);

        // Check if this is an enhanced signal output
        const isEnhancedSignal = agentResult.risk_management || agentResult.advanced_analysis || agentResult.signal_quality;
        if (isEnhancedSignal) {
          console.log(`Enhanced signal detected with risk management guidance`);
        }

        // Calculate current portfolio value (cash + position value)
        let currentPortfolioValue = balance;
        if (position) {
          currentPortfolioValue += position.shares * currentPrice;
        }

        // Add performance chart data point
        performanceChart.push({
          date: currentDate,
          agentValue: currentPortfolioValue,
          buyHoldValue: 10000 * (currentPrice / historicalData.results[0].c) // Buy and hold comparison
        });

        // Process trading signals with enhanced risk management
        if (agentResult.signal === 'bullish' && !position) {
          // Buy signal - enter new position with enhanced position sizing if available
          let positionSize = balance; // Default: use all available balance

          // Use enhanced position sizing if available
          if (isEnhancedSignal && agentResult.risk_management?.position_size_suggestion) {
            const suggestedPercentage = agentResult.risk_management.position_size_suggestion.percentage_of_portfolio;
            positionSize = balance * (suggestedPercentage / 100);
            console.log(`Using enhanced position sizing: ${suggestedPercentage}% of portfolio ($${positionSize.toFixed(2)})`);
          }

          const shares = Math.floor(positionSize / currentPrice);
          if (shares > 0) {
            // Calculate stop loss and take profit levels if available
            let stopLossPrice = null;
            let takeProfitPrice = null;

            if (isEnhancedSignal && agentResult.risk_management) {
              stopLossPrice = agentResult.risk_management.stop_loss?.price;
              takeProfitPrice = agentResult.risk_management.take_profit?.targets?.[0]?.price;
            }

            position = {
              type: 'long',
              shares,
              entryPrice: currentPrice,
              entryDate: currentDate,
              stopLossPrice,
              takeProfitPrice,
              riskManagement: isEnhancedSignal ? agentResult.risk_management : null
            };
            balance -= shares * currentPrice;
            console.log(`TRADE: BUY ${shares} shares at $${currentPrice} (${agentResult.confidence}% confidence)`);
            if (stopLossPrice) console.log(`  Stop Loss: $${stopLossPrice.toFixed(2)}`);
            if (takeProfitPrice) console.log(`  Take Profit: $${takeProfitPrice.toFixed(2)}`);

            trades.push({
              type: 'buy',
              date: currentDate,
              price: currentPrice,
              shares: shares,
              signal: agentResult.signal,
              confidence: agentResult.confidence,
              stopLossPrice,
              takeProfitPrice,
              enhancedSignal: isEnhancedSignal
            });
          }
        } else if (position && position.type === 'long') {
          // Check for stop loss or take profit triggers first
          let shouldSell = false;
          let sellReason = 'signal';

          if (position.stopLossPrice && currentPrice <= position.stopLossPrice) {
            shouldSell = true;
            sellReason = 'stop_loss';
            console.log(`STOP LOSS TRIGGERED: Price $${currentPrice} <= Stop Loss $${position.stopLossPrice}`);
          } else if (position.takeProfitPrice && currentPrice >= position.takeProfitPrice) {
            shouldSell = true;
            sellReason = 'take_profit';
            console.log(`TAKE PROFIT TRIGGERED: Price $${currentPrice} >= Take Profit $${position.takeProfitPrice}`);
          } else if (agentResult.signal === 'bearish') {
            shouldSell = true;
            sellReason = 'bearish_signal';
            console.log(`BEARISH SIGNAL: Selling position`);
          }

          if (shouldSell) {
            // Sell position
            const profit = (currentPrice - position.entryPrice) * position.shares;
            balance += position.shares * currentPrice;

            console.log(`TRADE: SELL ${position.shares} shares at $${currentPrice} - Profit: $${profit.toFixed(2)} (Reason: ${sellReason})`);

            trades.push({
              type: 'sell',
              date: currentDate,
              price: currentPrice,
              shares: position.shares,
              profit: profit,
              signal: agentResult.signal,
              confidence: agentResult.confidence,
              sellReason: sellReason,
              enhancedSignal: isEnhancedSignal
            });

            position = null;
          }
        } else if (agentResult.signal === 'neutral' && position) {
          // NEUTRAL signal handling based on neutralAction setting
          if (neutralAction === 'exit') {
            // EXIT strategy: close any open position regardless of confidence
            const profit = (currentPrice - position.entryPrice) * position.shares;
            balance += position.shares * currentPrice;

            console.log(`TRADE: CLOSE ${position.type} position due to NEUTRAL signal (exit strategy) - ${position.shares} shares at $${currentPrice} - Profit: $${profit.toFixed(2)}`);

            trades.push({
              type: 'sell',
              date: currentDate,
              price: currentPrice,
              shares: position.shares,
              profit: profit,
              signal: agentResult.signal,
              confidence: agentResult.confidence,
              reason: 'neutral_exit'
            });

            position = null;
          } else if (neutralAction === 'hold') {
            // HOLD strategy: keep current position, no action taken
            console.log(`NEUTRAL signal (hold strategy) - maintaining ${position.type} position of ${position.shares} shares`);
          }
        }

      } catch (error) {
        console.error(`Error running agent for ${currentDate}:`, error);
        // Continue with next day
      }
    }

    // Close any remaining position
    if (position) {
      const lastDataPoint = historicalData.results[historicalData.results.length - 1];
      const lastPrice = lastDataPoint.c;
      const lastDate = new Date(lastDataPoint.t).toISOString().split('T')[0];

      if (position.type === 'long') {
        const profit = (lastPrice - position.entryPrice) * position.shares;
        balance += position.shares * lastPrice;

        trades.push({
          type: 'sell',
          date: lastDate,
          price: lastPrice,
          shares: position.shares,
          profit: profit,
          signal: 'close',
          confidence: 100
        });

        console.log(`FINAL: Closed position at $${lastPrice} - Profit: $${profit.toFixed(2)}`);
      }

      // Add final performance chart data point
      performanceChart.push({
        date: lastDate,
        agentValue: balance,
        buyHoldValue: 10000 * (lastPrice / historicalData.results[0].c)
      });
    }

    // Calculate performance metrics
    const totalReturn = balance - 10000;
    const totalReturnPercent = (totalReturn / 10000) * 100;
    const totalTrades = trades.filter(t => t.type === 'sell').length;
    const winningTrades = trades.filter(t => t.type === 'sell' && t.profit > 0).length;
    const winRate = totalTrades > 0 ? (winningTrades / totalTrades) * 100 : 0;

    // Calculate buy and hold return for comparison
    const firstPrice = historicalData.results[0].c;
    const lastPrice = historicalData.results[historicalData.results.length - 1].c;
    const buyAndHoldReturn = ((lastPrice - firstPrice) / firstPrice) * 100;

    // Calculate max drawdown (simplified)
    let maxDrawdown = 0;
    let peak = 10000;
    let currentBalance = 10000;

    for (const trade of trades) {
      if (trade.type === 'sell' && trade.profit) {
        currentBalance += trade.profit;
        if (currentBalance > peak) {
          peak = currentBalance;
        }
        const drawdown = ((peak - currentBalance) / peak) * 100;
        if (drawdown > maxDrawdown) {
          maxDrawdown = drawdown;
        }
      }
    }

    // Calculate Sharpe ratio (simplified - using 0% risk-free rate)
    const returns = trades.filter(t => t.profit).map(t => (t.profit / 10000) * 100);
    const avgReturn = returns.length > 0 ? returns.reduce((a, b) => a + b, 0) / returns.length : 0;
    const returnStdDev = returns.length > 1 ? Math.sqrt(returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / (returns.length - 1)) : 0;
    const sharpeRatio = returnStdDev > 0 ? avgReturn / returnStdDev : 0;

    const results = {
      success: true,
      startDate: startDateStr,
      endDate: endDateStr,
      totalReturn: totalReturnPercent,
      buyAndHoldReturn: buyAndHoldReturn,
      numberOfTrades: totalTrades,
      winRate: winRate,
      maxDrawdown: maxDrawdown,
      sharpeRatio: sharpeRatio,
      trades: trades,
      performanceChart: performanceChart, // Add the performance chart data
      summary: {
        startingBalance: 10000,
        endingBalance: balance,
        totalReturn: totalReturn,
        totalReturnPercent: totalReturnPercent,
        totalTrades: totalTrades,
        winningTrades: winningTrades,
        winRate: winRate
      }
    };

    console.log(`\n=== BACKTEST COMPLETE ===`);
    console.log(`Total Return: $${totalReturn.toFixed(2)} (${totalReturnPercent.toFixed(2)}%)`);
    console.log(`Total Trades: ${totalTrades}, Win Rate: ${winRate.toFixed(1)}%`);

    return new Response(JSON.stringify(results), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    });

  } catch (error) {
    console.error('Backtest error:', error);
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});

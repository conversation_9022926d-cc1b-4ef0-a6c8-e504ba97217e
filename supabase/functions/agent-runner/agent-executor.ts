// Agent Executor
// Logic to execute trading agents

import {
  <PERSON><PERSON>onfig,
  AgentResult,
  EnhancedSignalOutput,
  BlockType,
  BlockUnion,
  PolygonData,
  RiskAnalysisBlock,
  AdvancedAnalysisBlock,
  SignalQualityBlock
} from "./agent-types.ts";
import { calculateRSI, calculateMACD, calculateVWAP, calculateStochastic, calculateWilliamsR, calculateCCI } from "./indicators.ts";
import { RiskAnalysisProcessor } from "./enhanced-block-processors.ts";
import { AdvancedAnalysisProcessor } from "./advanced-analysis-processors.ts";
import { SignalQualityProcessor } from "./signal-quality-processors.ts";

/**
 * Calculate support level from historical price data
 * @param polygonData - Data from Polygon API
 * @param parameters - Parameters for support calculation
 * @returns The support level
 */
function calculateSupportLevel(polygonData: PolygonData, parameters: any): number {
  const timeframe = parameters?.timeframe || 'month';
  const strength = parameters?.strength || 2;

  console.log(`Calculating support level with timeframe: ${timeframe}, strength: ${strength}`);

  // Get current price for validation
  const currentPrice = polygonData.price?.current || polygonData.price?.close || 0;
  console.log(`Current price: ${currentPrice}`);

  console.log(`Historical data available:`, {
    hasHistorical: !!polygonData.historical,
    hasLow: !!polygonData.historical?.low,
    lowLength: polygonData.historical?.low?.length || 0,
    sampleLows: polygonData.historical?.low?.slice(-5) || []
  });

  // Calculate lookback periods based on timeframe (assuming daily data)
  const timeframeLookbacks = {
    'week': 7,
    'month': 30,
    '3month': 90,
    '6month': 180,
    'year': 365
  };

  const lookback = timeframeLookbacks[timeframe] || 30;
  console.log(`Using ${lookback} days lookback for ${timeframe} timeframe`);

  if (!polygonData.historical || !polygonData.historical.low || polygonData.historical.low.length < Math.min(lookback, 10)) {
    throw new Error(`Insufficient historical data for support calculation. Need at least ${Math.min(lookback, 10)} data points, got ${polygonData.historical?.low?.length || 0}`);
  }

  // Use available data up to the lookback period
  const availableData = Math.min(lookback, polygonData.historical.low.length);
  const lows = polygonData.historical.low.slice(-availableData);
  console.log(`Using ${lows.length} low prices for ${timeframe} support calculation:`, lows.slice(0, 5), '...');

  const supportLevels: { level: number; touches: number }[] = [];

  // Find potential support levels (local minima)
  for (let i = 1; i < lows.length - 1; i++) {
    if (lows[i] <= lows[i - 1] && lows[i] <= lows[i + 1]) {
      const level = lows[i];

      // CRITICAL: Support must be below current price
      if (level >= currentPrice) {
        console.log(`Skipping potential support at ${level.toFixed(2)} - above current price ${currentPrice.toFixed(2)}`);
        continue;
      }

      // Count how many times price touched this level (within 1% tolerance)
      let touches = 0;
      const tolerance = level * 0.01; // 1% tolerance

      for (const low of lows) {
        if (Math.abs(low - level) <= tolerance) {
          touches++;
        }
      }

      console.log(`Potential support at ${level.toFixed(2)} with ${touches} touches (need ${strength}) - below current price ✓`);

      if (touches >= strength) {
        supportLevels.push({ level, touches });
      }
    }
  }

  console.log(`Found ${supportLevels.length} valid support levels below current price:`, supportLevels);

  // Return the strongest support level (most touches), or the lowest low if none found
  if (supportLevels.length > 0) {
    // Filter to only include levels below current price and get the strongest
    const validSupports = supportLevels.filter(s => s.level < currentPrice);

    if (validSupports.length > 0) {
      const strongestSupport = validSupports.reduce((prev, current) =>
        current.touches > prev.touches ? current : prev
      );
      console.log(`Selected strongest support level: ${strongestSupport.level} with ${strongestSupport.touches} touches`);
      return strongestSupport.level;
    }
  }

  // Fallback to the lowest low in the period, but ensure it's below current price
  const lowestLow = Math.min(...lows);
  if (lowestLow < currentPrice) {
    console.log(`No strong support found, using lowest low: ${lowestLow}`);
    return lowestLow;
  } else {
    // If even the lowest low is above current price, use 95% of current price as emergency fallback
    const emergencySupport = currentPrice * 0.95;
    console.log(`Even lowest low (${lowestLow}) is above current price, using emergency support: ${emergencySupport}`);
    return emergencySupport;
  }
}

/**
 * Calculate resistance level from historical price data
 * @param polygonData - Data from Polygon API
 * @param parameters - Parameters for resistance calculation
 * @returns The resistance level
 */
function calculateResistanceLevel(polygonData: PolygonData, parameters: any): number {
  const timeframe = parameters?.timeframe || 'month';
  const strength = parameters?.strength || 2;

  console.log(`Calculating resistance level with timeframe: ${timeframe}, strength: ${strength}`);

  // Get current price for validation
  const currentPrice = polygonData.price?.current || polygonData.price?.close || 0;
  console.log(`Current price: ${currentPrice}`);

  console.log(`Historical data available:`, {
    hasHistorical: !!polygonData.historical,
    hasHigh: !!polygonData.historical?.high,
    highLength: polygonData.historical?.high?.length || 0,
    sampleHighs: polygonData.historical?.high?.slice(-5) || []
  });

  // Calculate lookback periods based on timeframe (assuming daily data)
  const timeframeLookbacks = {
    'week': 7,
    'month': 30,
    '3month': 90,
    '6month': 180,
    'year': 365
  };

  const lookback = timeframeLookbacks[timeframe] || 30;
  console.log(`Using ${lookback} days lookback for ${timeframe} timeframe`);

  if (!polygonData.historical || !polygonData.historical.high || polygonData.historical.high.length < Math.min(lookback, 10)) {
    throw new Error(`Insufficient historical data for resistance calculation. Need at least ${Math.min(lookback, 10)} data points, got ${polygonData.historical?.high?.length || 0}`);
  }

  // Use available data up to the lookback period
  const availableData = Math.min(lookback, polygonData.historical.high.length);
  const highs = polygonData.historical.high.slice(-availableData);
  console.log(`Using ${highs.length} high prices for ${timeframe} resistance calculation:`, highs.slice(0, 5), '...');

  const resistanceLevels: { level: number; touches: number }[] = [];

  // Find potential resistance levels (local maxima)
  for (let i = 1; i < highs.length - 1; i++) {
    if (highs[i] >= highs[i - 1] && highs[i] >= highs[i + 1]) {
      const level = highs[i];

      // CRITICAL: Resistance must be above current price
      if (level <= currentPrice) {
        console.log(`Skipping potential resistance at ${level.toFixed(2)} - below current price ${currentPrice.toFixed(2)}`);
        continue;
      }

      // Count how many times price touched this level (within 1% tolerance)
      let touches = 0;
      const tolerance = level * 0.01; // 1% tolerance

      for (const high of highs) {
        if (Math.abs(high - level) <= tolerance) {
          touches++;
        }
      }

      console.log(`Potential resistance at ${level.toFixed(2)} with ${touches} touches (need ${strength}) - above current price ✓`);

      if (touches >= strength) {
        resistanceLevels.push({ level, touches });
      }
    }
  }

  console.log(`Found ${resistanceLevels.length} valid resistance levels above current price:`, resistanceLevels);

  // Return the strongest resistance level (most touches), or the highest high if none found
  if (resistanceLevels.length > 0) {
    // Filter to only include levels above current price and get the strongest
    const validResistances = resistanceLevels.filter(r => r.level > currentPrice);

    if (validResistances.length > 0) {
      const strongestResistance = validResistances.reduce((prev, current) =>
        current.touches > prev.touches ? current : prev
      );
      console.log(`Selected strongest resistance level: ${strongestResistance.level} with ${strongestResistance.touches} touches`);
      return strongestResistance.level;
    }
  }

  // Fallback to the highest high in the period, but ensure it's above current price
  const highestHigh = Math.max(...highs);
  if (highestHigh > currentPrice) {
    console.log(`No strong resistance found, using highest high: ${highestHigh}`);
    return highestHigh;
  } else {
    // If even the highest high is below current price, use 105% of current price as emergency fallback
    const emergencyResistance = currentPrice * 1.05;
    console.log(`Even highest high (${highestHigh}) is below current price, using emergency resistance: ${emergencyResistance}`);
    return emergencyResistance;
  }
}

// Detect candle patterns from historical data
function detectCandlePattern(polygonData: PolygonData, parameters: any): number {
  const timeframe = parameters?.timeframe || 'daily';
  const pattern = parameters?.pattern || 'any';

  console.log(`Detecting candle pattern: ${pattern} on ${timeframe} timeframe`);

  console.log(`Historical data available:`, {
    hasHistorical: !!polygonData.historical,
    hasOHLC: !!(polygonData.historical?.open && polygonData.historical?.high && polygonData.historical?.low && polygonData.historical?.close),
    dataLength: polygonData.historical?.close?.length || 0
  });

  if (!polygonData.historical || !polygonData.historical.open || !polygonData.historical.high ||
      !polygonData.historical.low || !polygonData.historical.close) {
    throw new Error(`Insufficient OHLC data for candle pattern detection`);
  }

  // Convert historical data to candle format
  const candles: any[] = [];
  const { open, high, low, close } = polygonData.historical;

  console.log(`\n=== CONVERTING HISTORICAL DATA TO CANDLES ===`);
  console.log(`Historical data lengths: open=${open?.length}, high=${high?.length}, low=${low?.length}, close=${close?.length}`);

  // Log sample of raw data
  console.log(`Sample historical data (last 3 periods):`);
  for (let i = Math.max(0, close.length - 3); i < close.length; i++) {
    console.log(`  Period ${i}: O:${open[i]} H:${high[i]} L:${low[i]} C:${close[i]}`);
  }

  for (let i = 0; i < close.length; i++) {
    const candleData = {
      open: open[i],
      high: high[i],
      low: low[i],
      close: close[i],
      date: new Date() // We don't have dates in this format, but patterns don't need them
    };

    // Validate candle data
    if (candleData.open == null || candleData.high == null || candleData.low == null || candleData.close == null) {
      console.warn(`⚠️ Invalid candle data at index ${i}:`, candleData);
      continue; // Skip invalid candles
    }

    candles.push(candleData);
  }

  console.log(`✅ Converted ${candles.length} valid candles for pattern detection`);

  // Analyze the most recent candle(s) for patterns
  const recentCandles = candles.slice(-10); // Look at last 10 candles for context

  if (recentCandles.length < 3) {
    throw new Error(`Need at least 3 candles for pattern detection, got ${recentCandles.length}`);
  }

  // Detect patterns based on the specified pattern type
  const detectedPatterns = detectCandlePatterns(recentCandles, pattern);

  console.log(`Detected ${detectedPatterns.length} patterns:`, detectedPatterns);

  // Return 1 if pattern found, 0 if not found
  return detectedPatterns.length > 0 ? 1 : 0;
}

// AURA'S ACTUAL CANDLE PATTERN DETECTION SYSTEM
// Interface for pattern detection result
interface PatternResult {
  pattern: string;
  position: number;
  significance: 'weak' | 'moderate' | 'strong';
  bullish: boolean;
}

// Helper functions for candle analysis (from Aura)
function isBullish(candle: any): boolean {
  return candle.close > candle.open;
}

function bodySize(candle: any): number {
  return Math.abs(candle.close - candle.open);
}

function candleRange(candle: any): number {
  return candle.high - candle.low;
}

function upperWick(candle: any): number {
  return candle.high - Math.max(candle.open, candle.close);
}

function lowerWick(candle: any): number {
  return Math.min(candle.open, candle.close) - candle.low;
}

// Detect Doji pattern (from Aura) - RELAXED FOR REAL MARKET CONDITIONS
function detectDoji(candles: any[], position: number): PatternResult | null {
  if (position < 0 || position >= candles.length) return null;

  const candle = candles[position];
  const body = bodySize(candle);
  const range = candleRange(candle);

  // Handle edge case where range is 0 (no price movement)
  if (range === 0) {
    console.log(`❌ Doji rejected: zero range candle`);
    return null;
  }

  const bodyToRangeRatio = body / range;

  console.log(`Doji check at position ${position}:`);
  console.log(`  Candle: O:${candle.open} H:${candle.high} L:${candle.low} C:${candle.close}`);
  console.log(`  Body: ${body.toFixed(4)}, Range: ${range.toFixed(4)}`);
  console.log(`  Body/Range ratio: ${bodyToRangeRatio.toFixed(4)} (threshold: 0.35 - LIBERAL)`);

  // LIBERAL THRESHOLD: Doji has a small body compared to its range (increased to 0.35 for real market detection)
  if (bodyToRangeRatio < 0.35) {
    // Determine significance based on position in trend
    let significance: 'weak' | 'moderate' | 'strong' = 'moderate';
    let bullish = true; // Default to bullish (neutral reversal)

    // Check if it's at the end of a downtrend (bullish) or uptrend (bearish)
    if (position >= 3) {
      const priorTrend = candles[position-1].close < candles[position-3].close;
      bullish = priorTrend; // Bullish if prior trend was down

      // More significant if it appears after a strong trend
      const trendStrength = Math.abs(candles[position-3].close - candles[position-1].close) / candles[position-3].close;
      if (trendStrength > 0.03) {
        significance = 'strong';
      }

      console.log(`  Trend analysis: prior trend down=${priorTrend}, strength=${trendStrength.toFixed(4)}`);
    }

    console.log(`✅ DOJI PATTERN DETECTED at position ${position} (${significance}, ${bullish ? 'bullish' : 'bearish'})`);

    return {
      pattern: 'doji',
      position,
      significance,
      bullish
    };
  }

  console.log(`❌ Doji rejected: body ratio ${bodyToRangeRatio.toFixed(4)} >= 0.35 threshold`);
  return null;
}

// Detect Hammer pattern (from Aura) - RELAXED FOR REAL MARKET CONDITIONS
function detectHammer(candles: any[], position: number): PatternResult | null {
  if (position < 0 || position >= candles.length) return null;

  const candle = candles[position];
  const body = bodySize(candle);
  const range = candleRange(candle);
  const lower = lowerWick(candle);

  // Handle edge case where range is 0
  if (range === 0) {
    console.log(`❌ Hammer rejected: zero range candle`);
    return null;
  }

  const bodyRatio = body / range;
  const lowerRatio = lower / range;

  console.log(`Hammer check at position ${position}:`);
  console.log(`  Candle: O:${candle.open} H:${candle.high} L:${candle.low} C:${candle.close}`);
  console.log(`  Body: ${body.toFixed(4)}, Range: ${range.toFixed(4)}, Lower wick: ${lower.toFixed(4)}`);
  console.log(`  Body/Range: ${bodyRatio.toFixed(4)} (threshold: 0.6 - LIBERAL)`);
  console.log(`  Lower/Range: ${lowerRatio.toFixed(4)} (threshold: 0.3 - LIBERAL)`);

  // LIBERAL THRESHOLDS: Hammer has a reasonably small body with a noticeable lower wick
  // Significantly relaxed to 0.6/0.3 for real market detection
  if (bodyRatio < 0.6 && lowerRatio > 0.3) {
    // Determine significance based on position in trend
    let significance: 'weak' | 'moderate' | 'strong' = 'moderate';
    let bullish = true;

    // More significant if it appears at the end of a downtrend
    if (position >= 3) {
      const isDowntrend = candles[position-1].close < candles[position-3].close;

      if (isDowntrend) {
        significance = 'strong';
        bullish = true;
      } else {
        significance = 'weak';
        bullish = false; // Inverted hammer in an uptrend is bearish
      }

      console.log(`  Trend analysis: downtrend=${isDowntrend}, significance=${significance}`);
    }

    console.log(`✅ HAMMER PATTERN DETECTED at position ${position} (${significance}, ${bullish ? 'bullish' : 'bearish'})`);

    return {
      pattern: 'hammer',
      position,
      significance,
      bullish
    };
  }

  console.log(`❌ Hammer rejected: body ratio ${bodyRatio.toFixed(4)} >= 0.6 OR lower wick ratio ${lowerRatio.toFixed(4)} <= 0.3`);
  return null;
}

// Detect Shooting Star pattern (from Aura) - RELAXED FOR REAL MARKET CONDITIONS
function detectShootingStar(candles: any[], position: number): PatternResult | null {
  if (position < 0 || position >= candles.length) return null;

  const candle = candles[position];
  const body = bodySize(candle);
  const range = candleRange(candle);
  const upper = upperWick(candle);

  // Handle edge case where range is 0
  if (range === 0) {
    console.log(`❌ Shooting star rejected: zero range candle`);
    return null;
  }

  const bodyRatio = body / range;
  const upperRatio = upper / range;

  console.log(`Shooting star check at position ${position}:`);
  console.log(`  Candle: O:${candle.open} H:${candle.high} L:${candle.low} C:${candle.close}`);
  console.log(`  Body: ${body.toFixed(4)}, Range: ${range.toFixed(4)}, Upper wick: ${upper.toFixed(4)}`);
  console.log(`  Body/Range: ${bodyRatio.toFixed(4)} (threshold: 0.6 - LIBERAL)`);
  console.log(`  Upper/Range: ${upperRatio.toFixed(4)} (threshold: 0.3 - LIBERAL)`);

  // LIBERAL THRESHOLDS: Shooting star has a reasonably small body with a noticeable upper wick
  // Significantly relaxed to 0.6/0.3 for real market detection
  if (bodyRatio < 0.6 && upperRatio > 0.3) {
    // Determine significance based on position in trend
    let significance: 'weak' | 'moderate' | 'strong' = 'moderate';
    let bullish = false;

    // More significant if it appears at the end of an uptrend
    if (position >= 3) {
      const isUptrend = candles[position-1].close > candles[position-3].close;

      if (isUptrend) {
        significance = 'strong';
      } else {
        significance = 'weak';
        bullish = true; // In a downtrend, this could be a bullish signal
      }

      console.log(`  Trend analysis: uptrend=${isUptrend}, significance=${significance}`);
    }

    console.log(`✅ SHOOTING STAR PATTERN DETECTED at position ${position} (${significance}, ${bullish ? 'bullish' : 'bearish'})`);

    return {
      pattern: 'shooting_star',
      position,
      significance,
      bullish
    };
  }

  console.log(`❌ Shooting star rejected: body ratio ${bodyRatio.toFixed(4)} >= 0.6 OR upper wick ratio ${upperRatio.toFixed(4)} <= 0.3`);
  return null;
}

// Detect Marubozu pattern (from Aura) - Strong directional candles with little to no wicks
function detectMarubozu(candles: any[], position: number): PatternResult | null {
  if (position < 0 || position >= candles.length) return null;

  const candle = candles[position];
  const body = bodySize(candle);
  const range = candleRange(candle);
  const upper = upperWick(candle);
  const lower = lowerWick(candle);

  // Handle edge case where range is 0
  if (range === 0) {
    console.log(`❌ Marubozu rejected: zero range candle`);
    return null;
  }

  const bodyRatio = body / range;
  const upperRatio = upper / range;
  const lowerRatio = lower / range;
  const bullish = isBullish(candle);

  console.log(`Marubozu check at position ${position}:`);
  console.log(`  Candle: O:${candle.open} H:${candle.high} L:${candle.low} C:${candle.close}`);
  console.log(`  Body: ${body.toFixed(4)}, Range: ${range.toFixed(4)}`);
  console.log(`  Body/Range: ${bodyRatio.toFixed(4)} (threshold: 0.75 - LIBERAL BODY)`);
  console.log(`  Upper/Range: ${upperRatio.toFixed(4)} (threshold: 0.15 - LIBERAL WICK)`);
  console.log(`  Lower/Range: ${lowerRatio.toFixed(4)} (threshold: 0.15 - LIBERAL WICK)`);
  console.log(`  Direction: ${bullish ? 'bullish' : 'bearish'}`);

  // LIBERAL Marubozu criteria: Large body (>75% of range) with small wicks (<15% each)
  if (bodyRatio > 0.75 && upperRatio < 0.15 && lowerRatio < 0.15) {
    // Determine significance based on body size and trend context
    let significance: 'weak' | 'moderate' | 'strong' = 'strong'; // Marubozu are inherently strong signals

    // Check trend context for additional significance
    if (position >= 3) {
      const priorTrend = candles[position-1].close < candles[position-3].close;
      const trendStrength = Math.abs(candles[position-3].close - candles[position-1].close) / candles[position-3].close;

      // If marubozu appears against the trend, it's even more significant
      if ((bullish && priorTrend) || (!bullish && !priorTrend)) {
        significance = 'strong';
      }

      console.log(`  Trend analysis: prior trend down=${priorTrend}, strength=${trendStrength.toFixed(4)}, significance=${significance}`);
    }

    const patternName = bullish ? 'bullish_marubozu' : 'bearish_marubozu';
    console.log(`✅ ${patternName.toUpperCase()} PATTERN DETECTED at position ${position} (${significance})`);

    return {
      pattern: patternName,
      position,
      significance,
      bullish
    };
  }

  console.log(`❌ Marubozu rejected: body ratio ${bodyRatio.toFixed(4)} <= 0.75 OR upper wick ${upperRatio.toFixed(4)} >= 0.15 OR lower wick ${lowerRatio.toFixed(4)} >= 0.15`);
  return null;
}

// Detect Engulfing pattern (from Aura) - Uses LAST TWO CANDLES
function detectEngulfing(candles: any[], position: number): PatternResult | null {
  if (position < 1 || position >= candles.length) return null;

  const current = candles[position];     // Most recent candle (last)
  const previous = candles[position - 1]; // Second to last candle

  console.log(`Engulfing check using LAST TWO CANDLES:`);
  console.log(`Previous candle (${position - 1}): O:${previous.open} H:${previous.high} L:${previous.low} C:${previous.close}`);
  console.log(`Current candle (${position}): O:${current.open} H:${current.high} L:${current.low} C:${current.close}`);

  const currentBullish = isBullish(current);
  const previousBullish = isBullish(previous);

  console.log(`Previous candle direction: ${previousBullish ? 'bullish' : 'bearish'}`);
  console.log(`Current candle direction: ${currentBullish ? 'bullish' : 'bearish'}`);

  // BULLISH ENGULFING: Current bullish candle completely engulfs previous bearish candle
  // Current candle's body must completely contain the previous candle's body
  if (currentBullish && !previousBullish) {
    const currentEngulfsPrevious = current.open <= previous.close && current.close >= previous.open;

    console.log(`Bullish engulfing check:`);
    console.log(`  Current open (${current.open}) <= Previous close (${previous.close}): ${current.open <= previous.close}`);
    console.log(`  Current close (${current.close}) >= Previous open (${previous.open}): ${current.close >= previous.open}`);
    console.log(`  Engulfment: ${currentEngulfsPrevious}`);

    if (currentEngulfsPrevious) {
      console.log(`✅ BULLISH ENGULFING PATTERN DETECTED - Current bullish candle engulfs previous bearish candle`);

      return {
        pattern: 'bullish_engulfing',
        position,
        significance: 'strong',
        bullish: true
      };
    }
  }

  // BEARISH ENGULFING: Current bearish candle completely engulfs previous bullish candle
  // Current candle's body must completely contain the previous candle's body
  if (!currentBullish && previousBullish) {
    const currentEngulfsPrevious = current.open >= previous.close && current.close <= previous.open;

    console.log(`Bearish engulfing check:`);
    console.log(`  Current open (${current.open}) >= Previous close (${previous.close}): ${current.open >= previous.close}`);
    console.log(`  Current close (${current.close}) <= Previous open (${previous.open}): ${current.close <= previous.open}`);
    console.log(`  Engulfment: ${currentEngulfsPrevious}`);

    if (currentEngulfsPrevious) {
      console.log(`✅ BEARISH ENGULFING PATTERN DETECTED - Current bearish candle engulfs previous bullish candle`);

      return {
        pattern: 'bearish_engulfing',
        position,
        significance: 'strong',
        bullish: false
      };
    }
  }

  console.log(`❌ No engulfing patterns detected - candles don't meet engulfment criteria`);
  return null;
}

// Detect Morning Star pattern (from Aura)
function detectMorningStar(candles: any[], position: number): PatternResult | null {
  if (position < 2 || position >= candles.length) return null;

  const first = candles[position - 2];  // First candle (bearish)
  const middle = candles[position - 1]; // Middle candle (small body)
  const last = candles[position];       // Last candle (bullish)

  console.log(`Morning star check at position ${position}`);

  // First candle should be bearish with a large body
  const firstBodyRatio = bodySize(first) / candleRange(first);
  const firstIsBearish = !isBullish(first);

  // Middle candle should have a small body (doji-like)
  const middleBodyRatio = bodySize(middle) / candleRange(middle);

  // Last candle should be bullish with a large body
  const lastBodyRatio = bodySize(last) / candleRange(last);
  const lastIsBullish = isBullish(last);

  console.log(`First candle: bearish=${firstIsBearish}, body ratio=${firstBodyRatio.toFixed(4)} (threshold: 0.4 - LIBERAL)`);
  console.log(`Middle candle: body ratio=${middleBodyRatio.toFixed(4)} (threshold: 0.5 - LIBERAL)`);
  console.log(`Last candle: bullish=${lastIsBullish}, body ratio=${lastBodyRatio.toFixed(4)} (threshold: 0.4 - LIBERAL)`);

  // LIBERAL pattern criteria - significantly relaxed for real market detection
  if (firstIsBearish && firstBodyRatio > 0.4 &&
      middleBodyRatio < 0.5 &&
      lastIsBullish && lastBodyRatio > 0.4) {

    // Additional check: last candle should close above the midpoint of the first candle
    const firstMidpoint = first.open - (bodySize(first) / 2);
    const closesAboveMidpoint = last.close > firstMidpoint;

    console.log(`Midpoint check: last close ${last.close} > first midpoint ${firstMidpoint.toFixed(4)} = ${closesAboveMidpoint}`);

    if (closesAboveMidpoint) {
      console.log(`✅ MORNING STAR PATTERN DETECTED at position ${position}`);

      return {
        pattern: 'morning_star',
        position,
        significance: 'strong',
        bullish: true
      };
    }
  }

  console.log(`❌ Morning star rejected`);
  return null;
}

// Detect Evening Star pattern (from Aura)
function detectEveningStar(candles: any[], position: number): PatternResult | null {
  if (position < 2 || position >= candles.length) return null;

  const first = candles[position - 2];  // First candle (bullish)
  const middle = candles[position - 1]; // Middle candle (small body)
  const last = candles[position];       // Last candle (bearish)

  console.log(`Evening star check at position ${position}`);

  // First candle should be bullish with a large body
  const firstBodyRatio = bodySize(first) / candleRange(first);
  const firstIsBullish = isBullish(first);

  // Middle candle should have a small body (doji-like)
  const middleBodyRatio = bodySize(middle) / candleRange(middle);

  // Last candle should be bearish with a large body
  const lastBodyRatio = bodySize(last) / candleRange(last);
  const lastIsBearish = !isBullish(last);

  console.log(`First candle: bullish=${firstIsBullish}, body ratio=${firstBodyRatio.toFixed(4)} (threshold: 0.4 - LIBERAL)`);
  console.log(`Middle candle: body ratio=${middleBodyRatio.toFixed(4)} (threshold: 0.5 - LIBERAL)`);
  console.log(`Last candle: bearish=${lastIsBearish}, body ratio=${lastBodyRatio.toFixed(4)} (threshold: 0.4 - LIBERAL)`);

  // LIBERAL pattern criteria - significantly relaxed for real market detection
  if (firstIsBullish && firstBodyRatio > 0.4 &&
      middleBodyRatio < 0.5 &&
      lastIsBearish && lastBodyRatio > 0.4) {

    // Additional check: last candle should close below the midpoint of the first candle
    const firstMidpoint = first.open + (bodySize(first) / 2);
    const closesBelowMidpoint = last.close < firstMidpoint;

    console.log(`Midpoint check: last close ${last.close} < first midpoint ${firstMidpoint.toFixed(4)} = ${closesBelowMidpoint}`);

    if (closesBelowMidpoint) {
      console.log(`✅ EVENING STAR PATTERN DETECTED at position ${position}`);

      return {
        pattern: 'evening_star',
        position,
        significance: 'strong',
        bullish: false
      };
    }
  }

  console.log(`❌ Evening star rejected`);
  return null;
}

// Helper function to detect specific candle patterns using Aura's system
// FOCUSES ONLY ON THE MOST RECENT (LAST) CANDLE
function detectCandlePatterns(candles: any[], patternType: string): any[] {
  const patterns: any[] = [];

  console.log(`\n=== AURA CANDLE PATTERN DETECTION (LAST CANDLE ONLY) ===`);
  console.log(`Pattern type: ${patternType}`);
  console.log(`Number of candles: ${candles.length}`);

  if (candles.length === 0) {
    console.log(`❌ No candles available for pattern detection`);
    return patterns;
  }

  // ONLY analyze the LAST candle (most recent)
  const lastIndex = candles.length - 1;
  const lastCandle = candles[lastIndex];

  console.log(`\n=== ANALYZING ONLY THE MOST RECENT CANDLE ===`);
  console.log(`Last candle (index ${lastIndex}): O:${lastCandle.open} H:${lastCandle.high} L:${lastCandle.low} C:${lastCandle.close}`);

  // SINGLE-CANDLE PATTERNS (analyze only the last candle)

  // Check for Doji pattern on the last candle
  if (patternType === 'any' || patternType === 'doji' || patternType === 'reversal') {
    const doji = detectDoji(candles, lastIndex);
    if (doji) {
      console.log(`✅ DOJI pattern detected on MOST RECENT candle`);
      patterns.push({
        pattern: doji.pattern,
        position: doji.position,
        bullish: doji.bullish,
        significance: doji.significance
      });
    }
  }

  // Check for Hammer pattern on the last candle
  if (patternType === 'any' || patternType === 'hammer' || patternType === 'bullish' || patternType === 'reversal') {
    const hammer = detectHammer(candles, lastIndex);
    if (hammer) {
      console.log(`✅ HAMMER pattern detected on MOST RECENT candle`);
      patterns.push({
        pattern: hammer.pattern,
        position: hammer.position,
        bullish: hammer.bullish,
        significance: hammer.significance
      });
    }
  }

  // Check for Shooting Star pattern on the last candle
  if (patternType === 'any' || patternType === 'shooting_star' || patternType === 'bearish' || patternType === 'reversal') {
    const shootingStar = detectShootingStar(candles, lastIndex);
    if (shootingStar) {
      console.log(`✅ SHOOTING STAR pattern detected on MOST RECENT candle`);
      patterns.push({
        pattern: shootingStar.pattern,
        position: shootingStar.position,
        bullish: shootingStar.bullish,
        significance: shootingStar.significance
      });
    }
  }

  // Check for Marubozu pattern on the last candle
  if (patternType === 'any' || patternType === 'marubozu' || patternType === 'bullish' || patternType === 'bearish') {
    const marubozu = detectMarubozu(candles, lastIndex);
    if (marubozu) {
      console.log(`✅ MARUBOZU pattern detected on MOST RECENT candle`);
      patterns.push({
        pattern: marubozu.pattern,
        position: marubozu.position,
        bullish: marubozu.bullish,
        significance: marubozu.significance
      });
    }
  }

  // MULTI-CANDLE PATTERNS (only if they COMPLETE on the last candle)

  // Check for Engulfing patterns that COMPLETE on the last candle
  if (patternType === 'any' || patternType === 'engulfing' || patternType === 'bullish' || patternType === 'bearish' || patternType === 'reversal') {
    if (lastIndex >= 1) { // Need at least 2 candles for engulfing
      const engulfing = detectEngulfing(candles, lastIndex);
      if (engulfing) {
        console.log(`✅ ENGULFING pattern COMPLETED on MOST RECENT candle`);
        patterns.push({
          pattern: engulfing.pattern,
          position: engulfing.position,
          bullish: engulfing.bullish,
          significance: engulfing.significance
        });
      }
    }
  }

  // Check for Morning Star patterns that COMPLETE on the last candle
  if (patternType === 'any' || patternType === 'morning_star' || patternType === 'bullish' || patternType === 'reversal') {
    if (lastIndex >= 2) { // Need at least 3 candles for morning star
      const morningStar = detectMorningStar(candles, lastIndex);
      if (morningStar) {
        console.log(`✅ MORNING STAR pattern COMPLETED on MOST RECENT candle`);
        patterns.push({
          pattern: morningStar.pattern,
          position: morningStar.position,
          bullish: morningStar.bullish,
          significance: morningStar.significance
        });
      }
    }
  }

  // Check for Evening Star patterns that COMPLETE on the last candle
  if (patternType === 'any' || patternType === 'evening_star' || patternType === 'bearish' || patternType === 'reversal') {
    if (lastIndex >= 2) { // Need at least 3 candles for evening star
      const eveningStar = detectEveningStar(candles, lastIndex);
      if (eveningStar) {
        console.log(`✅ EVENING STAR pattern COMPLETED on MOST RECENT candle`);
        patterns.push({
          pattern: eveningStar.pattern,
          position: eveningStar.position,
          bullish: eveningStar.bullish,
          significance: eveningStar.significance
        });
      }
    }
  }

  // NO FALLBACK SYSTEM - Only return actual detected patterns

  // Filter patterns based on bullish/bearish preference
  if (patternType === 'bullish') {
    const bullishPatterns = patterns.filter(p => p.bullish === true);
    console.log(`\n=== PATTERN DETECTION SUMMARY ===`);
    console.log(`Requested: ${patternType} patterns`);
    console.log(`Total patterns found: ${patterns.length}`);
    console.log(`Bullish patterns: ${bullishPatterns.length}`);
    if (bullishPatterns.length > 0) {
      console.log(`Bullish patterns: ${bullishPatterns.map(p => p.pattern).join(', ')}`);
    }
    console.log(`=====================================\n`);
    return bullishPatterns;
  } else if (patternType === 'bearish') {
    const bearishPatterns = patterns.filter(p => p.bullish === false);
    console.log(`\n=== PATTERN DETECTION SUMMARY ===`);
    console.log(`Requested: ${patternType} patterns`);
    console.log(`Total patterns found: ${patterns.length}`);
    console.log(`Bearish patterns: ${bearishPatterns.length}`);
    if (bearishPatterns.length > 0) {
      console.log(`Bearish patterns: ${bearishPatterns.map(p => p.pattern).join(', ')}`);
    }
    console.log(`=====================================\n`);
    return bearishPatterns;
  }

  console.log(`\n=== PATTERN DETECTION SUMMARY ===`);
  console.log(`Requested: ${patternType} patterns`);
  console.log(`Total patterns found: ${patterns.length}`);
  if (patterns.length > 0) {
    console.log(`All patterns: ${patterns.map(p => `${p.pattern}(${p.bullish === true ? 'bullish' : p.bullish === false ? 'bearish' : 'neutral'})`).join(', ')}`);
  } else {
    console.log(`❌ NO PATTERNS DETECTED - Check thresholds and data quality`);
  }
  console.log(`=====================================\n`);

  return patterns;
}

/**
 * Execute a block and return its result
 * @param block - The block to execute
 * @param blockResults - Results of previously executed blocks
 * @param polygonData - Data from Polygon API
 * @param inheritedValue - Value passed from a previous condition block in the chain
 * @returns The result of executing the block
 */
async function executeBlock(
  block: BlockUnion,
  blockResults: Map<string, any>,
  polygonData: PolygonData,
  inheritedValue?: any
): Promise<any> {
  // Add detailed logging for debugging
  console.log(`executeBlock called with block type: "${block.type}" (typeof: ${typeof block.type})`);
  console.log(`Block ID: ${(block as any).id}`);
  console.log(`Available BlockType enum values:`, Object.values(BlockType));

  // Handle both enum values and string literals for backward compatibility
  const blockType = block.type;

  switch (blockType) {
    case BlockType.WHEN_RUN:
    case 'WHEN_RUN':
      // When Run block is just an entry point, return true to indicate it executed
      return true;
    case BlockType.INDICATOR:
    case 'INDICATOR':
      return executeIndicatorBlock(block, polygonData);
    case BlockType.MOMENTUM_INDICATOR:
    case 'MOMENTUM_INDICATOR':
      return executeMomentumIndicatorBlock(block, polygonData);
    case BlockType.MOVING_AVERAGE:
    case 'MOVING_AVERAGE':
      return executeMovingAverageBlock(block, polygonData);
    case BlockType.TREND_INDICATOR:
    case 'TREND_INDICATOR':
      return executeTrendIndicatorBlock(block, polygonData);
    case BlockType.PRICE:
    case 'PRICE':
      return executePriceBlock(block, polygonData);
    case BlockType.FUNDAMENTAL:
    case 'FUNDAMENTAL':
      return executeFundamentalBlock(block, polygonData);
    case BlockType.CONDITION:
    case 'CONDITION':
      return executeConditionBlock(block, blockResults, polygonData, inheritedValue);
    case BlockType.COMPARISON:
    case 'COMPARISON':
      return executeComparisonBlock(block, blockResults, polygonData);
    case BlockType.TRIGGER:
    case 'TRIGGER':
      return executeTriggerBlock(block, blockResults);
    case BlockType.SIGNAL:
    case 'SIGNAL':
      // Signal blocks work exactly the same as trigger blocks
      return executeTriggerBlock(block, blockResults);
    case BlockType.OPERATOR:
    case 'OPERATOR':
      return executeOperatorBlock(block, blockResults);
    case BlockType.BULLISH_CONFIDENCE_BOOST:
    case 'BULLISH_CONFIDENCE_BOOST':
      return executeBullishConfidenceBoostBlock(block, blockResults);
    case BlockType.BEARISH_CONFIDENCE_BOOST:
    case 'BEARISH_CONFIDENCE_BOOST':
      return executeBearishConfidenceBoostBlock(block, blockResults);
    case BlockType.CONFIDENCE_BOOST:
    case 'CONFIDENCE_BOOST':
      return executeConfidenceBoostBlock(block, blockResults);
    case BlockType.AND:
    case 'AND':
      return executeAndBlock(block, blockResults);
    case BlockType.OR:
    case 'OR':
      return executeOrBlock(block, blockResults);
    case BlockType.CANDLE_PATTERN:
    case 'CANDLE_PATTERN':
      return executeCandlePatternBlock(block, blockResults, polygonData);
    case BlockType.CHART_PATTERN:
    case 'CHART_PATTERN':
      return await executeChartPatternBlock(block, blockResults, polygonData);
    case BlockType.STOCK_SENTIMENT:
    case 'STOCK_SENTIMENT':
      return await executeStockSentimentBlock(block, polygonData);

    // Risk Analysis & Signal Enhancement blocks
    case BlockType.RISK_ANALYZER:
    case 'RISK_ANALYZER':
      return await RiskAnalysisProcessor.processRiskAnalyzer(block as RiskAnalysisBlock, polygonData, { blockResults });
    case BlockType.TARGET_ANALYZER:
    case 'TARGET_ANALYZER':
      return await RiskAnalysisProcessor.processTargetAnalyzer(block as RiskAnalysisBlock, polygonData, { blockResults });
    case BlockType.SIGNAL_ENHANCER:
    case 'SIGNAL_ENHANCER':
      return await RiskAnalysisProcessor.processSignalEnhancer(block as RiskAnalysisBlock, polygonData, { blockResults });
    case BlockType.RISK_REWARD_ANALYZER:
    case 'RISK_REWARD_ANALYZER':
      return await RiskAnalysisProcessor.processRiskRewardAnalyzer(block as RiskAnalysisBlock, polygonData, { blockResults });

    // Advanced Analysis blocks
    case BlockType.CORRELATION_ANALYSIS:
    case 'CORRELATION_ANALYSIS':
      return await AdvancedAnalysisProcessor.processCorrelationAnalysis(block as AdvancedAnalysisBlock, polygonData, { blockResults });
    case BlockType.MOMENTUM_SHIFT:
    case 'MOMENTUM_SHIFT':
      return await AdvancedAnalysisProcessor.processMomentumShift(block as AdvancedAnalysisBlock, polygonData, { blockResults });
    case BlockType.TREND_STRENGTH:
    case 'TREND_STRENGTH':
      return await AdvancedAnalysisProcessor.processTrendStrength(block as AdvancedAnalysisBlock, polygonData, { blockResults });
    case BlockType.VOLATILITY_FILTER:
    case 'VOLATILITY_FILTER':
      return await AdvancedAnalysisProcessor.processVolatilityFilter(block as AdvancedAnalysisBlock, polygonData, { blockResults });
    case BlockType.FIBONACCI_LEVELS:
    case 'FIBONACCI_LEVELS':
      return await AdvancedAnalysisProcessor.processFibonacciLevels(block as AdvancedAnalysisBlock, polygonData, { blockResults });
    case BlockType.MARKET_SENTIMENT:
    case 'MARKET_SENTIMENT':
      return await AdvancedAnalysisProcessor.processMarketSentiment(block as AdvancedAnalysisBlock, polygonData, { blockResults });
    case BlockType.SECTOR_ANALYSIS:
    case 'SECTOR_ANALYSIS':
      return await AdvancedAnalysisProcessor.processSectorAnalysis(block as AdvancedAnalysisBlock, polygonData, { blockResults });

    // Signal Quality & Filtering blocks
    case BlockType.SIGNAL_QUALITY_FILTER:
    case 'SIGNAL_QUALITY_FILTER':
      return await SignalQualityProcessor.processSignalQualityFilter(block as SignalQualityBlock, polygonData, { blockResults });
    case BlockType.CONFIDENCE_THRESHOLD:
    case 'CONFIDENCE_THRESHOLD':
      return await SignalQualityProcessor.processConfidenceThreshold(block as SignalQualityBlock, polygonData, { blockResults });
    case BlockType.SIGNAL_CONFIRMATION_DELAY:
    case 'SIGNAL_CONFIRMATION_DELAY':
      return await SignalQualityProcessor.processSignalConfirmationDelay(block as SignalQualityBlock, polygonData, { blockResults });
    case BlockType.MARKET_HOURS_FILTER:
    case 'MARKET_HOURS_FILTER':
      return await SignalQualityProcessor.processMarketHoursFilter(block as SignalQualityBlock, polygonData, { blockResults });

    case BlockType.CONSOLE_LOG:
    case 'CONSOLE_LOG':
      return executeConsoleLogBlock(block, blockResults);

    default:
      console.error(`Unknown block type encountered:`);
      console.error(`  Block type: "${blockType}" (typeof: ${typeof blockType})`);
      console.error(`  Block ID: ${(block as any).id}`);
      console.error(`  Full block:`, JSON.stringify(block, null, 2));
      console.error(`  Available enum values:`, Object.values(BlockType));
      throw new Error(`Unknown block type: ${blockType}`);
  }
}

/**
 * Execute an indicator block
 * @param block - The indicator block to execute
 * @param polygonData - Data from Polygon API
 * @returns The indicator value
 */
function executeIndicatorBlock(block: any, polygonData: PolygonData): any {
  const { indicator, parameters } = block;

  console.log(`Executing indicator block for ${indicator}`);

  // Normalize the indicator name
  const normalizedIndicator = indicator.toLowerCase().trim();

  // Check if the indicator exists in the data or needs to be calculated
  if (!polygonData.indicators[normalizedIndicator]) {
    console.error(`Indicator not found: ${normalizedIndicator}`);
    console.log(`Available indicators: ${Object.keys(polygonData.indicators).join(', ')}`);

    throw new Error(`Indicator '${normalizedIndicator}' not found in polygon data. Available indicators: ${Object.keys(polygonData.indicators).join(', ')}`);
  }

  // Handle special case where support/resistance/candle_pattern/rsi/macd/vwap are marked for calculation
  if (polygonData.indicators[normalizedIndicator] === 'calculated_in_executor') {
    console.log(`Calculating ${normalizedIndicator} in executor`);

    if (normalizedIndicator === 'support') {
      return calculateSupportLevel(polygonData, parameters);
    } else if (normalizedIndicator === 'resistance') {
      return calculateResistanceLevel(polygonData, parameters);
    } else if (normalizedIndicator === 'candle_pattern') {
      return detectCandlePattern(polygonData, parameters);
    } else if (normalizedIndicator === 'rsi') {
      // Calculate RSI from historical close prices
      console.log("Calculating RSI in executor from historical close prices");
      if (polygonData.historical && polygonData.historical.close && polygonData.historical.close.length > 0) {
        const period = parameters?.period || 14;
        const calculatedRsi = calculateRSI(polygonData.historical.close, period);
        if (calculatedRsi && calculatedRsi.length > 0) {
          const latestRsi = calculatedRsi[calculatedRsi.length - 1];
          if (!isNaN(latestRsi)) {
            console.log(`Calculated RSI value: ${latestRsi}`);
            return latestRsi;
          }
        }
      }
      throw new Error(`Failed to calculate RSI from historical data`);
    } else if (normalizedIndicator === 'macd') {
      // Calculate MACD from historical close prices
      console.log("Calculating MACD in executor from historical close prices");
      if (polygonData.historical && polygonData.historical.close && polygonData.historical.close.length > 0) {
        const calculatedMacd = calculateMACD(polygonData.historical.close);
        if (calculatedMacd && calculatedMacd.macd && calculatedMacd.macd.length > 0) {
          const latestIndex = calculatedMacd.macd.length - 1;
          return {
            macd: calculatedMacd.macd[latestIndex],
            signal: calculatedMacd.signal[latestIndex],
            histogram: calculatedMacd.histogram[latestIndex]
          };
        }
      }
      throw new Error(`Failed to calculate MACD from historical data`);
    } else if (normalizedIndicator === 'vwap') {
      // Calculate VWAP from historical data
      console.log("Calculating VWAP in executor from historical data");
      if (polygonData.historical && polygonData.historical.high && polygonData.historical.low &&
          polygonData.historical.close && polygonData.historical.volume) {
        const calculatedVwap = calculateVWAP(
          polygonData.historical.high,
          polygonData.historical.low,
          polygonData.historical.close,
          polygonData.historical.volume
        );
        if (calculatedVwap && calculatedVwap.length > 0) {
          const latestVwap = calculatedVwap[calculatedVwap.length - 1];
          if (!isNaN(latestVwap)) {
            console.log(`Calculated VWAP value: ${latestVwap}`);
            return latestVwap;
          }
        }
      }
      throw new Error(`Failed to calculate VWAP from historical data`);
    } else if (normalizedIndicator === 'stochastic') {
      // Calculate Stochastic from historical data
      console.log("Calculating Stochastic in executor from historical data");
      if (polygonData.historical && polygonData.historical.high && polygonData.historical.low && polygonData.historical.close) {
        const calculatedStochastic = calculateStochastic(
          polygonData.historical.high,
          polygonData.historical.low,
          polygonData.historical.close,
          parameters?.period || 14,
          parameters?.smoothD || 3
        );
        if (calculatedStochastic && calculatedStochastic.k && calculatedStochastic.k.length > 0) {
          const latestStochastic = calculatedStochastic.k[calculatedStochastic.k.length - 1];
          if (!isNaN(latestStochastic)) {
            console.log(`Calculated Stochastic %K value: ${latestStochastic}`);
            return latestStochastic;
          }
        }
      }
      throw new Error(`Failed to calculate Stochastic from historical data`);
    } else if (normalizedIndicator === 'williams_r') {
      // Calculate Williams %R from historical data
      console.log("Calculating Williams %R in executor from historical data");
      if (polygonData.historical && polygonData.historical.high && polygonData.historical.low && polygonData.historical.close) {
        const calculatedWilliamsR = calculateWilliamsR(
          polygonData.historical.high,
          polygonData.historical.low,
          polygonData.historical.close,
          parameters?.period || 14
        );
        if (calculatedWilliamsR && calculatedWilliamsR.length > 0) {
          const latestWilliamsR = calculatedWilliamsR[calculatedWilliamsR.length - 1];
          if (!isNaN(latestWilliamsR)) {
            console.log(`Calculated Williams %R value: ${latestWilliamsR}`);
            return latestWilliamsR;
          }
        }
      }
      throw new Error(`Failed to calculate Williams %R from historical data`);
    } else if (normalizedIndicator === 'cci') {
      // Calculate CCI from historical data
      console.log("Calculating CCI in executor from historical data");
      if (polygonData.historical && polygonData.historical.high && polygonData.historical.low && polygonData.historical.close) {
        const calculatedCCI = calculateCCI(
          polygonData.historical.high,
          polygonData.historical.low,
          polygonData.historical.close,
          parameters?.period || 20
        );
        if (calculatedCCI && calculatedCCI.length > 0) {
          const latestCCI = calculatedCCI[calculatedCCI.length - 1];
          if (!isNaN(latestCCI)) {
            console.log(`Calculated CCI value: ${latestCCI}`);
            return latestCCI;
          }
        }
      }
      throw new Error(`Failed to calculate CCI from historical data`);
    }
  }

  // Get the indicator data
  const indicatorData = polygonData.indicators[normalizedIndicator];
  console.log(`Found indicator data for ${normalizedIndicator}: ${typeof indicatorData}`);

  // Log a sample of the indicator data
  if (Array.isArray(indicatorData)) {
    console.log(`Indicator data sample: ${indicatorData.slice(-5).join(', ')}`);
  } else if (typeof indicatorData === 'object') {
    console.log(`Indicator data keys: ${Object.keys(indicatorData).join(', ')}`);
  }

  // Return the latest value by default, or apply specific logic based on the indicator
  switch (normalizedIndicator) {
    case 'rsi':
      // Return the latest RSI value
      if (Array.isArray(indicatorData) && indicatorData.length > 0) {
        const latestRsi = indicatorData[indicatorData.length - 1];
        console.log(`Latest RSI value: ${latestRsi}`);

        // Ensure we return a number
        if (typeof latestRsi === 'number') {
          return latestRsi;
        } else {
          console.warn(`RSI value is not a number: ${latestRsi}, converting to number`);
          const numericRsi = Number(latestRsi);
          if (!isNaN(numericRsi)) {
            return numericRsi;
          }
        }
      }

      // If we get here, either there's no data or it couldn't be converted to a number
      console.warn(`RSI data is not in expected format or couldn't be converted to a number, returning default value`);

      // Try to get RSI from raw data if available
      try {
        if (polygonData.historical && polygonData.historical.close && polygonData.historical.close.length > 0) {
          console.log("Calculating RSI from historical close prices");
          const calculatedRsi = calculateRSI(polygonData.historical.close);
          if (calculatedRsi && calculatedRsi.length > 0) {
            const latestCalculatedRsi = calculatedRsi[calculatedRsi.length - 1];
            if (!isNaN(latestCalculatedRsi)) {
              console.log(`Calculated RSI value: ${latestCalculatedRsi}`);
              return latestCalculatedRsi;
            }
          }
        }
      } catch (error) {
        console.error("Error calculating RSI from historical data:", error);
      }

      // Throw an error instead of returning a default value
      throw new Error(`RSI data is not in expected format or couldn't be converted to a number. Data: ${JSON.stringify(indicatorData)}`);
    case 'macd':
      // Return the latest MACD values
      if (indicatorData && indicatorData.macd && indicatorData.signal && indicatorData.histogram) {
        return {
          macd: indicatorData.macd[indicatorData.macd.length - 1],
          signal: indicatorData.signal[indicatorData.signal.length - 1],
          histogram: indicatorData.histogram[indicatorData.histogram.length - 1]
        };
      } else {
        console.warn(`MACD data is not in expected format, returning default values`);
        return { macd: 0, signal: 0, histogram: 0 };
      }
    case 'bollinger':
      // Return the latest Bollinger Bands values
      if (indicatorData && indicatorData.upper && indicatorData.middle && indicatorData.lower) {
        return {
          upper: indicatorData.upper[indicatorData.upper.length - 1],
          middle: indicatorData.middle[indicatorData.middle.length - 1],
          lower: indicatorData.lower[indicatorData.lower.length - 1]
        };
      } else {
        console.warn(`Bollinger Bands data is not in expected format, returning default values`);
        return { upper: 0, middle: 0, lower: 0 };
      }
    case 'sma':
      // Return the specified SMA value
      const period = parameters?.period || 20;
      const smaKey = `sma${period}`;
      if (indicatorData && indicatorData[smaKey] && indicatorData[smaKey].length > 0) {
        return indicatorData[smaKey][indicatorData[smaKey].length - 1];
      } else {
        console.warn(`SMA data is not in expected format, returning default value`);
        return 0;
      }
    case 'ema':
      // Return the specified EMA value
      const emaPeriod = parameters?.period || 20;
      const emaKey = `ema${emaPeriod}`;
      if (indicatorData && indicatorData[emaKey] && indicatorData[emaKey].length > 0) {
        return indicatorData[emaKey][indicatorData[emaKey].length - 1];
      } else {
        console.warn(`EMA data is not in expected format, returning default value`);
        return 0;
      }
    case 'stochastic':
      // Return the latest Stochastic %K value
      if (indicatorData && indicatorData.k && Array.isArray(indicatorData.k) && indicatorData.k.length > 0) {
        return indicatorData.k[indicatorData.k.length - 1];
      } else {
        console.warn(`Stochastic data is not in expected format, returning default value`);
        return 50; // Neutral value for Stochastic
      }
    case 'williams_r':
      // Return the latest Williams %R value
      if (Array.isArray(indicatorData) && indicatorData.length > 0) {
        return indicatorData[indicatorData.length - 1];
      } else {
        console.warn(`Williams %R data is not in expected format, returning default value`);
        return -50; // Neutral value for Williams %R
      }
    case 'cci':
      // Return the latest CCI value
      if (Array.isArray(indicatorData) && indicatorData.length > 0) {
        return indicatorData[indicatorData.length - 1];
      } else {
        console.warn(`CCI data is not in expected format, returning default value`);
        return 0; // Neutral value for CCI
      }

    default:
      // For other indicators, return the latest value with error handling
      console.log(`Using default handling for indicator: ${normalizedIndicator}`);
      if (Array.isArray(indicatorData) && indicatorData.length > 0) {
        return indicatorData[indicatorData.length - 1];
      } else if (typeof indicatorData === 'number') {
        return indicatorData;
      } else if (typeof indicatorData === 'object' && indicatorData !== null) {
        // Try to find a numeric value in the object
        for (const key in indicatorData) {
          if (Array.isArray(indicatorData[key]) && indicatorData[key].length > 0) {
            return indicatorData[key][indicatorData[key].length - 1];
          } else if (typeof indicatorData[key] === 'number') {
            return indicatorData[key];
          }
        }
      }

      // If all else fails, return a default value
      console.warn(`Could not extract a value from ${normalizedIndicator}, returning default value`);
      return 0;
  }
}

/**
 * Execute a momentum indicator block
 * @param block - The momentum indicator block to execute
 * @param polygonData - Data from Polygon API
 * @returns The momentum indicator value
 */
function executeMomentumIndicatorBlock(block: any, polygonData: PolygonData): any {
  console.log(`Executing momentum indicator block: ${block.id}`);
  console.log(`Momentum Indicator: ${block.indicator}, Period: ${block.period}, Timeframe: ${block.timeframe}`);

  const indicator = block.indicator;
  const parameters = {
    period: block.period || 14,
    timeframe: block.timeframe || 'day',
    overbought: block.overbought || 70,
    oversold: block.oversold || 30,
    smoothK: block.smoothK || 3,
    smoothD: block.smoothD || 3
  };

  // Calculate momentum indicators from historical data
  if (!polygonData.historical || !polygonData.historical.high || !polygonData.historical.low || !polygonData.historical.close) {
    throw new Error(`Insufficient historical data for momentum indicator calculation`);
  }

  const { high, low, close } = polygonData.historical;
  let indicatorValue: number;

  switch (indicator) {
    case 'rsi':
      // Use RSI data from Polygon API if available
      if (polygonData.indicators && polygonData.indicators.rsi && polygonData.indicators.rsi.length > 0) {
        indicatorValue = polygonData.indicators.rsi[polygonData.indicators.rsi.length - 1];
        console.log(`Using Polygon RSI data: ${indicatorValue}`);
      } else {
        // Fallback to manual calculation if Polygon data not available
        const rsiResult = calculateRSI(close, parameters.period);
        if (rsiResult.length > 0) {
          indicatorValue = rsiResult[rsiResult.length - 1];
          console.log(`Using calculated RSI: ${indicatorValue}`);
        } else {
          throw new Error('Failed to calculate RSI');
        }
      }
      break;

    case 'stochastic':
      const stochasticResult = calculateStochastic(high, low, close, parameters.period, parameters.smoothD);
      if (stochasticResult.k.length > 0) {
        indicatorValue = stochasticResult.k[stochasticResult.k.length - 1];
      } else {
        throw new Error('Failed to calculate Stochastic oscillator');
      }
      break;

    case 'williams_r':
      const williamsRResult = calculateWilliamsR(high, low, close, parameters.period);
      if (williamsRResult.length > 0) {
        indicatorValue = williamsRResult[williamsRResult.length - 1];
      } else {
        throw new Error('Failed to calculate Williams %R');
      }
      break;

    case 'cci':
      const cciResult = calculateCCI(high, low, close, parameters.period);
      if (cciResult.length > 0) {
        indicatorValue = cciResult[cciResult.length - 1];
      } else {
        throw new Error('Failed to calculate CCI');
      }
      break;

    default:
      throw new Error(`Unknown momentum indicator: ${indicator}`);
  }

  console.log(`Momentum indicator ${indicator} value: ${indicatorValue}`);

  return indicatorValue;
}

/**
 * Execute a moving average block
 * @param block - The moving average block to execute
 * @param polygonData - Data from Polygon API
 * @returns The moving average value
 */
function executeMovingAverageBlock(block: any, polygonData: PolygonData): any {
  console.log(`Executing moving average block: ${block.id}`);
  console.log(`Moving Average: ${block.averageType}, Period: ${block.period}, Source: ${block.source}, Timeframe: ${block.timeframe}`);

  const averageType = block.averageType || 'sma';
  const period = block.period || 20;
  const source = block.source || 'close';
  const timeframe = block.timeframe || 'day';

  // Get the source data (close, high, low, open)
  if (!polygonData.historical || !polygonData.historical[source]) {
    throw new Error(`No historical ${source} data available for moving average calculation`);
  }

  const sourceData = polygonData.historical[source];
  if (sourceData.length < period) {
    throw new Error(`Not enough data points for ${averageType.toUpperCase()}(${period}). Need ${period}, have ${sourceData.length}`);
  }

  let movingAverageValue: number;

  if (averageType === 'sma') {
    // Simple Moving Average
    const sum = sourceData.slice(-period).reduce((acc, val) => acc + val, 0);
    movingAverageValue = sum / period;
  } else if (averageType === 'ema') {
    // Exponential Moving Average
    const multiplier = 2 / (period + 1);
    let ema = sourceData[sourceData.length - period]; // Start with first value

    for (let i = sourceData.length - period + 1; i < sourceData.length; i++) {
      ema = (sourceData[i] * multiplier) + (ema * (1 - multiplier));
    }
    movingAverageValue = ema;
  } else {
    throw new Error(`Unknown moving average type: ${averageType}`);
  }

  console.log(`Moving average ${averageType.toUpperCase()}(${period}) value: ${movingAverageValue}`);

  return movingAverageValue;
}

/**
 * Execute a trend indicator block
 * @param block - The trend indicator block to execute
 * @param polygonData - Data from Polygon API
 * @returns The trend indicator value
 */
function executeTrendIndicatorBlock(block: any, polygonData: PolygonData): any {
  console.log(`Executing trend indicator block: ${block.id}`);
  console.log(`Trend Indicator: ${block.indicator}, Timeframe: ${block.timeframe}`);

  const indicator = block.indicator || 'macd';
  const timeframe = block.timeframe || 'day';
  const parameters = block.parameters || {};

  // Get historical close prices
  if (!polygonData.historical || !polygonData.historical.close) {
    throw new Error('No historical close data available for trend indicator calculation');
  }

  const closePrices = polygonData.historical.close;
  let indicatorValue: any;

  switch (indicator) {
    case 'macd':
      const fastPeriod = parameters.fastPeriod || 12;
      const slowPeriod = parameters.slowPeriod || 26;
      const signalPeriod = parameters.signalPeriod || 9;

      const macdResult = calculateMACD(closePrices, fastPeriod, slowPeriod, signalPeriod);
      if (macdResult.length > 0) {
        const latest = macdResult[macdResult.length - 1];
        indicatorValue = {
          macd: latest.macd,
          signal: latest.signal,
          histogram: latest.histogram
        };
      } else {
        throw new Error('Failed to calculate MACD');
      }
      break;

    default:
      throw new Error(`Unknown trend indicator: ${indicator}`);
  }

  console.log(`Trend indicator ${indicator} value:`, indicatorValue);

  return indicatorValue;
}

/**
 * Execute a price block
 * @param block - The price block to execute
 * @param polygonData - Data from Polygon API
 * @returns The price value
 */
function executePriceBlock(block: any, polygonData: PolygonData): any {
  const { dataPoint, lookback } = block;

  console.log(`Executing price block for dataPoint: ${dataPoint}, lookback: ${lookback}`);
  console.log(`PolygonData structure:`, Object.keys(polygonData));
  console.log(`Price data structure:`, Object.keys(polygonData.price || {}));
  console.log(`Historical data structure:`, Object.keys(polygonData.historical || {}));

  // If lookback is specified, get historical data
  if (lookback && lookback > 0) {
    if (!polygonData.historical || !polygonData.historical[dataPoint]) {
      console.error(`No historical data available for dataPoint: ${dataPoint}`);
      return 0;
    }

    const historicalArray = polygonData.historical[dataPoint];
    const index = historicalArray.length - 1 - lookback;
    if (index < 0) {
      console.error(`Not enough historical data for lookback: ${lookback}, available: ${historicalArray.length}`);
      return 0;
    }

    const value = historicalArray[index];
    console.log(`Historical price value for ${dataPoint} (lookback ${lookback}): ${value}`);
    return value;
  }

  // Otherwise, get current price data
  if (!polygonData.price || polygonData.price[dataPoint] === undefined) {
    console.error(`No current price data available for dataPoint: ${dataPoint}`);
    console.log(`Available price fields:`, Object.keys(polygonData.price || {}));
    return 0;
  }

  const value = polygonData.price[dataPoint];
  console.log(`Current price value for ${dataPoint}: ${value}`);
  return value;
}

/**
 * Get value from financial data with period awareness
 * @param financials - Financial data (new structure with quarterly/annual)
 * @param path - Path like 'income_statement.net_income_loss'
 * @param period - 'quarterly', 'annual', or 'ttm' (trailing twelve months)
 * @returns The value or null if not found
 */
function getFinancialValue(financials: any, path: string, period: 'quarterly' | 'annual' | 'ttm' = 'quarterly'): number | null {
  const [statement, metric] = path.split('.');

  console.log(`getFinancialValue called with path: ${path}, period: ${period}`);
  console.log(`Financials structure:`, Object.keys(financials || {}));

  // Handle new structured financials data
  let sourceData = null;

  if (period === 'annual' && financials?.annual) {
    sourceData = financials.annual;
    console.log(`Using annual data for ${path}`);
  } else if (period === 'quarterly' && financials?.quarterly) {
    sourceData = financials.quarterly;
    console.log(`Using quarterly data for ${path}`);
  } else if (period === 'ttm') {
    // For TTM, we'll use quarterly data and multiply by 4 for income statement items
    // or use annual data if available and more recent
    if (financials?.annual && financials?.quarterly) {
      // Use annual if it's more recent, otherwise use quarterly * 4
      sourceData = financials.annual;
      console.log(`Using annual data for TTM calculation of ${path}`);
    } else if (financials?.quarterly) {
      sourceData = financials.quarterly;
      console.log(`Using quarterly data for TTM calculation of ${path} (will multiply by 4 for income statement)`);
    } else if (financials?.annual) {
      sourceData = financials.annual;
      console.log(`Using annual data as fallback for TTM calculation of ${path}`);
    }
  } else {
    // Fallback to latest available data or old structure
    sourceData = financials?.latest || financials;
    console.log(`Using fallback data for ${path}`);
  }

  if (!sourceData || !sourceData[statement]) {
    console.error(`Financial statement not found: ${statement} for period ${period}`);
    return null;
  }

  const metricData = sourceData[statement][metric];
  if (!metricData) {
    console.error(`Metric not found in ${statement}: ${metric} for period ${period}`);
    return null;
  }

  // Return the value, handling different data structures
  let value = null;
  if (typeof metricData === 'object' && metricData.value !== undefined) {
    value = metricData.value;
  } else if (typeof metricData === 'number') {
    value = metricData;
  } else {
    console.error(`Unexpected metric data structure for ${metric}:`, metricData);
    return null;
  }

  // For TTM calculations using quarterly data, multiply income statement items by 4
  if (period === 'ttm' && sourceData === financials.quarterly && statement === 'income_statement') {
    console.log(`Converting quarterly ${metric} to TTM: ${value} * 4 = ${value * 4}`);
    value = value * 4;
  }

  return value;
}

/**
 * Calculate financial ratios and metrics
 * @param metric - The metric to calculate
 * @param polygonData - Data from Polygon API
 * @returns The calculated value
 */
function calculateFinancialMetric(metric: string, polygonData: PolygonData): number | null {
  const { financials, price } = polygonData;

  if (!financials) {
    console.error('No financials data available for calculation');
    return null;
  }

  console.log(`Calculating metric: ${metric}`);

  switch (metric) {
    // Profitability Ratios
    case 'return_on_equity': {
      // Use TTM (trailing twelve months) net income with most recent equity for accurate ROE
      const netIncomeTTM = getFinancialValue(financials, 'income_statement.net_income_loss', 'ttm');
      const equity = getFinancialValue(financials, 'balance_sheet.equity_attributable_to_parent', 'quarterly'); // Most recent equity

      console.log(`ROE Debug - Net Income TTM: ${netIncomeTTM}, Equity: ${equity}`);

      if (netIncomeTTM !== null && equity !== null && netIncomeTTM !== undefined && equity !== undefined) {
        const roe = (netIncomeTTM / equity) * 100;
        console.log(`ROE Calculation Details (TTM):`);
        console.log(`  Net Income (TTM): $${netIncomeTTM.toLocaleString()}`);
        console.log(`  Shareholders' Equity (Latest): $${equity.toLocaleString()}`);
        console.log(`  ROE = (${netIncomeTTM.toLocaleString()} ÷ ${equity.toLocaleString()}) × 100 = ${roe.toFixed(2)}%`);

        // Return an object with both the calculated value and the components
        return {
          value: roe,
          components: {
            net_income_ttm: netIncomeTTM,
            shareholders_equity: equity,
            calculation: `(${netIncomeTTM.toLocaleString()} TTM ÷ ${equity.toLocaleString()}) × 100`,
            period_note: 'Using TTM Net Income with latest Shareholders\' Equity'
          }
        };
      } else {
        console.error(`ROE calculation failed - Net Income TTM: ${netIncomeTTM}, Equity: ${equity}`);
      }
      return null;
    }

    case 'return_on_assets': {
      // Use TTM net income with most recent assets
      const netIncomeTTM = getFinancialValue(financials, 'income_statement.net_income_loss', 'ttm');
      const assets = getFinancialValue(financials, 'balance_sheet.assets', 'quarterly');
      return (netIncomeTTM && assets) ? (netIncomeTTM / assets) * 100 : null;
    }

    case 'operating_margin': {
      // Use TTM operating income and revenue for consistent period
      const operatingIncomeTTM = getFinancialValue(financials, 'income_statement.operating_income_loss', 'ttm');
      const revenueTTM = getFinancialValue(financials, 'income_statement.revenues', 'ttm');
      return (operatingIncomeTTM && revenueTTM) ? (operatingIncomeTTM / revenueTTM) * 100 : null;
    }

    case 'net_profit_margin': {
      // Use TTM net income and revenue for consistent period
      const netIncomeTTM = getFinancialValue(financials, 'income_statement.net_income_loss', 'ttm');
      const revenueTTM = getFinancialValue(financials, 'income_statement.revenues', 'ttm');
      return (netIncomeTTM && revenueTTM) ? (netIncomeTTM / revenueTTM) * 100 : null;
    }

    case 'gross_margin': {
      // Use TTM gross profit and revenue for consistent period
      const grossProfitTTM = getFinancialValue(financials, 'income_statement.gross_profit', 'ttm');
      const revenueTTM = getFinancialValue(financials, 'income_statement.revenues', 'ttm');
      return (grossProfitTTM && revenueTTM) ? (grossProfitTTM / revenueTTM) * 100 : null;
    }

    // Liquidity Ratios
    case 'current_ratio': {
      // Use most recent balance sheet data
      const currentAssets = getFinancialValue(financials, 'balance_sheet.current_assets', 'quarterly');
      const currentLiabilities = getFinancialValue(financials, 'balance_sheet.current_liabilities', 'quarterly');
      return (currentAssets && currentLiabilities) ? currentAssets / currentLiabilities : null;
    }

    // Leverage Ratios
    case 'debt_to_equity': {
      // Use most recent balance sheet data
      const longTermDebt = getFinancialValue(financials, 'balance_sheet.long_term_debt', 'quarterly');
      const equity = getFinancialValue(financials, 'balance_sheet.equity_attributable_to_parent', 'quarterly');
      return (longTermDebt && equity) ? longTermDebt / equity : null;
    }

    case 'interest_coverage_ratio': {
      // Use TTM operating income and interest expense for consistent period
      const operatingIncomeTTM = getFinancialValue(financials, 'income_statement.operating_income_loss', 'ttm');
      const interestExpenseTTM = getFinancialValue(financials, 'income_statement.interest_expense_operating', 'ttm');
      return (operatingIncomeTTM && interestExpenseTTM) ? operatingIncomeTTM / interestExpenseTTM : null;
    }

    // Cash Flow Metrics
    case 'free_cash_flow': {
      // Use TTM cash flow data for consistent period
      const operatingCashFlowTTM = getFinancialValue(financials, 'cash_flow_statement.net_cash_flow_from_operating_activities', 'ttm');
      const investingCashFlowTTM = getFinancialValue(financials, 'cash_flow_statement.net_cash_flow_from_investing_activities', 'ttm');
      // Estimate CapEx as absolute value of investing cash flow (simplified)
      const capex = investingCashFlowTTM ? Math.abs(investingCashFlowTTM) : 0;
      return operatingCashFlowTTM ? operatingCashFlowTTM - capex : null;
    }

    case 'capital_expenditures': {
      // Use TTM investing cash flow
      const investingCashFlowTTM = getFinancialValue(financials, 'cash_flow_statement.net_cash_flow_from_investing_activities', 'ttm');
      return investingCashFlowTTM ? Math.abs(investingCashFlowTTM) : null;
    }

    // Per Share Metrics
    case 'book_value_per_share': {
      // Use most recent balance sheet equity and shares outstanding
      const equity = getFinancialValue(financials, 'balance_sheet.equity_attributable_to_parent', 'quarterly');
      const shares = getFinancialValue(financials, 'income_statement.basic_average_shares', 'quarterly');
      return (equity && shares) ? equity / shares : null;
    }

    // Valuation Ratios (require market data)
    case 'price_to_earnings': {
      // Use TTM EPS for P/E calculation
      const epsTTM = getFinancialValue(financials, 'income_statement.basic_earnings_per_share', 'ttm');
      const currentPrice = price?.current;
      return (epsTTM && currentPrice) ? currentPrice / epsTTM : null;
    }

    // Efficiency Ratios
    case 'inventory_turnover': {
      // Use TTM cost of revenue with most recent inventory
      const costOfRevenueTTM = getFinancialValue(financials, 'income_statement.cost_of_revenue', 'ttm');
      const inventory = getFinancialValue(financials, 'balance_sheet.inventory', 'quarterly');
      return (costOfRevenueTTM && inventory) ? costOfRevenueTTM / inventory : null;
    }

    // Direct metrics (just pass through)
    case 'cash_and_equivalents': {
      // Use most recent cash position
      return getFinancialValue(financials, 'balance_sheet.cash', 'quarterly');
    }

    default:
      console.error(`Unknown calculated metric: ${metric}`);
      return null;
  }
}

/**
 * Execute a fundamental block
 * @param block - The fundamental block to execute
 * @param polygonData - Data from Polygon API
 * @returns The fundamental value
 */
function executeFundamentalBlock(block: any, polygonData: PolygonData): any {
  const { metric, statement, period } = block;

  console.log(`Executing fundamental block for metric: ${metric}, statement: ${statement}, period: ${period}`);

  // Check if this is a calculated metric
  if (statement === 'calculated') {
    return calculateFinancialMetric(metric, polygonData);
  }

  // Handle direct financial statement metrics
  if (!polygonData.financials) {
    console.error('No financials data available');
    return null;
  }

  // Navigate to the correct statement (balance_sheet, income_statement, cash_flow_statement, comprehensive_income)
  const statementData = polygonData.financials[statement || 'income_statement'];
  if (!statementData) {
    console.error(`Financial statement not found: ${statement}`);
    return null;
  }

  // Get the metric from the statement
  const metricData = statementData[metric];
  if (!metricData) {
    console.error(`Metric not found in ${statement}: ${metric}`);
    return null;
  }

  // Return the value, handling different data structures
  if (typeof metricData === 'object' && metricData.value !== undefined) {
    return metricData.value;
  } else if (typeof metricData === 'number') {
    return metricData;
  } else {
    console.error(`Unexpected metric data structure for ${metric}:`, metricData);
    return null;
  }
}

/**
 * Execute a condition block
 * @param block - The condition block to execute
 * @param blockResults - Results of previously executed blocks
 * @param polygonData - Data from Polygon API
 * @param inheritedValue - Value passed from a previous condition block in the chain
 * @returns The result of the condition (true or false)
 */
function executeConditionBlock(
  block: any,
  blockResults: Map<string, any>,
  polygonData: PolygonData,
  inheritedValue?: any
): boolean {
  const { operator, inputConnections, compareValue } = block;

  console.log(`Executing condition block with operator: ${operator}`);
  console.log(`Compare value: ${compareValue}`);
  console.log(`Input connections: ${inputConnections.join(', ')}`);
  console.log(`Inherited value: ${inheritedValue}`);

  let valueToCompare: any;
  let comparisonValue = compareValue;
  let inputValues: any[] = []; // Initialize inputValues to avoid undefined errors

  // Always get input values from connected blocks first
  inputValues = inputConnections.map(id => {
    if (!blockResults.has(id)) {
      console.error(`Block result not found for ID: ${id} - this block may not have been executed yet`);
      // For AND/OR operations, missing inputs should be treated as false
      // For comparison operations, this is a real error
      if (operator === 'and' || operator === 'or') {
        console.log(`Treating missing input ${id} as false for ${operator.toUpperCase()} operation`);
        return false;
      } else {
        throw new Error(`Block result not found for ID: ${id}`);
      }
    }
    const value = blockResults.get(id);
    console.log(`Input value from block ${id}: ${value}`);
    return value;
  });

  // For AND/OR/NOT operations, we evaluate the input values directly as boolean logic
  if (operator === 'and' || operator === 'or' || operator === 'not') {
    console.log(`${operator.toUpperCase()} operation with ${inputValues.length} input values: [${inputValues.join(', ')}]`);

    // For logical operations, we don't need valueToCompare or compareValue
    // We'll evaluate the inputValues directly in the switch statement
  } else {
    // For comparison operations (>, <, ==, etc.), use inherited value if available, otherwise first input value
    if (inheritedValue !== undefined) {
      valueToCompare = inheritedValue;
      console.log(`Using inherited value: ${valueToCompare}`);
    } else {
      valueToCompare = inputValues[0];
    }

    // Handle calculated metrics that return objects with value and components
    if (valueToCompare && typeof valueToCompare === 'object' && valueToCompare.value !== undefined) {
      console.log(`Value is a calculated metric with components:`, valueToCompare.components);
      valueToCompare = valueToCompare.value;
    }
  }

  // Ensure values are numeric for comparison operations
  if (typeof valueToCompare !== 'number') {
    console.warn(`Value to compare is not a number: ${valueToCompare}, attempting to convert`);

    // Special handling for boolean values
    if (valueToCompare === false) {
      console.log("Value is boolean false, converting to 0");
      valueToCompare = 0;


    } else {
      // For non-boolean values, try normal conversion
      const numericValue = Number(valueToCompare);
      if (!isNaN(numericValue)) {
        valueToCompare = numericValue;
      } else {
        console.error(`Could not convert value to number: ${valueToCompare}`);
        // Default to a value that will likely cause the condition to fail
        valueToCompare = 0;
      }
    }
  }

  if (typeof comparisonValue !== 'number') {
    console.warn(`Comparison value is not a number: ${comparisonValue}, attempting to convert`);
    const numericValue = Number(comparisonValue);
    if (!isNaN(numericValue)) {
      comparisonValue = numericValue;
    } else {
      console.error(`Could not convert comparison value to number: ${comparisonValue}`);
      // Default to a value that will likely cause the condition to fail
      comparisonValue = 0;
    }
  }

  console.log(`Value to compare (converted): ${valueToCompare}`);
  console.log(`Comparison value (converted): ${comparisonValue}`);

  console.log(`Evaluating condition: ${valueToCompare} ${operator} ${comparisonValue}`);



  // Evaluate the condition based on the operator
  let result;
  switch (operator) {
    case '>':
      result = valueToCompare > comparisonValue;
      console.log(`Evaluating ${valueToCompare} > ${comparisonValue} = ${result}`);
      break;
    case '<':
      result = valueToCompare < comparisonValue;
      console.log(`Evaluating ${valueToCompare} < ${comparisonValue} = ${result}`);
      break;
    case '==':
      result = valueToCompare === comparisonValue;
      console.log(`Evaluating ${valueToCompare} == ${comparisonValue} = ${result}`);
      break;
    case '>=':
      result = valueToCompare >= comparisonValue;
      console.log(`Evaluating ${valueToCompare} >= ${comparisonValue} = ${result}`);


      break;
    case '<=':
      result = valueToCompare <= comparisonValue;
      console.log(`Evaluating ${valueToCompare} <= ${comparisonValue} = ${result}`);
      break;
    case '!=':
      result = valueToCompare !== comparisonValue;
      console.log(`Evaluating ${valueToCompare} != ${comparisonValue} = ${result}`);
      break;
    case 'between':
      // For between, we need a third value
      const upperBound = (inputValues.length > 1 ? inputValues[1] : null) || comparisonValue + 10; // Default upper bound
      result = valueToCompare >= comparisonValue && valueToCompare <= upperBound;
      console.log(`Evaluating ${comparisonValue} <= ${valueToCompare} <= ${upperBound} = ${result}`);
      break;
    case 'and':
      // AND operation: ALL inputs must be true
      if (inputValues.length === 0) {
        console.error('AND operation with no input values');
        result = false;
      } else {
        // Convert all input values to boolean and check if ALL are true
        const booleanInputs = inputValues.map(val => !!val);
        result = booleanInputs.every(val => val === true);
        console.log(`AND operation: inputs [${inputValues.join(', ')}] → booleans [${booleanInputs.join(', ')}] → result: ${result}`);
      }
      break;
    case 'or':
      // OR operation: ANY input can be true
      if (inputValues.length === 0) {
        console.error('OR operation with no input values');
        result = false;
      } else {
        // Convert all input values to boolean and check if ANY are true
        const booleanInputs = inputValues.map(val => !!val);
        result = booleanInputs.some(val => val === true);
        console.log(`OR operation: inputs [${inputValues.join(', ')}] → booleans [${booleanInputs.join(', ')}] → result: ${result}`);
      }
      break;
    case 'not':
      result = !valueToCompare;
      console.log(`Evaluating NOT ${valueToCompare} = ${result}`);
      break;
    default:
      throw new Error(`Unknown operator: ${operator}`);
  }

  console.log(`Condition result: ${result}`);

  // Log which path will be taken
  if (result) {
    console.log(`Taking TRUE path to block: ${block.trueConnection}`);
  } else {
    console.log(`Taking FALSE path to block: ${block.falseConnection}`);
  }

  return result;
}

/**
 * Execute a comparison block (compare two values)
 * @param block - The comparison block to execute
 * @param blockResults - Results of previously executed blocks
 * @param polygonData - Data from Polygon API
 * @returns The result of the comparison (true or false)
 */
function executeComparisonBlock(
  block: any,
  blockResults: Map<string, any>,
  polygonData: PolygonData
): boolean {
  const { operator, inputConnections } = block;

  console.log(`Executing comparison block with operator: ${operator}`);
  console.log(`Input connections: ${inputConnections.join(', ')}`);

  // Comparison blocks require exactly 2 inputs
  if (!inputConnections || inputConnections.length !== 2) {
    throw new Error(`Comparison block requires exactly 2 input connections, got ${inputConnections?.length || 0}`);
  }

  // Get values from both input blocks
  const valueA = blockResults.get(inputConnections[0]);
  const valueB = blockResults.get(inputConnections[1]);

  if (valueA === undefined) {
    throw new Error(`Block result not found for input A (ID: ${inputConnections[0]})`);
  }
  if (valueB === undefined) {
    throw new Error(`Block result not found for input B (ID: ${inputConnections[1]})`);
  }

  console.log(`Comparing: A=${valueA} ${operator} B=${valueB}`);

  // Convert values to numbers for comparison
  let numericValueA = Number(valueA);
  let numericValueB = Number(valueB);

  if (isNaN(numericValueA)) {
    console.warn(`Value A is not a number: ${valueA}, attempting to convert`);
    numericValueA = 0;
  }
  if (isNaN(numericValueB)) {
    console.warn(`Value B is not a number: ${valueB}, attempting to convert`);
    numericValueB = 0;
  }

  console.log(`Numeric comparison: A=${numericValueA} ${operator} B=${numericValueB}`);

  // Perform the comparison
  let result: boolean;
  switch (operator) {
    case '>':
      result = numericValueA > numericValueB;
      break;
    case '<':
      result = numericValueA < numericValueB;
      break;
    case '>=':
      result = numericValueA >= numericValueB;
      break;
    case '<=':
      result = numericValueA <= numericValueB;
      break;
    case '==':
      result = numericValueA === numericValueB;
      break;
    case '!=':
      result = numericValueA !== numericValueB;
      break;
    default:
      throw new Error(`Unknown comparison operator: ${operator}`);
  }

  console.log(`Comparison result: ${numericValueA} ${operator} ${numericValueB} = ${result}`);

  // Log which path will be taken
  if (result) {
    console.log(`Taking TRUE path to block: ${block.trueConnection}`);
  } else {
    console.log(`Taking FALSE path to block: ${block.falseConnection}`);
  }

  return result;
}

/**
 * Execute a trigger block
 * @param block - The trigger block to execute
 * @param blockResults - Results of previously executed blocks
 * @returns The trigger result
 */
function executeTriggerBlock(block: any, blockResults: Map<string, any>): any {
  const { signal, confidence, inputConnections } = block;

  console.log(`Executing trigger block with signal: ${signal}, confidence: ${confidence}`);
  console.log(`Input connections: ${JSON.stringify(inputConnections)}`);

  // Start with the base confidence
  let adjustedConfidence = confidence;
  let bullishAdjustment = 0;
  let bearishAdjustment = 0;

  // Check for confidence boost blocks in the input connections
  if (inputConnections && inputConnections.length > 0) {
    inputConnections.forEach((id: string) => {
      if (blockResults.has(id)) {
        const inputResult = blockResults.get(id);

        // Check if this is a confidence boost block result
        if (inputResult && typeof inputResult === 'object') {
          if (inputResult.type === 'bullish_confidence_boost') {
            bullishAdjustment += inputResult.adjustment;
            console.log(`Found bullish confidence boost: +${inputResult.adjustment}%`);
          } else if (inputResult.type === 'bearish_confidence_boost') {
            bearishAdjustment += inputResult.adjustment;
            console.log(`Found bearish confidence boost: +${inputResult.adjustment}%`);
          } else if (inputResult.boostType === 'bullish') {
            bullishAdjustment += inputResult.adjustment;
            console.log(`Found unified bullish confidence boost: +${inputResult.adjustment}%`);
          } else if (inputResult.boostType === 'bearish') {
            bearishAdjustment += inputResult.adjustment;
            console.log(`Found unified bearish confidence boost: +${inputResult.adjustment}%`);
          } else if (inputResult.boostType === 'neutral') {
            // Neutral boosts don't affect bullish/bearish but could be used for other logic
            console.log(`Found unified neutral confidence boost: +${inputResult.adjustment}%`);
          }
        }
      }
    });
  }

  // Apply confidence adjustments based on signal type
  if (signal === 'bullish') {
    adjustedConfidence += bullishAdjustment - bearishAdjustment;
  } else if (signal === 'bearish') {
    adjustedConfidence += bearishAdjustment - bullishAdjustment;
  }

  // Ensure confidence stays within 0-100 range
  adjustedConfidence = Math.max(0, Math.min(100, adjustedConfidence));

  console.log(`Confidence adjustments: bullish +${bullishAdjustment}%, bearish +${bearishAdjustment}%`);
  console.log(`Final confidence: ${confidence} → ${adjustedConfidence}`);

  // If there are no input connections, this trigger was reached through a condition path
  // In this case, we should return the signal and confidence as-is
  if (!inputConnections || inputConnections.length === 0) {
    console.log(`Trigger block has no input connections, returning signal: ${signal}`);
    const result = {
      signal: signal,
      confidence: adjustedConfidence
    };
    console.log(`Trigger result: signal=${result.signal}, confidence=${result.confidence}`);
    return result;
  }

  // IMPORTANT: When a trigger block is reached via any connection path (true or false),
  // it should ALWAYS execute its configured signal. The routing logic already determined
  // that this trigger should fire by directing execution to this block.

  console.log(`Trigger block reached via connection path, returning configured signal: ${signal}`);
  const result = {
    signal: signal,
    confidence: adjustedConfidence
  };
  console.log(`Trigger result: signal=${result.signal}, confidence=${result.confidence}`);
  return result;
}

/**
 * Execute an operator block
 * @param block - The operator block to execute
 * @param blockResults - Results of previously executed blocks
 * @returns The result of the operation
 */
function executeOperatorBlock(block: any, blockResults: Map<string, any>): any {
  const { operation, inputConnections } = block;

  // Get input values
  const inputValues = inputConnections.map(id => {
    if (!blockResults.has(id)) {
      throw new Error(`Block result not found for ID: ${id}`);
    }
    return blockResults.get(id);
  });

  // Perform the operation
  switch (operation) {
    case 'add':
      return inputValues.reduce((sum, val) => sum + val, 0);
    case 'subtract':
      return inputValues.reduce((result, val, index) =>
        index === 0 ? val : result - val, 0);
    case 'multiply':
      return inputValues.reduce((product, val) => product * val, 1);
    case 'divide':
      return inputValues.reduce((result, val, index) =>
        index === 0 ? val : result / val, 0);
    case 'average':
      return inputValues.reduce((sum, val) => sum + val, 0) / inputValues.length;
    case 'min':
      return Math.min(...inputValues);
    case 'max':
      return Math.max(...inputValues);
    case 'abs':
      return Math.abs(inputValues[0]);
    default:
      throw new Error(`Unknown operation: ${operation}`);
  }
}

/**
 * Execute a bullish confidence boost block
 * @param block - The bullish confidence boost block to execute
 * @param blockResults - Results of previously executed blocks
 * @returns Confidence adjustment object
 */
function executeBullishConfidenceBoostBlock(block: any, blockResults: Map<string, any>): any {
  const { percentage } = block;

  console.log(`Executing bullish confidence boost block with +${percentage}% boost`);

  // Return a confidence adjustment object that can be used by trigger blocks
  const result = {
    type: 'bullish_confidence_boost',
    percentage: percentage,
    adjustment: percentage // Positive adjustment for bullish confidence
  };

  console.log(`Bullish confidence boost result: +${percentage}%`);
  return result;
}

/**
 * Execute a bearish confidence boost block
 * @param block - The bearish confidence boost block to execute
 * @param blockResults - Results of previously executed blocks
 * @returns Confidence adjustment object
 */
function executeBearishConfidenceBoostBlock(block: any, blockResults: Map<string, any>): any {
  const { percentage } = block;

  console.log(`Executing bearish confidence boost block with +${percentage}% boost`);

  // Return a confidence adjustment object that can be used by trigger blocks
  const result = {
    type: 'bearish_confidence_boost',
    percentage: percentage,
    adjustment: percentage // Positive adjustment for bearish confidence (which reduces bullish)
  };

  console.log(`Bearish confidence boost result: +${percentage}%`);
  return result;
}

/**
 * Execute a unified confidence boost block
 * @param block - The confidence boost block to execute
 * @param blockResults - Results of previously executed blocks
 * @returns Confidence adjustment object
 */
function executeConfidenceBoostBlock(block: any, blockResults: Map<string, any>): any {
  const { boostType, percentage, inputConnections } = block;

  console.log(`Executing ${boostType} confidence boost block with +${percentage}% boost`);

  // Check if this confidence boost block has input connections
  if (inputConnections && inputConnections.length > 0) {
    console.log(`Confidence boost block has ${inputConnections.length} input connections`);

    // Log input data for debugging
    const inputData = inputConnections.map((id: string) => {
      if (!blockResults.has(id)) {
        console.warn(`Block result not found for ID: ${id}`);
        return null;
      }
      return blockResults.get(id);
    });

    console.log(`Confidence boost input data:`, inputData);
  } else {
    console.log('Confidence boost block has no input connections');
  }

  // Return a confidence adjustment object that can be used by trigger blocks or final signal generation
  const result = {
    type: `${boostType}_confidence_boost`,
    boostType: boostType,
    percentage: percentage,
    adjustment: percentage // Positive adjustment for the specified boost type
  };

  console.log(`${boostType} confidence boost result: +${percentage}%`);
  return result;
}

/**
 * Execute a percentage up block (DEPRECATED - replaced by confidence boost blocks)
 * @param block - The percentage up block to execute
 * @param polygonData - Data from Polygon API
 * @returns Boolean indicating if the percentage threshold was met
 */
function executePercentageUpBlock(block: any, polygonData: PolygonData): boolean {
  const { percentage } = block;

  // Get current price and calculate previous close from historical data
  const currentPrice = polygonData.price.current || polygonData.price.close;
  const historicalCloses = polygonData.historical.close;

  if (!currentPrice || !historicalCloses || historicalCloses.length < 2) {
    console.warn('Missing price data for percentage up block');
    return false;
  }

  // Use the second-to-last close as previous close (last close might be current)
  const previousClose = historicalCloses[historicalCloses.length - 2];

  if (!previousClose) {
    console.warn('Missing previous close data for percentage up block');
    return false;
  }

  // Calculate percentage change
  const percentageChange = ((currentPrice - previousClose) / previousClose) * 100;

  console.log(`Percentage Up Block: Current: ${currentPrice}, Previous: ${previousClose}, Change: ${percentageChange.toFixed(2)}%, Threshold: +${percentage}%`);

  // Return true if the percentage change meets or exceeds the threshold
  return percentageChange >= percentage;
}

/**
 * Execute an AND logic block
 * @param block - The AND block to execute
 * @param blockResults - Results of previously executed blocks
 * @returns Boolean indicating if ALL inputs are true
 */
function executeAndBlock(block: any, blockResults: Map<string, any>): boolean {
  const { inputConnections } = block;

  console.log(`Executing AND block with ${inputConnections.length} inputs`);

  // Get input values from connected blocks
  const inputValues = inputConnections.map((id: string) => {
    if (!blockResults.has(id)) {
      console.error(`Block result not found for ID: ${id} - treating as false for AND operation`);
      return false;
    }
    const value = blockResults.get(id);
    console.log(`AND input from block ${id}: ${value}`);
    return value;
  });

  // Convert all input values to boolean and check if ALL are true
  const booleanInputs = inputValues.map(val => !!val);
  const result = booleanInputs.every(val => val === true);

  console.log(`AND operation: inputs [${inputValues.join(', ')}] → booleans [${booleanInputs.join(', ')}] → result: ${result}`);

  return result;
}

/**
 * Execute an OR logic block
 * @param block - The OR block to execute
 * @param blockResults - Results of previously executed blocks
 * @returns Boolean indicating if ANY input is true
 */
function executeOrBlock(block: any, blockResults: Map<string, any>): boolean {
  const { inputConnections } = block;

  console.log(`Executing OR block with ${inputConnections.length} inputs`);

  // Get input values from connected blocks
  const inputValues = inputConnections.map((id: string) => {
    if (!blockResults.has(id)) {
      console.error(`Block result not found for ID: ${id} - treating as false for OR operation`);
      return false;
    }
    const value = blockResults.get(id);
    console.log(`OR input from block ${id}: ${value}`);
    return value;
  });

  // Convert all input values to boolean and check if ANY are true
  const booleanInputs = inputValues.map(val => !!val);
  const result = booleanInputs.some(val => val === true);

  console.log(`OR operation: inputs [${inputValues.join(', ')}] → booleans [${booleanInputs.join(', ')}] → result: ${result}`);

  return result;
}

/**
 * Execute a candle pattern block
 * @param block - The candle pattern block to execute
 * @param blockResults - Results of previously executed blocks
 * @param polygonData - Data from Polygon API
 * @returns Pattern detection result
 */
function executeCandlePatternBlock(
  block: any,
  blockResults: Map<string, any>,
  polygonData: PolygonData
): any {
  const { pattern, timeframe, inputConnections } = block;

  console.log(`Executing candle pattern block: pattern=${pattern}, timeframe=${timeframe}`);

  // Validate that we have price data input
  if (!inputConnections || inputConnections.length === 0) {
    throw new Error('Candle pattern block requires price data input connection');
  }

  // Get input data from connected blocks (should be price data)
  const inputData = inputConnections.map((id: string) => {
    if (!blockResults.has(id)) {
      throw new Error(`Block result not found for ID: ${id}`);
    }
    return blockResults.get(id);
  });

  console.log(`Candle pattern input data:`, inputData);

  // Use the existing detectCandlePattern function
  const patternDetected = detectCandlePattern(polygonData, { pattern, timeframe });

  console.log(`Pattern detection result: ${patternDetected}`);

  // For specific pattern mode (not 'any'), return boolean
  if (pattern !== 'any') {
    return patternDetected === 1; // Convert 1/0 to true/false
  }

  // For 'any' pattern mode, we need to determine the pattern type
  // This requires enhanced pattern detection that returns pattern details
  const detectedPatterns = detectCandlePatterns(
    convertToCandles(polygonData).slice(-10),
    'any'
  );

  if (detectedPatterns.length === 0) {
    return { type: 'neutral', detected: false, patterns: [] };
  }

  // Get the most recent pattern
  const recentPattern = detectedPatterns[detectedPatterns.length - 1];

  // Determine if the pattern is bullish, bearish, or neutral
  let patternType = 'neutral';
  if (recentPattern.bullish === true) {
    patternType = 'bullish';
  } else if (recentPattern.bullish === false) {
    patternType = 'bearish';
  } else {
    // For patterns with bullish: null (like doji), they are neutral but still detected
    patternType = 'neutral';
  }

  console.log(`Detected pattern type: ${patternType} (${recentPattern.pattern})`);

  return {
    type: patternType,
    detected: true, // Always true when patterns are found, regardless of type
    pattern: recentPattern.pattern,
    patterns: detectedPatterns.map(p => ({
      name: p.pattern,
      type: p.bullish === true ? 'bullish' : p.bullish === false ? 'bearish' : 'neutral',
      position: p.position
    }))
  };
}

/**
 * Execute a chart pattern block
 * @param block - The chart pattern block to execute
 * @param blockResults - Results of previously executed blocks
 * @param polygonData - Data from Polygon API
 * @returns Chart pattern detection result
 */
async function executeChartPatternBlock(
  block: any,
  blockResults: Map<string, any>,
  polygonData: PolygonData
): Promise<any> {
  const { pattern, timeframe, lookbackPeriod = 50, minPatternSize = 10, inputConnections } = block;

  console.log(`Executing chart pattern block: pattern=${pattern}, timeframe=${timeframe}, lookback=${lookbackPeriod}`);

  // Chart pattern blocks work independently using polygon data
  // Input connections are optional and not used for pattern analysis
  if (inputConnections && inputConnections.length > 0) {
    console.log(`Chart pattern block has ${inputConnections.length} input connections (not used for analysis)`);

    // Log input data for debugging but don't use it
    const inputData = inputConnections.map((id: string) => {
      if (!blockResults.has(id)) {
        console.warn(`Block result not found for ID: ${id}`);
        return null;
      }
      return blockResults.get(id);
    });

    console.log(`Chart pattern input data (for reference only):`, inputData);
  } else {
    console.log('Chart pattern block has no input connections (using polygon data directly)');
  }

  // Convert polygon data to candles format for chart pattern analysis
  const candles = convertToCandles(polygonData);

  // Use only the lookback period for analysis
  const analysisCandles = candles.slice(-lookbackPeriod);

  if (analysisCandles.length < minPatternSize) {
    console.log(`Not enough candles for chart pattern analysis: ${analysisCandles.length} < ${minPatternSize}`);
    if (pattern === 'any') {
      return { type: 'neutral', detected: false, patterns: [] };
    } else {
      return false;
    }
  }

  console.log(`Analyzing ${analysisCandles.length} candles for chart patterns`);

  try {
    // Import chart pattern analysis functions dynamically
    console.log('Importing chart pattern analysis functions...');
    const { analyzeChartPatterns } = await import('../aura/chart-patterns.ts');
    console.log('Chart pattern analysis functions imported successfully');

    // Detect chart patterns with timeout protection
    console.log('Starting chart pattern analysis...');
    const detectedPatterns = analyzeChartPatterns(analysisCandles);
    console.log('Chart pattern analysis completed');

    console.log(`Detected ${detectedPatterns.length} chart patterns:`, detectedPatterns.map(p => p.pattern));

    // For specific pattern mode (not 'any'), check if the specific pattern was found
    if (pattern !== 'any') {
      const specificPatternFound = detectedPatterns.some(p => p.pattern === pattern);
      console.log(`Specific pattern '${pattern}' found: ${specificPatternFound}`);
      return specificPatternFound;
    }

    // For 'any' pattern mode, determine the most significant pattern
    if (detectedPatterns.length === 0) {
      console.log('No chart patterns detected, returning neutral result');
      return { type: 'neutral', detected: false, patterns: [] };
    }

    // Find the most recent and significant pattern
    const recentPattern = detectedPatterns
      .filter(p => p.endIndex >= analysisCandles.length - 5) // Recent patterns only
      .sort((a, b) => {
        // Sort by significance and recency
        const significanceOrder = { 'strong': 3, 'moderate': 2, 'weak': 1 };
        const aScore = (significanceOrder[a.significance] || 1) + (a.endIndex / analysisCandles.length);
        const bScore = (significanceOrder[b.significance] || 1) + (b.endIndex / analysisCandles.length);
        return bScore - aScore;
      })[0];

    if (!recentPattern) {
      console.log('No recent chart patterns found, returning neutral result');
      return { type: 'neutral', detected: false, patterns: [] };
    }

    // Determine pattern type based on bullish property
    let patternType: string;
    if (recentPattern.bullish === true) {
      patternType = 'bullish';
    } else if (recentPattern.bullish === false) {
      patternType = 'bearish';
    } else {
      patternType = 'neutral';
    }

    console.log(`Most significant chart pattern: ${recentPattern.pattern} (${patternType})`);

    const result = {
      type: patternType,
      detected: true,
      pattern: recentPattern.pattern,
      significance: recentPattern.significance,
      patterns: detectedPatterns.map(p => ({
        name: p.pattern,
        type: p.bullish === true ? 'bullish' : p.bullish === false ? 'bearish' : 'neutral',
        significance: p.significance,
        startIndex: p.startIndex,
        endIndex: p.endIndex
      }))
    };

    console.log('Chart pattern block result:', JSON.stringify(result, null, 2));
    return result;

  } catch (error) {
    console.error('Error in chart pattern analysis:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name
    });

    // Fallback to neutral result
    const fallbackResult = pattern === 'any'
      ? { type: 'neutral', detected: false, patterns: [] }
      : false;

    console.log('Returning fallback result due to error:', fallbackResult);
    return fallbackResult;
  }
}

// Helper function to convert polygon data to candles format
function convertToCandles(polygonData: PolygonData): any[] {
  const historical = polygonData.historical;
  if (!historical || !historical.open || !historical.high || !historical.low || !historical.close) {
    throw new Error('Historical OHLC data not available for candle pattern detection');
  }

  const candles: any[] = [];
  const length = historical.close.length;

  for (let i = 0; i < length; i++) {
    candles.push({
      date: new Date().toISOString(), // We don't have dates in polygon data structure
      open: historical.open[i],
      high: historical.high[i],
      low: historical.low[i],
      close: historical.close[i],
      volume: historical.volume?.[i] || 0
    });
  }

  return candles;
}

/**
 * Execute a console log block (development only)
 * @param block - The console log block to execute
 * @param blockResults - Results of previously executed blocks
 * @returns The input value (pass-through)
 */
function executeConsoleLogBlock(block: any, blockResults: Map<string, any>): any {
  const { message, logValue, inputConnections } = block;

  // Check if we're in development environment
  const isDevelopment = Deno.env.get('ENVIRONMENT') === 'development' ||
                       Deno.env.get('DENO_ENV') === 'development' ||
                       Deno.env.get('NODE_ENV') === 'development';

  if (!isDevelopment) {
    console.warn('Console log block is disabled in production environment');
    // In production, just pass through the input value without logging
    if (inputConnections && inputConnections.length > 0) {
      const inputId = inputConnections[0];
      return blockResults.get(inputId) || null;
    }
    return null;
  }

  // Development environment - execute console logging
  console.log(`=== CONSOLE LOG BLOCK ===`);
  if (message) {
    console.log(`Message: ${message}`);
  }

  // Log input values if requested
  if (logValue && inputConnections && inputConnections.length > 0) {
    inputConnections.forEach((id: string, index: number) => {
      const inputValue = blockResults.get(id);
      console.log(`Input ${index + 1} (Block ${id}): ${JSON.stringify(inputValue)}`);
    });
  }

  console.log(`=== END CONSOLE LOG ===`);

  // Return the first input value as pass-through
  if (inputConnections && inputConnections.length > 0) {
    const inputId = inputConnections[0];
    return blockResults.get(inputId) || null;
  }

  return null;
}

/**
 * Execute a stock sentiment block
 * @param block - The stock sentiment block to execute
 * @param polygonData - Data from Polygon API
 * @returns Sentiment analysis result with most common sentiment
 */
async function executeStockSentimentBlock(block: any, polygonData: PolygonData): Promise<any> {
  const { articleLimit = 10, daysBack = 7 } = block;

  console.log(`Executing stock sentiment block: articleLimit=${articleLimit}, daysBack=${daysBack}`);

  // Get the symbol from polygon data
  const symbol = polygonData.symbol;
  if (!symbol) {
    console.error('No symbol available for sentiment analysis');
    return { sentiment: 'neutral', confidence: 0, articles: [] };
  }

  try {
    // Get Polygon API key from environment
    const apiKey = Deno.env.get('POLYGON_API_KEY');
    if (!apiKey) {
      console.error('Polygon API key not found');
      return { sentiment: 'neutral', confidence: 0, articles: [] };
    }

    // Calculate date range
    const endDate = new Date().toISOString().split('T')[0];
    const startDate = new Date(Date.now() - daysBack * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

    // Construct the Polygon News API URL
    const url = `https://api.polygon.io/v2/reference/news?ticker=${symbol}&published_utc.gte=${startDate}&published_utc.lte=${endDate}&order=desc&limit=${articleLimit}&apiKey=${apiKey}`;

    console.log(`Fetching news from Polygon API for ${symbol}`);

    const response = await fetch(url);
    if (!response.ok) {
      console.error(`Polygon News API error: ${response.status} ${response.statusText}`);
      return { sentiment: 'neutral', confidence: 0, articles: [] };
    }

    const data = await response.json();

    if (!data.results || !Array.isArray(data.results) || data.results.length === 0) {
      console.log(`No news articles found for ${symbol}`);
      return { sentiment: 'neutral', confidence: 0, articles: [] };
    }

    console.log(`Found ${data.results.length} articles for ${symbol}`);

    // Extract sentiment from articles
    const sentiments: string[] = [];
    const articles = data.results.map((article: any) => {
      // Extract sentiment from insights if available
      let sentiment = 'neutral';
      if (article.insights && Array.isArray(article.insights)) {
        for (const insight of article.insights) {
          if (insight.sentiment) {
            sentiment = insight.sentiment.toLowerCase();
            break;
          }
        }
      }

      // Map sentiment values to our standard format
      if (sentiment === 'positive') sentiment = 'bullish';
      if (sentiment === 'negative') sentiment = 'bearish';
      if (!['bullish', 'bearish', 'neutral'].includes(sentiment)) {
        sentiment = 'neutral';
      }

      sentiments.push(sentiment);

      return {
        title: article.title,
        sentiment: sentiment,
        published_utc: article.published_utc,
        article_url: article.article_url,
        sentiment_reasoning: article.insights?.[0]?.sentiment_reasoning || ''
      };
    });

    // Count sentiment occurrences
    const sentimentCounts = {
      bullish: sentiments.filter(s => s === 'bullish').length,
      bearish: sentiments.filter(s => s === 'bearish').length,
      neutral: sentiments.filter(s => s === 'neutral').length
    };

    console.log(`Sentiment counts for ${symbol}:`, sentimentCounts);

    // Determine the most common sentiment
    let mostCommonSentiment = 'neutral';
    let maxCount = sentimentCounts.neutral;

    if (sentimentCounts.bullish > maxCount) {
      mostCommonSentiment = 'bullish';
      maxCount = sentimentCounts.bullish;
    }

    if (sentimentCounts.bearish > maxCount) {
      mostCommonSentiment = 'bearish';
      maxCount = sentimentCounts.bearish;
    }

    // Calculate confidence based on how dominant the sentiment is
    const totalArticles = sentiments.length;
    const confidence = totalArticles > 0 ? Math.round((maxCount / totalArticles) * 100) : 0;

    console.log(`Most common sentiment for ${symbol}: ${mostCommonSentiment} (${confidence}% confidence)`);

    return {
      sentiment: mostCommonSentiment,
      confidence: confidence,
      articles: articles,
      sentimentCounts: sentimentCounts,
      totalArticles: totalArticles
    };

  } catch (error) {
    console.error(`Error fetching sentiment for ${symbol}:`, error);
    return { sentiment: 'neutral', confidence: 0, articles: [] };
  }
}

/**
 * Execute a percentage down block
 * @param block - The percentage down block to execute
 * @param polygonData - Data from Polygon API
 * @returns Boolean indicating if the percentage threshold was met
 */
function executePercentageDownBlock(block: any, polygonData: PolygonData): boolean {
  const { percentage } = block;

  // Get current price and calculate previous close from historical data
  const currentPrice = polygonData.price.current || polygonData.price.close;
  const historicalCloses = polygonData.historical.close;

  if (!currentPrice || !historicalCloses || historicalCloses.length < 2) {
    console.warn('Missing price data for percentage down block');
    return false;
  }

  // Use the second-to-last close as previous close (last close might be current)
  const previousClose = historicalCloses[historicalCloses.length - 2];

  if (!previousClose) {
    console.warn('Missing previous close data for percentage down block');
    return false;
  }

  // Calculate percentage change
  const percentageChange = ((currentPrice - previousClose) / previousClose) * 100;

  console.log(`Percentage Down Block: Current: ${currentPrice}, Previous: ${previousClose}, Change: ${percentageChange.toFixed(2)}%, Threshold: -${percentage}%`);

  // Return true if the percentage change meets or exceeds the threshold (negative)
  return percentageChange <= -percentage;
}

/**
 * Execute an agent based on its configuration
 * @param agentConfig - The agent configuration
 * @param polygonData - Data from Polygon API
 * @returns The result of executing the agent
 */
export async function executeAgent(
  agentConfig: AgentConfig,
  polygonData: PolygonData
): Promise<AgentResult> {
  const startTime = Date.now();
  const blockResults = new Map<string, any>();
  const executionPath: string[] = [];

  // Create a map of blocks by ID for easy lookup
  const blocksById = new Map<string, BlockUnion>();
  for (const block of agentConfig.blocks) {
    blocksById.set(block.id, block);
  }

  // Start with the entry block
  let currentBlockId: string | null = agentConfig.entryBlockId;
  let inheritedValue: any = undefined; // Track value passed through condition chains
  let result: AgentResult = {
    signal: 'neutral',
    confidence: 0,
    reasoning: '',
    metrics: {},
    executionPath: [],
    executionTime: 0,
    timestamp: new Date().toISOString()
  };

  // Execute blocks in sequence
  while (currentBlockId) {
    const currentBlock = blocksById.get(currentBlockId);
    if (!currentBlock) {
      throw new Error(`Block not found: ${currentBlockId}`);
    }

    console.log(`\n=== Executing Block: ${currentBlock.type} (ID: ${currentBlockId}) ===`);

    // Execute the block with inherited value if applicable
    const blockResult = await executeBlock(currentBlock, blockResults, polygonData, inheritedValue);
    blockResults.set(currentBlockId, blockResult);
    executionPath.push(currentBlockId);

    console.log(`Block result: ${JSON.stringify(blockResult)}`);

    // If this is a trigger or signal block, set the result
    if (currentBlock.type === BlockType.TRIGGER || currentBlock.type === 'TRIGGER' ||
        currentBlock.type === BlockType.SIGNAL || currentBlock.type === 'SIGNAL') {
      console.log(`Setting final agent result from ${currentBlock.type.toLowerCase()}: signal=${blockResult.signal}, confidence=${blockResult.confidence}`);
      result.signal = blockResult.signal;
      result.confidence = blockResult.confidence;
    }

    // Determine the next block to execute and manage inherited values
    if (currentBlock.type === BlockType.CONDITION || currentBlock.type === BlockType.COMPARISON || currentBlock.type === BlockType.AND || currentBlock.type === BlockType.OR || currentBlock.type === BlockType.CANDLE_PATTERN || currentBlock.type === BlockType.CHART_PATTERN || currentBlock.type === BlockType.STOCK_SENTIMENT) {
      // For condition, comparison, AND, OR, CANDLE_PATTERN, CHART_PATTERN, and STOCK_SENTIMENT blocks, follow the appropriate path
      let nextBlockId: string | null;

      if (currentBlock.type === BlockType.STOCK_SENTIMENT) {
        // Handle stock sentiment block routing based on sentiment result
        const sentimentResult = blockResult;
        if (sentimentResult.sentiment === 'bullish') {
          nextBlockId = currentBlock.bullishConnection || null;
        } else if (sentimentResult.sentiment === 'bearish') {
          nextBlockId = currentBlock.bearishConnection || null;
        } else {
          nextBlockId = currentBlock.neutralConnection || null;
        }
      } else if (currentBlock.type === BlockType.CANDLE_PATTERN) {
        // Handle candle pattern block routing
        if (currentBlock.pattern === 'any') {
          // For 'any' pattern mode, route based on pattern type
          const patternResult = blockResult;
          if (patternResult.type === 'bullish') {
            nextBlockId = currentBlock.bullishConnection || null;
          } else if (patternResult.type === 'bearish') {
            nextBlockId = currentBlock.bearishConnection || null;
          } else {
            nextBlockId = currentBlock.neutralConnection || null;
          }
        } else {
          // For specific pattern mode, route based on true/false
          if (blockResult) {
            nextBlockId = currentBlock.trueConnection || null;
          } else {
            nextBlockId = currentBlock.falseConnection || null;
          }
        }
      } else if (currentBlock.type === BlockType.CHART_PATTERN) {
        // Handle chart pattern block routing
        if (currentBlock.pattern === 'any') {
          // For 'any' pattern mode, route based on pattern type
          const patternResult = blockResult;
          if (patternResult.type === 'bullish') {
            nextBlockId = currentBlock.bullishConnection || null;
          } else if (patternResult.type === 'bearish') {
            nextBlockId = currentBlock.bearishConnection || null;
          } else {
            nextBlockId = currentBlock.neutralConnection || null;
          }
        } else {
          // For specific pattern mode, route based on true/false
          if (blockResult) {
            nextBlockId = currentBlock.trueConnection || null;
          } else {
            nextBlockId = currentBlock.falseConnection || null;
          }
        }
      } else {
        // For condition, AND, and OR blocks, follow the true or false path
        if (blockResult) {
          // Condition is true - follow true path if it exists, otherwise stop execution
          nextBlockId = currentBlock.trueConnection || null;
        } else {
          // Condition is false - follow false path if it exists, otherwise stop execution
          nextBlockId = currentBlock.falseConnection || null;
        }
      }

      // Check if the next block is also a condition/comparison block
      const nextBlock = nextBlockId ? blocksById.get(nextBlockId) : null;
      if (nextBlock && (nextBlock.type === BlockType.CONDITION || nextBlock.type === BlockType.COMPARISON || nextBlock.type === BlockType.AND || nextBlock.type === BlockType.OR)) {
        // If the next block is a condition/AND/OR, pass the original input value
        // Get the original input value that was used in this condition
        if (inheritedValue !== undefined) {
          // Keep passing the inherited value
          inheritedValue = inheritedValue;
        } else {
          // Get the input value from this condition block's input connections
          const inputConnections = currentBlock.inputConnections || [];
          if (inputConnections.length > 0) {
            const inputId = inputConnections[0];
            inheritedValue = blockResults.get(inputId);
          }
        }
        console.log(`Passing inherited value ${inheritedValue} to next condition block ${nextBlockId}`);
      } else {
        // Reset inherited value if next block is not a condition
        inheritedValue = undefined;
      }

      currentBlockId = nextBlockId;
    } else if (
      currentBlock.type === BlockType.WHEN_RUN ||
      currentBlock.type === BlockType.INDICATOR ||
      currentBlock.type === BlockType.MOMENTUM_INDICATOR ||
      currentBlock.type === BlockType.MOVING_AVERAGE ||
      currentBlock.type === BlockType.TREND_INDICATOR ||
      currentBlock.type === BlockType.VOLUME_INDICATOR ||
      currentBlock.type === BlockType.VOLATILITY_INDICATOR ||
      currentBlock.type === BlockType.PRICE ||
      currentBlock.type === BlockType.FUNDAMENTAL ||
      currentBlock.type === BlockType.OPERATOR ||
      currentBlock.type === BlockType.BULLISH_CONFIDENCE_BOOST ||
      currentBlock.type === BlockType.BEARISH_CONFIDENCE_BOOST ||
      currentBlock.type === BlockType.CONFIDENCE_BOOST
    ) {
      // For blocks with multiple output connections, we need to execute ALL paths
      const outputConnections = currentBlock.outputConnections || [];

      if (outputConnections.length > 1) {
        console.log(`Block has ${outputConnections.length} output connections, executing all paths`);

        // Execute all remaining paths recursively
        for (let i = 1; i < outputConnections.length; i++) {
          const pathResult = await executePathFromBlock(
            outputConnections[i],
            blocksById,
            new Map(blockResults), // Copy current results
            [...executionPath], // Copy current path
            polygonData
          );

          // If this path resulted in a trigger, update our result
          if (pathResult.signal !== 'neutral') {
            console.log(`Path ${i} resulted in signal: ${pathResult.signal} with confidence ${pathResult.confidence}`);
            result.signal = pathResult.signal;
            result.confidence = pathResult.confidence;
          }
        }

        // Continue with the first path in the main loop
        currentBlockId = outputConnections[0] || null;
      } else {
        // Single output connection, follow it normally
        currentBlockId = outputConnections[0] || null;
      }

      // Reset inherited value when moving to non-condition blocks
      inheritedValue = undefined;
    } else {
      // For trigger/signal blocks or blocks without connections, stop execution
      if (currentBlock.type === BlockType.TRIGGER || currentBlock.type === 'TRIGGER' ||
          currentBlock.type === BlockType.SIGNAL || currentBlock.type === 'SIGNAL') {
        console.log(`Execution stopped at ${currentBlock.type.toLowerCase()} block`);
      }
      currentBlockId = null;
      inheritedValue = undefined;
    }
  }

  // If no trigger block was reached but we have confidence boost blocks, generate a final signal
  if (result.signal === 'neutral' && result.confidence === 0) {
    const finalSignal = generateSignalFromConfidenceBoosts(blockResults, executionPath);
    if (finalSignal) {
      result.signal = finalSignal.signal;
      result.confidence = finalSignal.confidence;
      console.log(`Generated final signal from confidence boosts: ${finalSignal.signal} with ${finalSignal.confidence}% confidence`);
    }
  }

  // Calculate execution time
  const executionTime = Date.now() - startTime;

  // Generate reasoning based on the execution path
  const reasoning = generateReasoning(agentConfig, blocksById, blockResults, executionPath);

  // Collect metrics from the execution
  const metrics = collectMetrics(blocksById, blockResults, executionPath);

  // Create the final result with all required fields
  const finalResult = {
    signal: result.signal || 'neutral',
    confidence: typeof result.confidence === 'number' ? result.confidence : 0,
    reasoning: reasoning || '',
    metrics: metrics || {},
    executionPath: executionPath || [],
    executionTime: executionTime || 0,
    timestamp: new Date().toISOString()
  };

  // Check if agent uses enhanced blocks and generate enhanced output
  const hasEnhancedBlocks = agentConfig.blocks.some(block =>
    [BlockType.RISK_ANALYZER, BlockType.TARGET_ANALYZER, BlockType.SIGNAL_ENHANCER,
     BlockType.RISK_REWARD_ANALYZER, BlockType.CORRELATION_ANALYSIS, BlockType.MOMENTUM_SHIFT,
     BlockType.TREND_STRENGTH, BlockType.VOLATILITY_FILTER, BlockType.FIBONACCI_LEVELS,
     BlockType.MARKET_SENTIMENT, BlockType.SECTOR_ANALYSIS, BlockType.SIGNAL_QUALITY_FILTER,
     BlockType.CONFIDENCE_THRESHOLD, BlockType.SIGNAL_CONFIRMATION_DELAY, BlockType.MARKET_HOURS_FILTER].includes(block.type as BlockType)
  );

  if (hasEnhancedBlocks) {
    const enhancedResult = await generateEnhancedSignalOutput(finalResult, blockResults, polygonData, agentConfig);
    console.log(`Generated enhanced signal output with risk management and advanced analysis`);
    return enhancedResult;
  }

  // Log the result for debugging
  console.log(`Agent execution completed with signal: ${finalResult.signal}, confidence: ${finalResult.confidence}`);
  console.log(`Agent execution result: signal=${finalResult.signal}, confidence=${finalResult.confidence}`);

  return finalResult;
}

/**
 * Execute a path starting from a specific block
 * @param startBlockId - The block ID to start execution from
 * @param blocksById - Map of blocks by ID
 * @param blockResults - Results of previously executed blocks
 * @param executionPath - Path of executed blocks
 * @param polygonData - Data from Polygon API
 * @returns The result of executing the path
 */
async function executePathFromBlock(
  startBlockId: string | null,
  blocksById: Map<string, BlockUnion>,
  blockResults: Map<string, any>,
  executionPath: string[],
  polygonData: PolygonData
): Promise<AgentResult> {
  let currentBlockId = startBlockId;
  let inheritedValue: any = undefined;
  let result: AgentResult = {
    signal: 'neutral',
    confidence: 0,
    reasoning: '',
    metrics: {},
    executionPath: [],
    executionTime: 0,
    timestamp: new Date().toISOString()
  };

  // Execute blocks in this path
  while (currentBlockId) {
    const currentBlock = blocksById.get(currentBlockId);
    if (!currentBlock) {
      console.error(`Block not found in path execution: ${currentBlockId}`);
      break;
    }

    console.log(`Path execution: ${currentBlock.type} (ID: ${currentBlockId})`);

    // Execute the block if not already executed
    let blockResult: any;
    if (blockResults.has(currentBlockId)) {
      blockResult = blockResults.get(currentBlockId);
    } else {
      blockResult = await executeBlock(currentBlock, blockResults, polygonData, inheritedValue);
      blockResults.set(currentBlockId, blockResult);
      executionPath.push(currentBlockId);
    }

    // If this is a trigger or signal block, set the result and stop
    if (currentBlock.type === BlockType.TRIGGER || currentBlock.type === 'TRIGGER' ||
        currentBlock.type === BlockType.SIGNAL || currentBlock.type === 'SIGNAL') {
      console.log(`Path reached ${currentBlock.type.toLowerCase()}: signal=${blockResult.signal}, confidence=${blockResult.confidence}`);
      result.signal = blockResult.signal;
      result.confidence = blockResult.confidence;
      break;
    }

    // Determine the next block
    if (currentBlock.type === BlockType.CONDITION || currentBlock.type === BlockType.AND || currentBlock.type === BlockType.OR || currentBlock.type === BlockType.CANDLE_PATTERN || currentBlock.type === BlockType.CHART_PATTERN || currentBlock.type === BlockType.STOCK_SENTIMENT) {
      if (currentBlock.type === BlockType.STOCK_SENTIMENT) {
        // Handle stock sentiment block routing based on sentiment result
        const sentimentResult = blockResult;
        if (sentimentResult.sentiment === 'bullish') {
          currentBlockId = currentBlock.bullishConnection || null;
        } else if (sentimentResult.sentiment === 'bearish') {
          currentBlockId = currentBlock.bearishConnection || null;
        } else {
          currentBlockId = currentBlock.neutralConnection || null;
        }
      } else if (currentBlock.type === BlockType.CANDLE_PATTERN) {
        // Handle candle pattern block routing
        if (currentBlock.pattern === 'any') {
          // For 'any' pattern mode, route based on pattern type
          const patternResult = blockResult;
          if (patternResult.type === 'bullish') {
            currentBlockId = currentBlock.bullishConnection || null;
          } else if (patternResult.type === 'bearish') {
            currentBlockId = currentBlock.bearishConnection || null;
          } else {
            currentBlockId = currentBlock.neutralConnection || null;
          }
        } else {
          // For specific pattern mode, route based on true/false
          if (blockResult) {
            currentBlockId = currentBlock.trueConnection || null;
          } else {
            currentBlockId = currentBlock.falseConnection || null;
          }
        }
      } else if (currentBlock.type === BlockType.CHART_PATTERN) {
        // Handle chart pattern block routing
        if (currentBlock.pattern === 'any') {
          // For 'any' pattern mode, route based on pattern type
          const patternResult = blockResult;
          if (patternResult.type === 'bullish') {
            currentBlockId = currentBlock.bullishConnection || null;
          } else if (patternResult.type === 'bearish') {
            currentBlockId = currentBlock.bearishConnection || null;
          } else {
            currentBlockId = currentBlock.neutralConnection || null;
          }
        } else {
          // For specific pattern mode, route based on true/false
          if (blockResult) {
            currentBlockId = currentBlock.trueConnection || null;
          } else {
            currentBlockId = currentBlock.falseConnection || null;
          }
        }
      } else {
        // For condition, AND, and OR blocks
        if (blockResult) {
          currentBlockId = currentBlock.trueConnection || null;
        } else {
          currentBlockId = currentBlock.falseConnection || null;
        }
      }

      // Handle inherited values for condition chains
      const nextBlock = currentBlockId ? blocksById.get(currentBlockId) : null;
      if (nextBlock && (nextBlock.type === BlockType.CONDITION || nextBlock.type === BlockType.AND || nextBlock.type === BlockType.OR)) {
        if (inheritedValue === undefined) {
          const inputConnections = currentBlock.inputConnections || [];
          if (inputConnections.length > 0) {
            inheritedValue = blockResults.get(inputConnections[0]);
          }
        }
      } else {
        inheritedValue = undefined;
      }
    } else {
      // For other block types, follow the first output connection
      currentBlockId = currentBlock.outputConnections?.[0] || null;
      inheritedValue = undefined;
    }
  }

  return result;
}

/**
 * Generate reasoning for the agent's decision
 * @param agentConfig - The agent configuration
 * @param blocksById - Map of blocks by ID
 * @param blockResults - Results of executed blocks
 * @param executionPath - Order of block execution
 * @returns Reasoning string
 */
function generateReasoning(
  agentConfig: AgentConfig,
  blocksById: Map<string, BlockUnion>,
  blockResults: Map<string, any>,
  executionPath: string[]
): string {
  // Use a default name if agentConfig.name is undefined
  const agentName = agentConfig.name || 'Agent';
  let reasoning = `${agentName} analyzed the data and `;

  // Find the trigger or signal block (if any)
  const signalBlock = Array.from(blocksById.values())
    .find(block => block.type === BlockType.TRIGGER || block.type === 'TRIGGER' ||
                   block.type === BlockType.SIGNAL || block.type === 'SIGNAL');

  if (signalBlock && blockResults.has(signalBlock.id)) {
    const signalResult = blockResults.get(signalBlock.id);

    if (signalResult.signal === 'neutral') {
      reasoning += 'found no clear signal. ';
    } else {
      reasoning += `generated a ${signalResult.signal.toUpperCase()} signal with ${signalResult.confidence}% confidence. `;
    }
  } else {
    reasoning += 'did not reach a conclusion. ';
  }

  // Add details about key indicators and conditions
  reasoning += 'The analysis considered: ';

  const indicatorBlocks = executionPath
    .filter(id => {
      const blockType = blocksById.get(id)?.type;
      return blockType === BlockType.INDICATOR ||
             blockType === BlockType.MOMENTUM_INDICATOR ||
             blockType === BlockType.MOVING_AVERAGE ||
             blockType === BlockType.TREND_INDICATOR ||
             blockType === BlockType.VOLUME_INDICATOR ||
             blockType === BlockType.VOLATILITY_INDICATOR;
    })
    .map(id => {
      const block = blocksById.get(id);
      const result = blockResults.get(id);
      if (!block) return '';

      // Handle different indicator block types
      if (block.type === BlockType.INDICATOR) {
        return `${(block as any).indicator} (${typeof result === 'object' ? JSON.stringify(result) : result})`;
      } else if (block.type === BlockType.MOMENTUM_INDICATOR) {
        return `${(block as any).indicator} (${typeof result === 'object' ? JSON.stringify(result) : result})`;
      } else if (block.type === BlockType.MOVING_AVERAGE) {
        return `${(block as any).averageType?.toUpperCase()} ${(block as any).period} (${result})`;
      } else if (block.type === BlockType.TREND_INDICATOR) {
        return `${(block as any).indicator} (${typeof result === 'object' ? JSON.stringify(result) : result})`;
      } else if (block.type === BlockType.VOLUME_INDICATOR) {
        return `${(block as any).indicator} (${typeof result === 'object' ? JSON.stringify(result) : result})`;
      } else if (block.type === BlockType.VOLATILITY_INDICATOR) {
        return `${(block as any).indicator} (${typeof result === 'object' ? JSON.stringify(result) : result})`;
      }
      return '';
    })
    .filter(text => text !== '');

  if (indicatorBlocks.length > 0) {
    reasoning += `Indicators: ${indicatorBlocks.join(', ')}. `;
  }

  const priceBlocks = executionPath
    .filter(id => blocksById.get(id)?.type === BlockType.PRICE)
    .map(id => {
      const block = blocksById.get(id);
      const result = blockResults.get(id);
      if (!block || block.type !== BlockType.PRICE) return '';
      return `${(block as any).dataPoint} price (${result})`;
    })
    .filter(text => text !== '');

  if (priceBlocks.length > 0) {
    reasoning += `Price data: ${priceBlocks.join(', ')}. `;
  }

  const candlePatternBlocks = executionPath
    .filter(id => blocksById.get(id)?.type === BlockType.CANDLE_PATTERN)
    .map(id => {
      const block = blocksById.get(id);
      const result = blockResults.get(id);
      if (!block || block.type !== BlockType.CANDLE_PATTERN) return '';
      const patternBlock = block as any;

      if (patternBlock.pattern === 'any' && typeof result === 'object') {
        if (result.detected) {
          return `${result.type} candle pattern detected (${result.pattern})`;
        } else {
          return `no candle patterns detected`;
        }
      } else if (patternBlock.pattern !== 'any') {
        return `${patternBlock.pattern} pattern ${result ? 'detected' : 'not detected'}`;
      }
      return `candle pattern analysis (${result})`;
    })
    .filter(text => text !== '');

  if (candlePatternBlocks.length > 0) {
    reasoning += `Candle patterns: ${candlePatternBlocks.join(', ')}. `;
  }

  const chartPatternBlocks = executionPath
    .filter(id => blocksById.get(id)?.type === BlockType.CHART_PATTERN)
    .map(id => {
      const block = blocksById.get(id);
      const result = blockResults.get(id);
      if (!block || block.type !== BlockType.CHART_PATTERN) return '';
      const patternBlock = block as any;

      if (patternBlock.pattern === 'any' && typeof result === 'object') {
        if (result.detected) {
          return `${result.type} chart pattern detected (${result.pattern})`;
        } else {
          return `no chart patterns detected`;
        }
      } else if (patternBlock.pattern !== 'any') {
        return `${patternBlock.pattern} chart pattern ${result ? 'detected' : 'not detected'}`;
      }
      return `chart pattern analysis (${result})`;
    })
    .filter(text => text !== '');

  if (chartPatternBlocks.length > 0) {
    reasoning += `Chart patterns: ${chartPatternBlocks.join(', ')}. `;
  }

  const fundamentalBlocks = executionPath
    .filter(id => blocksById.get(id)?.type === BlockType.FUNDAMENTAL)
    .map(id => {
      const block = blocksById.get(id);
      const result = blockResults.get(id);

      if (!block || block.type !== BlockType.FUNDAMENTAL) return '';
      const fundamentalBlock = block as any;

      // Handle calculated metrics with components
      if (result && typeof result === 'object' && result.value !== undefined && result.components) {
        const components = result.components;
        if (fundamentalBlock.metric === 'return_on_equity') {
          const netIncome = components.net_income_ttm || components.net_income;
          const equity = components.shareholders_equity;
          if (netIncome && equity) {
            return `ROE: ${result.value.toFixed(2)}% (Net Income TTM: $${netIncome.toLocaleString()}, Equity: $${equity.toLocaleString()})`;
          } else {
            return `ROE: ${result.value.toFixed(2)}% (calculated)`;
          }
        }
        return `${fundamentalBlock.metric}: ${result.value} (calculated)`;
      }

      return `${fundamentalBlock.metric} (${result})`;
    })
    .filter(text => text !== '');

  if (fundamentalBlocks.length > 0) {
    reasoning += `Fundamentals: ${fundamentalBlocks.join(', ')}. `;
  }

  return reasoning;
}

/**
 * Collect metrics from the execution
 * @param blocksById - Map of blocks by ID
 * @param blockResults - Results of executed blocks
 * @param executionPath - Order of block execution
 * @returns Object with metrics
 */
function collectMetrics(
  blocksById: Map<string, BlockUnion>,
  blockResults: Map<string, any>,
  executionPath: string[]
): Record<string, any> {
  const metrics: Record<string, any> = {};

  // Collect indicator values
  const indicatorMetrics: Record<string, any> = {};
  for (const id of executionPath) {
    const block = blocksById.get(id);
    if (block?.type === BlockType.INDICATOR) {
      indicatorMetrics[block.indicator] = blockResults.get(id);
    } else if (block?.type === BlockType.MOMENTUM_INDICATOR) {
      indicatorMetrics[block.indicator] = blockResults.get(id);
    } else if (block?.type === BlockType.MOVING_AVERAGE) {
      const key = `${block.averageType}_${block.period}`;
      indicatorMetrics[key] = blockResults.get(id);
    } else if (block?.type === BlockType.TREND_INDICATOR) {
      indicatorMetrics[block.indicator] = blockResults.get(id);
    } else if (block?.type === BlockType.VOLUME_INDICATOR) {
      indicatorMetrics[block.indicator] = blockResults.get(id);
    } else if (block?.type === BlockType.VOLATILITY_INDICATOR) {
      indicatorMetrics[block.indicator] = blockResults.get(id);
    }
  }

  if (Object.keys(indicatorMetrics).length > 0) {
    metrics.indicators = indicatorMetrics;
  }

  // Collect candle pattern values
  const candlePatternMetrics: Record<string, any> = {};
  for (const id of executionPath) {
    const block = blocksById.get(id);
    if (block?.type === BlockType.CANDLE_PATTERN) {
      const result = blockResults.get(id);
      const patternBlock = block as any;

      if (patternBlock.pattern === 'any' && typeof result === 'object' && result.type) {
        candlePatternMetrics[`${patternBlock.pattern}_pattern`] = {
          detected: result.detected,
          type: result.type,
          timeframe: patternBlock.timeframe || 'daily',
          primaryPattern: result.pattern,
          allPatterns: result.patterns || []
        };
      } else {
        candlePatternMetrics[`${patternBlock.pattern}_pattern`] = {
          detected: !!result,
          timeframe: patternBlock.timeframe || 'daily'
        };
      }
    }
  }

  if (Object.keys(candlePatternMetrics).length > 0) {
    metrics.candlePatterns = candlePatternMetrics;
  }

  // Collect chart pattern values
  const chartPatternMetrics: Record<string, any> = {};
  for (const id of executionPath) {
    const block = blocksById.get(id);
    if (block?.type === BlockType.CHART_PATTERN) {
      const result = blockResults.get(id);
      const patternBlock = block as any;

      if (patternBlock.pattern === 'any' && typeof result === 'object' && result.type) {
        chartPatternMetrics[`${patternBlock.pattern}_chart_pattern`] = {
          detected: result.detected,
          type: result.type,
          timeframe: patternBlock.timeframe || 'daily',
          primaryPattern: result.pattern,
          significance: result.significance,
          allPatterns: result.patterns || []
        };
      } else {
        chartPatternMetrics[`${patternBlock.pattern}_chart_pattern`] = {
          detected: !!result,
          timeframe: patternBlock.timeframe || 'daily'
        };
      }
    }
  }

  if (Object.keys(chartPatternMetrics).length > 0) {
    metrics.chartPatterns = chartPatternMetrics;
  }

  // Collect price values
  const priceMetrics: Record<string, any> = {};
  for (const id of executionPath) {
    const block = blocksById.get(id);
    if (block?.type === BlockType.PRICE) {
      priceMetrics[block.dataPoint] = blockResults.get(id);
    }
  }

  if (Object.keys(priceMetrics).length > 0) {
    metrics.prices = priceMetrics;
  }

  // Collect fundamental values
  const fundamentalMetrics: Record<string, any> = {};
  for (const id of executionPath) {
    const block = blocksById.get(id);
    if (block?.type === BlockType.FUNDAMENTAL) {
      fundamentalMetrics[block.metric] = blockResults.get(id);
    }
  }

  if (Object.keys(fundamentalMetrics).length > 0) {
    metrics.fundamentals = fundamentalMetrics;
  }

  return metrics;
}

/**
 * Generate enhanced signal output with risk management and advanced analysis
 * @param baseResult - The base agent result
 * @param blockResults - Results from all executed blocks
 * @param polygonData - Market data
 * @param agentConfig - Agent configuration
 * @returns Enhanced signal output
 */
async function generateEnhancedSignalOutput(
  baseResult: AgentResult,
  blockResults: Map<string, any>,
  polygonData: PolygonData,
  agentConfig: AgentConfig
): Promise<EnhancedSignalOutput> {
  const enhanced: EnhancedSignalOutput = {
    ...baseResult
  };

  // Extract risk management data from risk analysis blocks
  const riskAnalyzerResults = Array.from(blockResults.entries())
    .filter(([id, result]) => {
      const block = agentConfig.blocks.find(b => b.id === id);
      return block && [BlockType.RISK_ANALYZER, BlockType.TARGET_ANALYZER,
                      BlockType.SIGNAL_ENHANCER, BlockType.RISK_REWARD_ANALYZER].includes(block.type as BlockType);
    });

  if (riskAnalyzerResults.length > 0) {
    enhanced.risk_management = await generateRiskManagementGuidance(riskAnalyzerResults, polygonData);
  }

  // Extract advanced analysis data
  const advancedAnalysisResults = Array.from(blockResults.entries())
    .filter(([id, result]) => {
      const block = agentConfig.blocks.find(b => b.id === id);
      return block && [BlockType.CORRELATION_ANALYSIS, BlockType.MOMENTUM_SHIFT, BlockType.TREND_STRENGTH,
                      BlockType.VOLATILITY_FILTER, BlockType.FIBONACCI_LEVELS, BlockType.MARKET_SENTIMENT,
                      BlockType.SECTOR_ANALYSIS].includes(block.type as BlockType);
    });

  if (advancedAnalysisResults.length > 0) {
    enhanced.advanced_analysis = await generateAdvancedAnalysis(advancedAnalysisResults, polygonData);
  }

  // Extract signal quality data
  const signalQualityResults = Array.from(blockResults.entries())
    .filter(([id, result]) => {
      const block = agentConfig.blocks.find(b => b.id === id);
      return block && [BlockType.SIGNAL_QUALITY_FILTER, BlockType.CONFIDENCE_THRESHOLD,
                      BlockType.SIGNAL_CONFIRMATION_DELAY, BlockType.MARKET_HOURS_FILTER].includes(block.type as BlockType);
    });

  if (signalQualityResults.length > 0) {
    enhanced.signal_quality = await generateSignalQuality(signalQualityResults, polygonData);
  }

  // Generate educational content
  enhanced.educational = await generateEducationalContent(baseResult, polygonData, agentConfig);

  return enhanced;
}

/**
 * Generate risk management guidance from risk analysis block results
 */
async function generateRiskManagementGuidance(
  riskResults: [string, any][],
  polygonData: PolygonData
): Promise<any> {
  const currentPrice = polygonData.price.current || polygonData.price.close;

  // Find risk analyzer result
  const riskAnalyzer = riskResults.find(([id, result]) => result.method?.includes('Risk') || result.stop_loss_price);
  const targetAnalyzer = riskResults.find(([id, result]) => result.method?.includes('Target') || result.targets);
  const riskRewardAnalyzer = riskResults.find(([id, result]) => result.risk_reward_ratio);

  const guidance: any = {
    entry_price: currentPrice
  };

  // Stop loss guidance
  if (riskAnalyzer && riskAnalyzer[1]) {
    const riskData = riskAnalyzer[1];
    guidance.stop_loss = {
      price: riskData.stop_loss_price || currentPrice * 0.97,
      percentage: riskData.risk_percentage || 3,
      method: riskData.method || 'Percentage-based',
      reasoning: riskData.reasoning || 'Conservative 3% stop loss'
    };
  }

  // Take profit guidance
  if (targetAnalyzer && targetAnalyzer[1]) {
    const targetData = targetAnalyzer[1];
    guidance.take_profit = {
      targets: targetData.targets || [
        { price: currentPrice * 1.06, percentage: 6, probability: 70, level_type: 'Target 1' },
        { price: currentPrice * 1.12, percentage: 12, probability: 50, level_type: 'Target 2' }
      ],
      method: targetData.method || 'Risk/Reward Ratio'
    };
  }

  // Risk/reward ratio
  if (riskRewardAnalyzer && riskRewardAnalyzer[1]) {
    guidance.risk_reward_ratio = riskRewardAnalyzer[1].risk_reward_ratio || 2.0;
  } else {
    guidance.risk_reward_ratio = 2.0; // Default
  }



  return guidance;
}

/**
 * Generate advanced analysis summary
 */
async function generateAdvancedAnalysis(
  analysisResults: [string, any][],
  polygonData: PolygonData
): Promise<any> {
  const analysis: any = {};

  // Process each analysis result
  for (const [id, result] of analysisResults) {
    if (result.method?.includes('Trend Strength') || result.trend_strength) {
      analysis.trend_strength = {
        score: result.adx_value || 25,
        direction: result.trend_strength === 'strong' ? 'up' : 'sideways',
        method: result.method || 'ADX-Based'
      };
    }

    if (result.method?.includes('Volatility') || result.volatility_level) {
      analysis.volatility_assessment = {
        level: result.volatility_level || 'moderate',
        percentile: result.atr_percentile || 50,
        suitable_for_strategy: !result.should_filter
      };
    }

    if (result.method?.includes('Support') || result.support_level) {
      analysis.support_resistance = {
        nearest_support: result.support_level || polygonData.price.current * 0.95,
        nearest_resistance: result.resistance_level || polygonData.price.current * 1.05,
        strength_score: 75
      };
    }

    if (result.method?.includes('Correlation') || result.correlation_coefficient !== undefined) {
      analysis.correlation_analysis = {
        market_correlation: result.correlation_coefficient || 0.65,
        sector_correlation: 0.8, // Mock
        relative_strength: result.relative_performance || 5
      };
    }

    if (result.method?.includes('Momentum') || result.momentum_shift_detected !== undefined) {
      analysis.momentum_analysis = {
        momentum_score: Math.abs(result.rate_of_change || 5),
        momentum_shift_detected: result.momentum_shift_detected || false,
        momentum_direction: result.direction === 'bullish' ? 'increasing' : 'decreasing'
      };
    }
  }

  return analysis;
}

/**
 * Generate signal quality metrics
 */
async function generateSignalQuality(
  qualityResults: [string, any][],
  polygonData: PolygonData
): Promise<any> {
  const quality: any = {
    overall_score: 75, // Default
    volume_confirmation: false,
    trend_alignment: false,
    multiple_timeframe_agreement: false,
    quality_factors: []
  };

  // Process quality results
  for (const [id, result] of qualityResults) {
    if (result.passes_filter !== undefined) {
      quality.overall_score = result.quality_metrics?.overall_score || quality.overall_score;
      quality.volume_confirmation = result.quality_metrics?.volume_confirmed || false;
      quality.trend_alignment = result.quality_metrics?.trend_aligned || false;
      quality.quality_factors = result.quality_metrics?.quality_factors || [];
    }

    if (result.adjusted_confidence !== undefined) {
      quality.overall_score = Math.max(quality.overall_score, result.adjusted_confidence);
    }

    if (result.is_confirmed !== undefined) {
      quality.multiple_timeframe_agreement = result.is_confirmed;
    }
  }

  return quality;
}

/**
 * Generate educational content
 */
async function generateEducationalContent(
  baseResult: AgentResult,
  polygonData: PolygonData,
  agentConfig: AgentConfig
): Promise<any> {
  return {
    strategy_explanation: `This ${baseResult.signal} signal is generated using a comprehensive analysis combining technical indicators, market structure, and risk management principles.`,
    risk_warnings: [
      'Market conditions can change rapidly',
      'Past performance does not guarantee future results',
      'Always use proper position sizing and risk management',
      'Consider market volatility and liquidity before trading'
    ],
    market_context: `Current market showing ${polygonData.price.current > polygonData.price.open ? 'bullish' : 'bearish'} intraday momentum with moderate volatility conditions.`,
    similar_historical_setups: Math.floor(Math.random() * 50) + 10 // Mock historical count
  };
}

/**
 * Generate a final signal from confidence boost blocks when no trigger block is present
 * @param blockResults - Results of executed blocks
 * @param executionPath - Order of block execution
 * @returns Final signal and confidence or null if no confidence boosts found
 */
function generateSignalFromConfidenceBoosts(
  blockResults: Map<string, any>,
  executionPath: string[]
): { signal: 'bullish' | 'bearish' | 'neutral'; confidence: number } | null {
  let bullishConfidence = 0;
  let bearishConfidence = 0;
  let neutralConfidence = 0;
  let hasConfidenceBoosts = false;

  console.log('Checking for confidence boost blocks to generate final signal...');

  // Iterate through execution path to find confidence boost blocks
  for (const blockId of executionPath) {
    const result = blockResults.get(blockId);
    if (result && typeof result === 'object') {
      // Check for legacy confidence boost blocks
      if (result.type === 'bullish_confidence_boost') {
        bullishConfidence += result.adjustment;
        hasConfidenceBoosts = true;
        console.log(`Found legacy bullish confidence boost: +${result.adjustment}%`);
      } else if (result.type === 'bearish_confidence_boost') {
        bearishConfidence += result.adjustment;
        hasConfidenceBoosts = true;
        console.log(`Found legacy bearish confidence boost: +${result.adjustment}%`);
      }
      // Check for unified confidence boost blocks
      else if (result.boostType) {
        hasConfidenceBoosts = true;
        if (result.boostType === 'bullish') {
          bullishConfidence += result.adjustment;
          console.log(`Found unified bullish confidence boost: +${result.adjustment}%`);
        } else if (result.boostType === 'bearish') {
          bearishConfidence += result.adjustment;
          console.log(`Found unified bearish confidence boost: +${result.adjustment}%`);
        } else if (result.boostType === 'neutral') {
          neutralConfidence += result.adjustment;
          console.log(`Found unified neutral confidence boost: +${result.adjustment}%`);
        }
      }
    }
  }

  if (!hasConfidenceBoosts) {
    console.log('No confidence boost blocks found');
    return null;
  }

  console.log(`Confidence totals: bullish=${bullishConfidence}%, bearish=${bearishConfidence}%, neutral=${neutralConfidence}%`);

  // Determine the final signal based on the highest confidence
  let finalSignal: 'bullish' | 'bearish' | 'neutral' = 'neutral';
  let finalConfidence = 0;

  if (bullishConfidence > bearishConfidence && bullishConfidence > neutralConfidence) {
    finalSignal = 'bullish';
    finalConfidence = Math.min(100, bullishConfidence); // Cap at 100%
  } else if (bearishConfidence > bullishConfidence && bearishConfidence > neutralConfidence) {
    finalSignal = 'bearish';
    finalConfidence = Math.min(100, bearishConfidence); // Cap at 100%
  } else if (neutralConfidence > 0) {
    finalSignal = 'neutral';
    finalConfidence = Math.min(100, neutralConfidence); // Cap at 100%
  } else {
    // If bullish and bearish are equal, or all are zero, default to neutral
    finalSignal = 'neutral';
    finalConfidence = 0;
  }

  console.log(`Generated final signal: ${finalSignal} with ${finalConfidence}% confidence`);

  return {
    signal: finalSignal,
    confidence: finalConfidence
  };
}


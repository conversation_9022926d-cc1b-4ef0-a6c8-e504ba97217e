// Agent Runner Edge Function
// This function executes trading agents created by users

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import { fetchPolygonData } from "./polygon-api.ts"
import { executeAgent } from "./agent-executor.ts"
import { AgentConfig, AgentResult } from "./agent-types.ts"

// CORS headers for cross-origin requests
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS'
};

// Handle OPTIONS requests for CORS
function handleOptions() {
  return new Response(null, {
    status: 204,
    headers: corsHeaders
  });
}

// Create a Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? '';
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '';
const supabase = createClient(supabaseUrl, supabaseServiceKey);

/**
 * Normalize block types to ensure compatibility
 * @param config - The agent configuration to normalize
 * @returns The normalized agent configuration
 */
function normalizeBlockTypes(config: AgentConfig): AgentConfig {
  // Create a deep copy of the configuration
  const normalizedConfig = JSON.parse(JSON.stringify(config));

  // Normalize block types
  normalizedConfig.blocks = normalizedConfig.blocks.map((block: any) => {
    // Convert block type to uppercase if it's a string
    if (typeof block.type === 'string') {
      const type = block.type.toUpperCase();

      // Map specific types
      if (type === 'WHEN_RUN' || type === 'WHENRUN') {
        block.type = 'WHEN_RUN';
      } else if (type === 'INDICATOR') {
        block.type = 'INDICATOR';
      } else if (type === 'PRICE') {
        block.type = 'PRICE';
      } else if (type === 'FUNDAMENTAL') {
        block.type = 'FUNDAMENTAL';
      } else if (type === 'CONDITION') {
        block.type = 'CONDITION';
      } else if (type === 'TRIGGER') {
        block.type = 'TRIGGER';
      } else if (type === 'SIGNAL') {
        block.type = 'SIGNAL';
      } else if (type === 'OPERATOR') {
        block.type = 'OPERATOR';
      } else if (type === 'PERCENTAGE_UP') {
        block.type = 'PERCENTAGE_UP';
      } else if (type === 'PERCENTAGE_DOWN') {
        block.type = 'PERCENTAGE_DOWN';
      } else if (type === 'MOMENTUM_INDICATOR') {
        block.type = 'MOMENTUM_INDICATOR';
      } else if (type === 'MOVING_AVERAGE') {
        block.type = 'MOVING_AVERAGE';
      } else if (type === 'TREND_INDICATOR') {
        block.type = 'TREND_INDICATOR';
      }
    }

    return block;
  });

  return normalizedConfig;
}

// Main handler function
serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return handleOptions();
  }

  // Only allow POST requests
  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      status: 405,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }

  try {
    // Parse request body
    const requestData = await req.json();

    // Validate request data
    if (!requestData.agentId && !requestData.agentConfig) {
      throw new Error('Either agentId or agentConfig must be provided');
    }

    if (!requestData.symbol) {
      throw new Error('Symbol is required');
    }

    // Log request data for debugging
    console.log(`Processing request for symbol: ${requestData.symbol}, timeframe: ${requestData.timeframe || 'day'}`);

    // Validate agent configuration if provided directly
    if (requestData.agentConfig) {
      if (!requestData.agentConfig.blocks || !Array.isArray(requestData.agentConfig.blocks) || requestData.agentConfig.blocks.length === 0) {
        throw new Error('Agent configuration must have at least one block');
      }

      if (!requestData.agentConfig.entryBlockId) {
        throw new Error('Agent configuration must have an entry block ID');
      }

      // Check if the entry block exists
      const entryBlockExists = requestData.agentConfig.blocks.some(block => block.id === requestData.agentConfig.entryBlockId);
      if (!entryBlockExists) {
        throw new Error('Entry block ID does not match any block in the configuration');
      }
    }

    // Get agent configuration
    let agentConfig: AgentConfig;

    if (requestData.agentId) {
      // Fetch agent configuration from database
      const { data: agent, error } = await supabase
        .from('agents')
        .select('configuration')
        .eq('id', requestData.agentId)
        .single();

      if (error || !agent) {
        throw new Error(`Agent not found: ${error?.message || 'Unknown error'}`);
      }

      // Normalize the agent configuration
      agentConfig = normalizeBlockTypes(agent.configuration as AgentConfig);

      // Add a name if not present
      if (!agentConfig.name) {
        agentConfig.name = `Agent ${requestData.agentId.substring(0, 8)}`;
      }
    } else {
      // Use provided agent configuration and normalize it
      agentConfig = normalizeBlockTypes(requestData.agentConfig as AgentConfig);

      // Add a name if not present
      if (!agentConfig.name) {
        agentConfig.name = `Custom Agent ${new Date().toISOString().substring(0, 10)}`;
      }
    }

    // Log the normalized block types
    console.log(`Normalized block types: ${agentConfig.blocks.map(b => b.type).join(', ')}`);

    // Check for debug mode
    const debugMode = requestData.debug === true;
    if (debugMode) {
      console.log('Running in debug mode');
    }

    // Fetch data from Polygon API
    const polygonApiKey = Deno.env.get('POLYGON_API_KEY') ?? '';
    if (!polygonApiKey && !debugMode) {
      throw new Error('POLYGON_API_KEY environment variable is not set');
    }

    const symbol = requestData.symbol;
    const timeframe = requestData.timeframe || 'day';

    // Fetch required data based on agent configuration
    let polygonData;

    if (requestData.historicalContext) {
      // For backtesting, use the provided historical data
      const historicalData = requestData.historicalContext.historicalData;
      const currentIndex = requestData.historicalContext.currentIndex;
      const currentData = historicalData[historicalData.length - 1]; // Use the last item as current

      const historicalArrays = {
        open: historicalData.map(d => d.open),
        high: historicalData.map(d => d.high),
        low: historicalData.map(d => d.low),
        close: historicalData.map(d => d.close),
        volume: historicalData.map(d => d.volume),
        timestamp: historicalData.map(d => d.timestamp)
      };

      polygonData = {
        symbol: symbol, // Include the symbol for backtesting
        price: {
          current: currentData.close,
          open: currentData.open,
          high: currentData.high,
          low: currentData.low,
          close: currentData.close,
          volume: currentData.volume,
          timestamp: currentData.timestamp
        },
        historical: historicalArrays,
        indicators: {},
        fundamentals: {}
      };

      // Calculate required indicators for backtesting
      const { calculateIndicators } = await import("./indicators.ts");

      // Determine which indicators are needed
      const requiredIndicators = new Set<string>();
      for (const block of agentConfig.blocks) {
        if (block.type === 'INDICATOR') {
          const indicatorName = (block as any).indicator;
          if (indicatorName) {
            requiredIndicators.add(indicatorName.toLowerCase());
          }
        } else if (block.type === 'MOMENTUM_INDICATOR') {
          const indicatorName = (block as any).indicator;
          if (indicatorName) {
            requiredIndicators.add(indicatorName.toLowerCase());
          }
        } else if (block.type === 'TREND_INDICATOR') {
          const indicatorName = (block as any).indicator;
          if (indicatorName) {
            requiredIndicators.add(indicatorName.toLowerCase());
          }
        } else if (block.type === 'VOLUME_INDICATOR') {
          const indicatorName = (block as any).indicator;
          if (indicatorName) {
            requiredIndicators.add(indicatorName.toLowerCase());
          }
        } else if (block.type === 'VOLATILITY_INDICATOR') {
          const indicatorName = (block as any).indicator;
          if (indicatorName) {
            requiredIndicators.add(indicatorName.toLowerCase());
          }
        }
      }

      // Calculate indicators if needed
      if (requiredIndicators.size > 0) {
        console.log(`Calculating indicators for backtesting: ${Array.from(requiredIndicators).join(', ')}`);

        for (const indicator of requiredIndicators) {
          if (indicator === 'support' || indicator === 'resistance' || indicator === 'candle_pattern' || indicator === 'rsi' || indicator === 'macd' || indicator === 'vwap') {
            // These are calculated in the executor
            polygonData.indicators[indicator] = 'calculated_in_executor';
          } else {
            try {
              const calculatedIndicators = calculateIndicators(historicalArrays, [indicator]);
              Object.assign(polygonData.indicators, calculatedIndicators);
            } catch (error) {
              console.error(`Error calculating ${indicator} for backtesting:`, error);
              // Set a default value to prevent errors
              polygonData.indicators[indicator] = [];
            }
          }
        }
      }
    } else {
      // For live trading, fetch data from Polygon API
      polygonData = await fetchPolygonData(
        symbol,
        timeframe,
        polygonApiKey,
        agentConfig
      );
    }

    // Log agent configuration for debugging
    console.log(`Executing agent with ${agentConfig.blocks.length} blocks`);
    console.log(`Block types: ${agentConfig.blocks.map(b => b.type).join(', ')}`);

    // Execute the agent
    let result;
    try {
      console.log('Starting agent execution...');
      result = await executeAgent(agentConfig, polygonData);
      console.log('Agent execution completed successfully');
      // Log the result for debugging
      console.log(`Agent execution result: signal=${result.signal}, confidence=${result.confidence}`);
    } catch (executionError) {
      console.error('Error during agent execution:', executionError);
      console.error('Error details:', {
        message: executionError.message,
        stack: executionError.stack,
        name: executionError.name
      });
      console.error('Agent config blocks:', agentConfig.blocks.map(b => ({ id: b.id, type: b.type })));
      console.error('Polygon data keys:', Object.keys(polygonData));
      console.error('Polygon data structure:', {
        hasPrice: !!polygonData.price,
        hasHistorical: !!polygonData.historical,
        hasSymbol: !!polygonData.symbol,
        priceKeys: polygonData.price ? Object.keys(polygonData.price) : [],
        historicalKeys: polygonData.historical ? Object.keys(polygonData.historical) : []
      });
      throw executionError;
    }

    // Ensure the result has all required fields and remove any undefined values
    const formattedResult = {
      signal: result.signal || 'neutral',
      confidence: typeof result.confidence === 'number' ? result.confidence : 0,
      reasoning: result.reasoning || '',
      metrics: result.metrics || {},
      executionPath: result.executionPath || [],
      executionTime: result.executionTime || 0,
      timestamp: result.timestamp || new Date().toISOString(),
      debugLogs: result.debugLogs || []
    };

    // Remove any undefined values that might cause JSON serialization issues
    const cleanResult = JSON.parse(JSON.stringify(formattedResult, (key, value) => {
      return value === undefined ? null : value;
    }));

    console.log('Formatted result:', JSON.stringify(cleanResult, null, 2));

    // If this was a saved agent run, record the result
    if (requestData.agentId && requestData.saveRun !== false) {
      console.log('Saving agent run to database...');
      console.log('Insert data:', {
        agent_id: requestData.agentId,
        symbol: symbol,
        result_keys: Object.keys(cleanResult),
        result_size: JSON.stringify(cleanResult).length
      });

      try {
        const { error: insertError } = await supabase
          .from('agent_runs')
          .insert({
            agent_id: requestData.agentId,
            symbol: symbol,
            result: cleanResult
          });

        if (insertError) {
          console.error('Error saving agent run:', insertError);
          console.error('Insert error details:', {
            message: insertError.message,
            code: insertError.code,
            details: insertError.details,
            hint: insertError.hint
          });
          throw insertError;
        }
        console.log('Agent run saved successfully');
      } catch (dbError) {
        console.error('Database error:', dbError);
        console.error('Database error details:', {
          message: dbError.message,
          stack: dbError.stack,
          name: dbError.name
        });
        throw dbError;
      }
    }

    console.log('Returning successful response...');
    // Return the cleaned result
    return new Response(JSON.stringify(cleanResult), {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  } catch (error) {
    // Handle errors
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error(`Error in agent-runner:`, errorMessage);

    return new Response(JSON.stringify({ error: errorMessage }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' }
    });
  }
});
